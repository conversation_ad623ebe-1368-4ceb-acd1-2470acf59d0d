package com.nimble.irisservices.dao.impl;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.ICompanyDao;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dao.IGroupDaos;
import com.nimble.irisservices.dao.IReportDao;
import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.GOverview;
import com.nimble.irisservices.dto.InvalidPacket;
import com.nimble.irisservices.dto.JAlertRangeType;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JGatewaySensorType;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JMeariNotification;
import com.nimble.irisservices.dto.JPacketReport;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.dto.MeidData;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.AllSubscription;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetGroup;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.DataP;
import com.nimble.irisservices.entity.FurbitLastGatewayReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayCredits;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.exception.InvalidSubgroupIdException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMonitorTypeService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IWifiInfoService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.*;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

@Repository
public class GatewayDaoImpl implements IGatewayDao {

	private static final Logger log = LogManager.getLogger(GatewayDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	@Lazy
	private SessionFactory slave2SessionFactory;

	@Autowired
	@Lazy
	private SessionFactory slave3SessionFactory;

	@Autowired
	private SessionFactory slave4SessionFactory;

	@Autowired
	private SessionFactory slave5SessionFactory;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	ICompanyDao companyDao;

	@Autowired
	@Lazy
	IReportDao reportDao;

	@Autowired
	@Lazy
	IGroupDaos groupdao;

	@Autowired
	@Lazy
	IAlertService alertService;

	@Autowired
	@Lazy
	IAlertCfgService alertcfgService;

	@Autowired
	@Lazy
	IDynamicCmdService dynamiccmdService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;

	@Autowired
	IMonitorTypeService monitorTypeService;

	@Autowired
	@Lazy
	private IWifiInfoService wifiService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	IrisservicesUtil irisUtil;

	@Autowired
	Helper _helper;

	@Autowired
	@Lazy
	IChargebeeService chargebeeService;

	@Value("${default_temp_range_offset}")
	private String default_temp_range_offset = "0";

	public List<JGateway> getJGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		log.info(" Entered GatewayDao :: getJGateway ");
		List<JGateway> jGateways = new ArrayList<JGateway>();
		try {
			List<Gateway> gateways = this.getGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);

			for (Gateway g : gateways) {
				JGateway jg = this.convertGatewayintoJGateway(g);
				jGateways.add(jg);
			}
			return jGateways;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<JGatewayConfig> getJGatewayConfig(String gatewayid, long userid, String tempunit) {
		List<JGatewayConfig> jcfgList = new ArrayList<JGatewayConfig>();
		try {
			String subqry = "SELECT gatewayid FROM usergateway WHERE userid=" + userid + "";

			if (gatewayid.isEmpty())
				subqry = "SELECT gatewayid FROM usergateway WHERE userid=" + userid + "";
			else
				subqry = gatewayid;

			String qry = "SELECT g.id AS assetid, g.onoffstatus AS onOffStatus ,g.gatewayconfig AS gatewayConfig,g.temp_calib, am.is_temp_calib, tc.status,tc.new_calib,tc.battery_offset_status,tc.charging_offset_status,tc.fullcharge_offset_status,am.is_pl_threshold,g.pl_threshold,plt.status AS plt_status, g.temp_range_offset FROM gateway g JOIN assetmodel am ON g.model_id = am.id LEFT JOIN temp_calib_status tc ON g.id = tc.gateway_id LEFT JOIN pl_threshold_status plt ON g.id = plt.gateway_id WHERE g.id IN ("
					+ subqry + ");";

//            jcfgList  = this.sessionFactory.getCurrentSession().createSQLQuery(qry).
//            		setResultTransformer(new org.hibernate.transform.AliasToBeanResultTransformer(JGatewayConfig.class)).list();									  

			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			if (!res.isEmpty()) {
				long assetid = 0;
				boolean onOffStatus = false;
				String gatewayconfig = "";
				for (Object[] gcObj : res) {

					assetid = ((BigInteger) gcObj[0]).longValue();

					onOffStatus = (boolean) gcObj[1];

					gatewayconfig = (String) (gcObj[2]);

					JGatewayConfig jGC = new JGatewayConfig(assetid, onOffStatus, gatewayconfig);

					// In temp calib, we should not use normal celcius to fahrenheit conversion.
					// Instead of that multiply celcius into 2.
					if (gcObj[3] != null) {
						if (tempunit.equalsIgnoreCase("F"))
							jGC.setTemp_calib((float) (gcObj[3]) * 2);
						else
							jGC.setTemp_calib((float) (gcObj[3]));
					}

					if (gcObj[4] != null)
						jGC.setTemp_calib_enable((boolean) gcObj[4]);

					if (gcObj[6] != null && gcObj[3] != null && gcObj[5] != null) {
						if ((((String) (gcObj[5])).equalsIgnoreCase("inprogress")
								&& ((String) (gcObj[7])).equalsIgnoreCase("inprogress")
								&& ((String) (gcObj[8])).equalsIgnoreCase("inprogress")
								&& ((String) (gcObj[9])).equalsIgnoreCase("inprogress"))
								&& (float) (gcObj[6]) != (float) (gcObj[3]))
							jGC.setShow_updating_calib(true);
						else
							jGC.setShow_updating_calib(false);
					}

					if (gcObj[10] != null)
						jGC.setPl_threshold_enable((boolean) gcObj[10]);
					if (gcObj[11] != null)
						jGC.setPl_threshold((int) gcObj[11]);
					if (gcObj[12] != null && ((String) gcObj[12]).equalsIgnoreCase("inprogress"))
						jGC.setShow_updating_pl_threshold(true);
					else
						jGC.setShow_updating_pl_threshold(false);
					if (gcObj[13] != null) {
						if (tempunit.equalsIgnoreCase("F")) {
							int offsetFer = Math.round((int) gcObj[13] * 1.8F);
							jGC.setTemp_range_Offset(offsetFer);
						} else
							jGC.setTemp_range_Offset((int) gcObj[13]);
					}
					jcfgList.add(jGC);

				}

			}
		} catch (Exception e) {
			log.error("Error in getJGatewayConfig :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return jcfgList;
		}
		return jcfgList;
	}

	public List<Gateway> getGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		log.info(" Entered GatewayDao :: getGateway ");
		String qry = "select G from Gateway G, IN (G.users) user where user.id ='" + userid + "' ";
		if (gatewayid != null && !(gatewayid.isEmpty()))
			qry = qry + " and G.id = '" + gatewayid + "'";
		/*
		 * else if(!subgroupid.isEmpty()) qry =
		 * qry+" and  G.subgroup.id = '"+subgroupid+"'";
		 */
		else if (groupid != null && !(groupid.isEmpty()))
			qry = qry + " and G.groups.id = '" + groupid + "'";
		else if (assetgroupid != null && !(assetgroupid.isEmpty()))
			qry = qry + " and G.assetgroup.id = '" + assetgroupid + "'";
		else if (meid != null && !(meid.isEmpty()))
			qry = qry + " and G.meid = '" + meid + "'";

		try {
			// System.out.println("get gateways in dao gatewayid: "+gatewayid);
			// System.out.println("query "+qry);
			List<Gateway> gateways = this.slave3SessionFactory.getCurrentSession().createQuery(qry).list();
			return gateways;
		} catch (Exception e) {
			log.error(
					"Error in getGateway :: Session Name : slave3SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	public List<Gateway> getGatewayByMonitorType(String monitortype, long userid) {
		log.info(" Entered GatewayDao :: getGatewayByMonitorType ");
		String qry = "SELECT g.* FROM gateway g JOIN  assetmodel a ON g.model_id = a.id JOIN usergateway ug ON g.id = ug.gatewayid\r\n"
				+ " WHERE a.monitor_type_id in('" + monitortype + "') and ug.userid=" + userid + " ORDER BY g.id";

		try {
			List<Gateway> gateways = this.sessionFactory.getCurrentSession().createSQLQuery(qry)
					.addEntity(Gateway.class).list();
			return gateways;
		} catch (Exception e) {
			log.error("exception catched while getting gateway by monitor type");
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	// public List<JGateway> getGatewayByMonitor(String monitortype, long userid) {
	//
	// String qry = "SELECT g.* FROM gateway g JOIN assetmodel a ON g.model_id =
	// a.id JOIN usergateway ug ON g.id = ug.gatewayid\r\n"
	// + " WHERE a.monitor_type_id in('" + monitortype + "') and ug.userid=" +
	// userid+" ORDER BY g.id";
	// List<JGateway> jGateways = new ArrayList<JGateway>();
	// try {
	// System.out.println("query " + qry);
	// List<Gateway> gateways =
	// this.sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(Gateway.class).list();
	//
	// for (Gateway g : gateways) {
	// JGateway jg = this.convertGatewayintoJGateway(g);
	// jGateways.add(jg);
	// }
	//
	// return jGateways;
	// } catch (Exception e) {
	// System.out.println("exception catched while getting gateway by monitor
	// type");
	// e.printStackTrace();
	// return null;
	// }
	// }

	public JGateway convertGatewayintoJGateway(Gateway g) {
		log.info(" Entered GatewayDao :: convertGatewayintoJGateway");
		long agrpid = 0;
		String agrpname = null;
		AssetGroup agrp = g.getAssetgroup();
		if (agrp != null) {
			agrpid = agrp.getId();
			agrpname = agrp.getName();
		}
		AssetModel assetModel = g.getModel();
		MonitorType monitorType = assetModel.getMonitor_type();
		boolean isN12_5 = assetModel.getInventorymodelname().contains("N12.5");

        String monitorTypeName = monitorType.getName();
		long monitorTypeId = monitorType.getId();

		// SubGroup sgrp = g.getSubgroup();
		// Group grp = g.getSubgroup().getGroup();

		String lastrptdatetime = null;
		if (g.getLastrptdatetime() != null)
			lastrptdatetime = changeDateFormat(g.getLastrptdatetime());

		Groups grp = g.getGroups();
		String datetime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(g.getInstalled_date());

		boolean fotaAvailable = gatewayService.checkFotaVersionAvailable(g.getId(), g.getMeid());

		JGateway jg = new JGateway(g.getId(), g.getName(), g.getMeid(), g.getMdn(), g.getCarrier(), g.isEnable(),
				g.isAlive(), g.getLocation(), g.getDescription(), g.getSensorenable(), g.getOwner(),
				g.getModel().getId(), g.getModel().getModel(), agrpid, agrpname, grp.getId(), grp.getName(),
				lastrptdatetime, g.isStopreport(), g.getStarttime(), g.getStoptime(), g.getPasswordtype().getId(),
				datetime, g.getQrcode(), g.getGatewayConfig(), g.isOnOffStatus(), g.getOnSleepTime(),
				g.getOffSleepTime(), monitorTypeId, monitorTypeName, g.getDefault_goal(), g.getCalories_goal(),
				g.getMacid(), g.isShowOrderId(), g.isPurchased_from_others(), g.getDnrInterval());

		jg.setUpdatePasscode(g.getUpdatePasscode());
		jg.setBleUpdateStatus(g.isBleUpdateStatus());
		jg.setN12_5(isN12_5);
		jg.setFotaAvailable(fotaAvailable);
		jg.setMeari_update_popup(g.isMeari_update_popup());

		return jg;
	}

	public List<JSGateway> getJSGateways(String cmptype_id) {
		log.info(" Entered GatewayDao :: getJSGateways ");
		List<JSGateway> jGateways = new ArrayList<JSGateway>();
		try {
			List<Gateway> gateways = this.getGateways(cmptype_id);
			for (Gateway g : gateways) {
				// System.out.println(g.getUsers().size());
				JSGateway jg = this.convertGatewayintoJSGateway(g);
				jGateways.add(jg);
			}
			return jGateways;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public List<Gateway> getGateways(String cmptype_id) {
		log.info(" Entered GatewayDao :: getGateways ");
		String qry = "select G from Gateway G ";

		if (!cmptype_id.isEmpty())
			qry = qry + " where G.company.companytype.id= '" + cmptype_id + "'";

		try {
			// System.out.println("query "+qry);
			List<Gateway> gateways = this.sessionFactory.getCurrentSession().createQuery(qry).list();
			return gateways;
		} catch (Exception e) {
			log.error("exception catched while getting gateway" + e.getLocalizedMessage());
			return null;
		}
	}

	public JSGateway convertGatewayintoJSGateway(Gateway g) {
		log.info(" Entered GatewayDao :: convertGatewayintoJSGateway ");
		long agrpid = 0;
		String agrpname = null;
		AssetGroup agrp = g.getAssetgroup();
		if (agrp != null) {
			agrpid = agrp.getId();
			agrpname = agrp.getName();
		}

		String lastrptdatetime = null;
		if (g.getLastrptdatetime() != null)
			lastrptdatetime = changeDateFormat(g.getLastrptdatetime());

		Groups grp = g.getGroups();

		JSGateway jg = new JSGateway(g.getId(), g.getName(), g.getMeid(), g.getMdn(), g.getCarrier(), g.isEnable(),
				g.isAlive(), g.getLocation(), g.getDescription(), g.getSensorenable(), g.getOwner(),
				g.getModel().getId(), g.getModel().getModel(), agrpid, agrpname, grp.getId(), grp.getName(),
				lastrptdatetime, g.isStopreport(), g.getStarttime(), g.getStoptime(), g.getUsers(),
				g.getCompany().getId(), g.getCompany().getName(), g.getCompany().getCompanytype(),
				g.getPasswordtype().getName(), g.getModel().getMonitor_type().getName(),
				g.getModel().getMonitor_type().getId(), g.getDefault_goal(), g.getCalories_goal());
		return jg;
	}

	public List<GOverview> getGatewayCount(String groupid, long userid) {
		log.info(" Entered GatewayDao :: getGatewayCount ");
		String qry = "select G.alive,count(*) from Gateway G, IN (G.users) user where user.id = '" + userid + "'";

		if (!groupid.isEmpty())
			qry = qry + " and G.groups.id = '" + groupid + "'";
		try {
			// System.out.println(qry);
			List<Object[]> objarr = (List<Object[]>) this.sessionFactory.getCurrentSession()
					.createQuery(qry + " group by G.alive").list();
			return ObjectArrayToGOverview(objarr);
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public List<GOverview> getGatewayCountTest(List<Long> groupid, long userid) {
		log.info(" Entered GatewayDao :: getGatewayCountTest ");
		String qry = "select G.alive,count(*) from Gateway G, IN (G.users) user where user.id = '" + userid + "'";

		long count = 0;
		if (!groupid.isEmpty())
			qry = qry + " and G.groups.id IN (:ids)";

		try {
			List<GOverview> gatewayOverviews = new ArrayList<GOverview>();
			List<Object[]> objarr = new ArrayList<Object[]>();

			if (!groupid.isEmpty())
				objarr = (List<Object[]>) this.sessionFactory.getCurrentSession().createQuery(qry + " group by G.alive")
						.setParameterList("ids", groupid).list();
			else
				objarr = (List<Object[]>) this.sessionFactory.getCurrentSession().createQuery(qry + " group by G.alive")
						.list();

			return ObjectArrayToGOverview(objarr);
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public Gateway saveORupdateGateway(JGateway jgateway, long cmpid) throws ConstraintViolationException,
			InvalidAssetGroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException,
			DataIntegrityViolationException, InvalidSubgroupIdException {
		log.info(" Entered GatewayDao :: saveORupdateGateway :"+jgateway.getId());
		Gateway gateway = this.convetJGatewaytoGateway(jgateway, cmpid);
		Gateway gatewayPrev = null;
		boolean updateGateway = false;
		if (jgateway.getId() != 0) {
			updateGateway = true;
			gatewayPrev = getGateway(jgateway.getId());
			gateway.saveDatap(getDataP(gatewayPrev.giveDatap().getId()));
			gateway.setMinTemp(gatewayPrev.getMinTemp());
			gateway.setMaxTemp(gatewayPrev.getMaxTemp());
			gateway.setAlive(gatewayPrev.isAlive());
			gateway.setOnOffStatus(gatewayPrev.isOnOffStatus());
			gateway.setAssetinformation(gatewayPrev.getAssetinformation());
			gateway.setInstalled_date(gatewayPrev.getInstalled_date());
			gateway.setTemp_calib(gatewayPrev.getTemp_calib());
			gateway.setDefault_temp_calib(gatewayPrev.getDefault_temp_calib());
			gateway.setDefault_battery_offset(gatewayPrev.getDefault_battery_offset());
			gateway.setDefault_charging_offset(gatewayPrev.getDefault_charging_offset());
			gateway.setDefault_fullcharge_offset(gatewayPrev.getDefault_fullcharge_offset());
			gateway.setPl_threshold(gatewayPrev.getPl_threshold());
			gateway.setTemp_range_offset(gatewayPrev.getTemp_range_offset());
			gateway.setOrder_channel(gatewayPrev.getOrder_channel());
			gateway.setSim_vendor(gatewayPrev.getSim_vendor());
			gateway.setIswithoutsub(gatewayPrev.isIswithoutsub());
			gateway.setIsnewverdevice(gatewayPrev.isIsnewverdevice());
			if (jgateway.isWebapp() == false) {
				gateway.setStopreport(gatewayPrev.isStopreport());
				gateway.setStarttime(gatewayPrev.getStarttime());
				gateway.setStoptime(gatewayPrev.getStoptime());
			}
			if(gateway.getSim_vendor().equals("NA")) {
				gateway.setSim_vendor(jgateway.getSim_vendor());
			}
		} else if (jgateway.getId() == 0) {
			gateway.setInstalled_date(Timestamp.valueOf(DynamicCmdDaoImpl.getUtcDateTime()));
			gateway.saveDatap(getDataP(jgateway.getPasswordtype()));
			gateway.setOnOffStatus(true);
			gateway.setDefault_temp_calib(gateway.getModel().getTemp_calib());
			gateway.setDefault_battery_offset(gateway.getModel().getBattery_offset());
			gateway.setDefault_charging_offset(gateway.getModel().getCharging_offset());
			gateway.setDefault_fullcharge_offset(gateway.getModel().getFullcharge_offset());
			gateway.setPl_threshold(gateway.getModel().getDefault_pl_threshold());
			gateway.setTemp_range_offset(Integer.parseInt(default_temp_range_offset));
			gateway.setOrder_channel(jgateway.getOrder_channel());
			gateway.setSim_vendor(jgateway.getSim_vendor());
			gateway.setIswithoutsub(jgateway.isIswithoutsub());
			gateway.setIsnewverdevice(jgateway.isIsnewverdevice());
		}

		/*
		 * webapp send default(NA) values in start & stoptime.Once webapp changed
		 * default values(0). this code is not need
		 */

		String starttime = gateway.getStarttime().trim();
		String stoptime = gateway.getStoptime().trim();
		if (starttime.equalsIgnoreCase("NA"))
			gateway.setStarttime("0");
		if (stoptime.equalsIgnoreCase("NA"))
			gateway.setStoptime("0");

		try {
			gateway.setShowOrderId(jgateway.isShowOrderId());

			String assetGroupId = gateway.getAssetgroup() == null ? null : "'" + gateway.getAssetgroup().getId() + "'";
			String assetInformationId = gateway.getAssetinformation() == null ? null
					: "'" + gateway.getAssetinformation().getId() + "'";
			String extsensortype = gateway.getExtsensortype() == null ? null : "'" + gateway.getExtsensortype() + "'";
			String lastrptdatetime = gateway.getLastrptdatetime() == null ? null
					: "'" + gateway.getLastrptdatetime() + "'";
			String location = gateway.getLocation() == null ? null : "'" + gateway.getLocation() + "'";
			String owner = gateway.getOwner() == null ? null : "'" + gateway.getOwner() + "'";
			String qry = "";
				boolean alive = gateway.isAlive();
				if(!gateway.isEnable())
					alive = false;
			if (updateGateway) {
				qry = " UPDATE gateway SET isalive=" + alive + ", asset_id='"
						+ gateway.giveAsset().getId() + "', assetgroup_id=" + assetGroupId + ", assetinfo_id="
						+ assetInformationId + "," + "calories_goal='" + gateway.getCalories_goal() + "', carrier='"
						+ gateway.getCarrier() + "', cmp_id='" + cmpid + "', default_goal='" + gateway.getDefault_goal()
						+ "', description='" + gateway.getDescription() + "', isenable=" + gateway.isEnable()
						+ ",extsensortype=" + extsensortype + "," + "gatewayconfig='" + gateway.getGatewayConfig()
						+ "', groups_id='" + gateway.getGroups().getId() + "', installed_date='"
						+ gateway.getInstalled_date() + "',lastrptdatetime=" + lastrptdatetime + ",location=" + location
						+ "," + "macid='" + gateway.getMacid() + "', maxval='" + gateway.getMaxTemp() + "', mdn='"
						+ gateway.getMdn() + "', meid='" + gateway.getMeid() + "', minval='" + gateway.getMinTemp()
						+ "', model_id='" + gateway.getModel().getId() + "', name='" + gateway.getName()
						+ "', offsleeptime='" + gateway.getOffSleepTime() + "', onoffstatus=" + gateway.isOnOffStatus()
						+ ", onsleeptime='" + gateway.getOnSleepTime() + "', owner=" + owner + "," + "passwordtype='"
						+ gateway.getPasswordtype().getId() + "', qrcode='" + gateway.getQrcode() + "', sensorenable='"
						+ gateway.getSensorenable() + "', show_order_id=" + gateway.isShowOrderId() + ", starttime='"
						+ gateway.getStarttime() + "', stopreport=" + gateway.isStopreport() + ", stoptime='"
						+ gateway.getStoptime() + "', default_temp_calib='" + gateway.getDefault_temp_calib()
						+ "', default_battery_offset = '" + gateway.getDefault_battery_offset()
						+ "', default_charging_offset = '" + gateway.getDefault_charging_offset()
						+ "', default_fullcharge_offset = '" + gateway.getDefault_fullcharge_offset()
						+ "', temp_calib = '" + gateway.getTemp_calib() + "', pl_threshold='"
						+ gateway.getPl_threshold() + "', temp_range_offset = '" + gateway.getTemp_range_offset()
						+ "', sensor_type_id="+ jgateway.getSensor_type_id()
						+ ", sensor_location_type_id="+ jgateway.getSensor_location_type_id()
						+ ", order_channel = '" + gateway.getOrder_channel()
						+ "', sim_vendor = '" + gateway.getSim_vendor()
						+ "', dnr_interval = " + gateway.getDnrInterval()
						+ ", iswithoutsub = " + gateway.isIswithoutsub()
						+ ", isnewverdevice = " + gateway.isIsnewverdevice()
						+ " WHERE id =" + gateway.getId();
			} else {
				qry = "INSERT INTO `gateway`(`isalive`,`asset_id`,`assetgroup_id`,`assetinfo_id`,`calories_goal`,`carrier`,`cmp_id`,`default_goal`,`description`,`isenable`,`extsensortype`,"
						+ "`gatewayconfig`,`groups_id`,`installed_date`,`lastrptdatetime`,`location`,`macid`,`maxval`,`mdn`,`meid`,`minval`,`model_id`,`name`,`offsleeptime`,`onoffstatus`,"
						+ "`onsleeptime`,`owner`,`passwordtype`,`qrcode`,`sensorenable`,`show_order_id`,`starttime`,`stopreport`,`stoptime`,`id`,`default_temp_calib`, `default_battery_offset`,"
						+ " `default_charging_offset`, `default_fullcharge_offset`, `pl_threshold`,temp_range_offset, firmware_ver, sensor_type_id, sensor_location_type_id,order_channel,sim_vendor, dnr_interval, iswithoutsub,isnewverdevice) "
						+ "values" + "(" + alive + ", '" + gateway.giveAsset().getId() + "', "
						+ assetGroupId + "," + assetInformationId + "," + "'" + gateway.getCalories_goal() + "', '"
						+ gateway.getCarrier() + "', '" + cmpid + "', '" + gateway.getDefault_goal() + "', '"
						+ gateway.getDescription() + "', " + gateway.isEnable() + "," + extsensortype + "," + "'"
						+ gateway.getGatewayConfig() + "', '" + gateway.getGroups().getId() + "', '"
						+ gateway.getInstalled_date() + "'," + lastrptdatetime + "," + location + "," + "'"
						+ gateway.getMacid() + "', '" + gateway.getMaxTemp() + "', '" + gateway.getMdn() + "', '"
						+ gateway.getMeid() + "', '" + gateway.getMinTemp() + "', '" + gateway.getModel().getId()
						+ "', '" + gateway.getName() + "', '" + gateway.getOffSleepTime() + "', "
						+ gateway.isOnOffStatus() + ", '" + gateway.getOnSleepTime() + "', " + owner + "," + "'"
						+ gateway.getPasswordtype().getId() + "', '" + gateway.getQrcode() + "', '"
						+ gateway.getSensorenable() + "', " + gateway.isShowOrderId() + ", '" + gateway.getStarttime()
						+ "', " + gateway.isStopreport() + ", '" + gateway.getStoptime() + "', '"
						+ gateway.giveAsset().getId() + "', '" + gateway.getModel().getTemp_calib() + "', '"
						+ gateway.getModel().getBattery_offset() + "', '" + gateway.getModel().getCharging_offset()
						+ "', '" + gateway.getModel().getFullcharge_offset() + "', '"
						+ gateway.getModel().getDefault_pl_threshold() + "','" + gateway.getTemp_range_offset() + "','"
						+ jgateway.getFota_version() + "',"+ jgateway.getSensor_type_id() +","+ jgateway.getSensor_location_type_id() 
						+ ","+jgateway.getOrder_channel() + ",'"+ jgateway.getSim_vendor() +"', " + gateway.getDnrInterval() + "," +gateway.isIswithoutsub()+","+gateway.isIsnewverdevice()+")";
			}

			int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("save or update gateway status : " + status);
//			sessionFactory.getCurrentSession().saveOrUpdate(gatewayV2);
			saveDynamiccmd(gateway, gatewayPrev, jgateway.getId());
		} catch (Exception e) {
			log.error("Error in saveORupdateGateway :: Error : " + e.getLocalizedMessage());
			return null;
		}

		return gateway;
	}

	public GatewayCredits updateGatewayCredit(long gateway_id, long cmp_id) {
		log.info(" Entered GatewayDao :: updateGatewayCredit ");
		long credit = companyDao.getCompany(cmp_id).getThrotsettings().getCredits();
		long extracredits = companyDao.getCompany(cmp_id).getThrotsettings().getExtra_credits();

		GatewayCredits gatewaycredit = new GatewayCredits(gateway_id, cmp_id, 0, credit,
				Timestamp.valueOf(getNextMonthUTCdate()), 1, 0, extracredits);

		sessionFactory.getCurrentSession().saveOrUpdate(gatewaycredit);
		return gatewaycredit;
	}

	public String getNextMonthUTCdate() {
		log.info(" Entered GatewayDao :: getNextMonthUTCdate ");
		String futureUtcDateTime = null;

		Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getTimeZone("UTC"));

		cal.add(Calendar.MONTH, 1);

		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
		futureUtcDateTime = dateFormat.format(cal.getTime());
		// System.out.println("Next month date in req. format= " +
		// futureUtcDateTime);

		return futureUtcDateTime;
	}

	public boolean updateGatewayCreditPoints(ThrottlingSettings throttleSettings) {
		log.info(" Entered GatewayDao :: updateGatewayCreditPoints ");
		long credit = throttleSettings.getCredits();
		long extra_credit = throttleSettings.getExtra_credits();
		long total = credit + extra_credit;

		String qry = "UPDATE gatewaycredits GC INNER JOIN company cmp ON cmp.id =  GC.cmp_id INNER JOIN throttlingsettings TS ON TS.id = cmp.throttsettings_id "
				+ "SET GC.creditsAssigned = " + total + " WHERE TS.id = " + throttleSettings.getId() + " ;";

		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	private void saveDynamiccmd(Gateway gateway, Gateway gatewayPrev, long jgateid) {
		log.info(" Entered GatewayDao :: saveDynamiccmd ");
		/* Gateway Updated use this if condition */
		if (jgateid != 0) {
			if (gateway.isStopreport()) {
				if (!gateway.getStarttime().equalsIgnoreCase(gatewayPrev.getStarttime())
						|| !gateway.getStoptime().equalsIgnoreCase(gatewayPrev.getStoptime())) {
					if (!gateway.getStarttime().equalsIgnoreCase(gateway.getStoptime()))
						saveStopReportTime(gateway.getStarttime().trim(), gateway.getStoptime().trim(), gateway);
					else if (!gatewayPrev.getStarttime().equalsIgnoreCase(gatewayPrev.getStoptime()))
						saveStartReport(gateway);
				}
			} else if (gateway.isStopreport() != gatewayPrev.isStopreport()) {
				if (!gateway.getStarttime().equalsIgnoreCase(gatewayPrev.getStarttime())
						|| !gateway.getStoptime().equalsIgnoreCase(gatewayPrev.getStoptime())) {
					saveStartReport(gateway);
				}
			}
		}
		/* Gateway Creation use this else if condition */
		else if (jgateid == 0 && gateway.isStopreport())
			saveStopReportTime(gateway.getStarttime().trim(), gateway.getStoptime().trim(), gateway);
	}

	public void saveStartReport(Gateway gateway) {
		log.info(" Entered GatewayDao :: saveStartReport ");
		String msg = "startreport=0";
		dynamiccmdService.saveDynamicCmd(gateway, msg, 1, "notsent");
	}

	public void saveStopReportTime(String starttim, String stoptim, Gateway gateway) {
		log.info(" Entered GatewayDao :: saveStopReportTime ");
		int starttime = Integer.parseInt(starttim);
		int stoptime = Integer.parseInt(stoptim);

		if (stoptime < starttime) {
			int duration = (starttime - stoptime) * 3600;
			String msg = "stopreport=" + stoptime + "-" + duration;
			dynamiccmdService.saveDynamicCmd(gateway, msg, 1, "notsent");
		} else if (stoptime > starttime) {
			int duration = ((24 - stoptime) + starttime) * 3600;
			String msg = "stopreport=" + stoptime + "-" + duration;
			dynamiccmdService.saveDynamicCmd(gateway, msg, 1, "notsent");
		}
	}

	public void saveAsset(Asset asset) throws InvalidAsseIdException {
		log.info("Entered into saveAsset assetaddr : " + asset.getAssetaddress());
		try {
			String qry = "insert into asset(assetaddr, assettype, model_id) " + "values('" + asset.getAssetaddress()
					+ "', '" + asset.getAssettype() + "', '" + asset.getModel().getId() + "');";
			int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("saved asset status : " + status);
//			sessionFactory.getCurrentSession().save(asset);
		} catch (Exception e) {
			throw new InvalidAsseIdException();
		}
	}

	public void updateAsset(Asset asset) throws InvalidAsseIdException {
		log.info("Entered into updateAsset");
		try {
			sessionFactory.getCurrentSession().update(asset);
		} catch (Exception e) {
			throw new InvalidAsseIdException();
		}
	}

	public Asset getAssetById(long id) {
		Session session = sessionFactory.getCurrentSession();
		Criteria criteria = session.createCriteria(Asset.class).add(Restrictions.eq("id", id));
		Asset asset = (Asset) criteria.list().get(0);
		return asset;
	}

	public Asset getAssetByAddr(String addr) {
		Session session = sessionFactory.getCurrentSession();
		Criteria criteria = session.createCriteria(Asset.class).add(Restrictions.eq("assetaddress", addr));
		Asset asset = (Asset) criteria.list().get(0);
		return asset;
	}

	@Override
	public AssetModel getAssetModel(long id) {
		log.info(" Entered GatewayDao :: getAssetModel ");
		Session session = sessionFactory.getCurrentSession();
		Criteria criteria = session.createCriteria(AssetModel.class).add(Restrictions.eq("id", id));
		AssetModel assetModel = (AssetModel) criteria.list().get(0);
		return assetModel;
	}

	public Gateway convetJGatewaytoGateway(JGateway jg, long cmpid) throws InvalidAssetGroupIdException,
			InvalidSubgroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException {
		log.info(" Entered GatewayDao :: convetJGatewaytoGateway ");
		AssetGroup assetgroup = new AssetGroup();
		// SubGroup subgroup = new SubGroup();
		AssetModel model = new AssetModel();
		Groups groups = new Groups();
		Asset asset = null;

		// Asset asset
		try {
			if (jg.getAssetgroupid() == 0)
				assetgroup = null;
			else
				assetgroup = groupservices.getAssetGroup(Long.toString(jg.getAssetgroupid()), cmpid).get(0);
		} catch (IndexOutOfBoundsException e) {

			throw new InvalidAssetGroupIdException();
		}
		try {
			// subgroup =
			// groupservices.getSubGroup("",Long.toString(jg.getSubgroupid()),
			// cmpid).get(0);

			List<Groups> grpLis = (List<Groups>) groupdao.getGroupsByLevel(Long.toString(jg.getGroupid()), "", cmpid,
					"Groups");
			if(!grpLis.isEmpty()) {
				groups = grpLis.get(0);
			}
			// groups =
			// groupservices.getGroups(Long.toString(jg.getGroupid()),"", "1",
			// cmpid).get(0); /*Gateway assigns only level 1*/
		} catch (IndexOutOfBoundsException e) {

			throw new InvalidGroupIdException();
		}
		try {
			model = this.getAssetModel(jg.getModelid());
			// MonitorType monitorType = monitorTypeService.getMonitorTypeById((int)
			// jg.getMonitorTypeId());
			// model.setMonitor_type(monitorType);

		} catch (IndexOutOfBoundsException e) {

			throw new InvalidModelIdException();
		}
		try {
			asset = getAssetById(jg.getId());
			if (!asset.getAssettype().equals("G"))
				throw new InvalidAsseIdException();
			asset.setAssetaddress(jg.getMeid());
			asset.setModel(model);
			this.gatewayService.updateAsset(asset);
		} catch (IndexOutOfBoundsException ne) {
			log.error("no asset found");
			Asset sasset = new Asset("G", jg.getMeid(), model);
			this.saveAsset(sasset);
			asset = this.getAssetByAddr(jg.getMeid());

		}
		Company company = companyDao.getCompany(cmpid); // cmpid
		Timestamp lastrptdatetime = null;
		int dnrInterval = jg.getDnrInterval() == 0 ? model.getDnrInterval() : jg.getDnrInterval();

		Gateway gateway = new Gateway(asset.getId(), jg.getName(), jg.getMeid(), jg.getMdn(), jg.getCarrier(),
				jg.isEnable(), false, jg.getLocation(), jg.getDescription(), jg.getSensorEnable(), jg.getOwner(), asset,
				assetgroup, groups, model, company, lastrptdatetime, jg.isStopreport(), jg.getStarttime(),
				jg.getStoptime(), jg.getQrcode(), jg.getGatewayConfig(), jg.getOnSleepTime(), jg.getOffSleepTime(),
				jg.getDefault_goal(), jg.getCalories_goal(), jg.getMacid(), jg.getSensor_type_id(), dnrInterval, jg.isIswithoutsub(),jg.isIsnewverdevice());

		return gateway;
	}

	public List<Asset> getAssets(Long[] assetids) throws InvalidAsseIdException {
		try {
			log.info(" Entered GatewayDao :: getAssets ");
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(Asset.class).add(Restrictions.in("id", assetids));
			List<Asset> assets = criteria.list();

			return assets;
		} catch (NullPointerException e) {
			throw new InvalidAsseIdException();
		} catch (Exception e) {
			throw new InvalidAsseIdException();

		}
	}

	@SuppressWarnings("unchecked")
	public List<Gateway> getGatewayByIds(Set<String> gatIds) {
		log.info(" Entered GatewayDao :: getGatewayByIds ");
		List<Long> gatewayid = new ArrayList<Long>();
		for (String gatId : gatIds) {
			gatewayid.add(Long.parseLong(gatId));
		}
		Long[] gatewayids = gatewayid.toArray(new Long[gatewayid.size()]);
		List<Gateway> gateways = this.sessionFactory.getCurrentSession()
				.createQuery("from Gateway where id in (:gatIds)").setParameter("gatIds", gatewayids).list();

		return gateways;

	}

	public long getSGatewayCount(String groupid, long userid, int rptStatus, String deliqStatus, int IsInbuilt) {
		log.info(" Entered GatewayDao :: getSGatewayCount ");
		String qry = "select count(*) from Gateway G, IN (G.users) user where user.id = '" + userid + "'"
				+ " and G.assetinformation.field_7 LIKE '%" + deliqStatus + "%'";

		if (!groupid.isEmpty())
			qry = qry + " and G.groups.id = '" + groupid + "'";

		if (rptStatus >= 0)
			qry = qry + " and G.alive='" + rptStatus + "'";

		if (IsInbuilt >= 0)
			qry = qry + " and G.assetinformation.field_9 ='" + IsInbuilt + "'";

		try {
			// "select count(*) from Gateway where subgroup.id =
			// '"+subgroupid+"'"
			long count = (Long) this.sessionFactory.getCurrentSession().createQuery(qry).uniqueResult();
			return count;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return -1;
		}
	}

	public long getSGatewayCount(List<Long> groupid, long userid, int rptStatus, String deliqStatus, int IsInbuilt) {
		log.info(" Entered GatewayDao :: getSGatewayCount ");
		String qry = "select count(*) from Gateway G, IN (G.users) user where user.id = '" + userid + "'"
				+ " and G.assetinformation.field_7 LIKE '%" + deliqStatus + "%'";

		if (!groupid.isEmpty())
			qry = qry + " and G.groups.id IN (:ids)";

		if (rptStatus >= 0)
			qry = qry + " and G.alive='" + rptStatus + "'";

		if (IsInbuilt >= 0)
			qry = qry + " and G.assetinformation.field_9='" + IsInbuilt + "'";

		long count = 0;

		try {
			// "select count(*) from Gateway where subgroup.id =
			// '"+subgroupid+"'"
			/* System.out.println(qry); */
			if (!groupid.isEmpty())
				count = listToLong((List<Long>) this.sessionFactory.getCurrentSession().createQuery(qry)
						.setParameterList("ids", groupid).list());
			else
				count = (Long) this.sessionFactory.getCurrentSession().createQuery(qry).uniqueResult();

			return count;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return -1;
		}
	}
	
	
	public boolean getSkipOtaStatus(String MEID) {
		log.info(" Entered GatewayDao :: getSimStatus ");
		
		boolean data = false;
		try {
			
		String qry = "SELECT enable from skipsfw_ota WHERE meid = '" + MEID + "'";
		ArrayList<Boolean> result =  (ArrayList<Boolean>) this.sessionFactory.getCurrentSession().createSQLQuery(qry).list();
		
			if(!result.isEmpty()) {//
				
				return result.get(0);
						
			}else {
				return false;
			}
			
		}catch(Exception e) {
			log.error(e.getLocalizedMessage());
		}
		
		return data;
	}
	
	private boolean byteToBoolean(Boolean boolean1) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean updateskipotastatus(String Meid, boolean status) {
		log.info(" Entered GatewayDao :: updateSimStatus ");
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT * from skipsfw_ota WHERE meid = '" + Meid + "'";
			
			ArrayList<MeidData> data = (ArrayList<MeidData>) this.sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			if(data != null && !status) {
				//delete qry....
		
				String hql = "DELETE FROM skipsfw_ota WHERE meid = :meid";
				Query query = session.createSQLQuery(hql);
				query.setParameter("meid", Meid).executeUpdate();
				
				return true;
				
			} else if(data != null && status) {
				//update qry....
		
				String hql = "update skipsfw_ota set enable=1 WHERE meid = :meid";
				Query query = session.createSQLQuery(hql);
				query.setParameter("meid", Meid).executeUpdate();
				
				return true;
				
			}else { 
					//insert qry.....
					String sql = "INSERT INTO skipsfw_ota (meid, enable) VALUES (:meid, 1)";
					Query query = session.createSQLQuery(sql);
					query.setParameter("meid", Meid);
					query.executeUpdate();

					return true;
			}

		} catch (Exception e) {
			log.error("Error in updateskipotastatus ::"
					+ e.getLocalizedMessage());
			return false;
		}
	}

	public JGatewayOverview getGatewayCount(String groupid, String subgroupid, long userid, String levelid) {
		log.info(" Entered GatewayDao :: getGatewayCount ");
		long gatewayCnt = 0, WgatewayCnt = 0, NWgatewayCnt = 0;

		List<JGroups> grpsList = null;

		if (!groupid.isEmpty() && !levelid.isEmpty())
			grpsList = reportDao.getlevelBasedGroupsList(Long.valueOf(groupid), levelid);

		if (grpsList != null) {

			List<GOverview> gatewayoverview = getGatewayCountTest(getGrpIdList(grpsList), userid);

			if (null != gatewayoverview) {
				WgatewayCnt = getcount(gatewayoverview, true);
				NWgatewayCnt = getcount(gatewayoverview, false);
				gatewayCnt = WgatewayCnt + NWgatewayCnt;
			}

		} else {
			List<GOverview> gatewayoverview = getGatewayCount(groupid, userid);

			if (null != gatewayoverview) {
				WgatewayCnt = getcount(gatewayoverview, true);
				NWgatewayCnt = getcount(gatewayoverview, false);
				gatewayCnt = WgatewayCnt + NWgatewayCnt;
			}

		}

		JGatewayOverview gatewayOverview = new JGatewayOverview(gatewayCnt, WgatewayCnt, NWgatewayCnt);
		return gatewayOverview;
	}

	public JSreiGatewayOverview getSreiGatewayCount(String groupid, String subgroupid, long userid, int rptStatus,
			String levelid, int IsInbuilt) {
		// TODO Auto-generated method stub
		log.info(" Entered GatewayDao :: getSreiGatewayCount ");
		long sgatewayGCnt = 0, sgatewayYCnt = 0, sgatewayRCnt = 0;
		List<JGroups> grpsList = null;

		if (!groupid.isEmpty() && !levelid.isEmpty())
			grpsList = reportDao.getlevelBasedGroupsList(Long.valueOf(groupid), levelid);

		if (grpsList != null) {

			sgatewayGCnt = getSGatewayCount(getGrpIdList(grpsList), userid, rptStatus, "Green", IsInbuilt);
			sgatewayYCnt = getSGatewayCount(getGrpIdList(grpsList), userid, rptStatus, "Yellow", IsInbuilt);
			sgatewayRCnt = getSGatewayCount(getGrpIdList(grpsList), userid, rptStatus, "Red", IsInbuilt);

		} else {
			sgatewayGCnt = getSGatewayCount(groupid, userid, rptStatus, "Green", IsInbuilt);
			sgatewayYCnt = getSGatewayCount(groupid, userid, rptStatus, "Yellow", IsInbuilt);
			sgatewayRCnt = getSGatewayCount(groupid, userid, rptStatus, "Red", IsInbuilt);
		}

		JSreiGatewayOverview sgateGatewayOverview = new JSreiGatewayOverview(sgatewayGCnt, sgatewayYCnt, sgatewayRCnt);
		return sgateGatewayOverview;
	}

	private long listToLong(List<Long> count) {

		long counts = 0;
		for (int i = 0; i < count.size(); i++) {
			counts += count.get(i);
		}
		return counts;

	}

	private List<Long> getGrpIdList(List<JGroups> grpsList) {
		List<Long> groupIds = new ArrayList<Long>();
		for (JGroups grpA : grpsList) {
			groupIds.add(grpA.getId());
		}
		return groupIds;
	}

	public List<Gateway> getGatewayCmpBased(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long cmpid) {
		log.info(" Entered GatewayDao :: getGatewayCmpBased ");
		String qry = "from Gateway where groups.company.id = '" + cmpid + "' ";

		if (!gatewayid.isEmpty())
			qry = qry + " and id = '" + gatewayid + "'";
		/*
		 * else if(!subgroupid.isEmpty()) qry =
		 * qry+" and  subgroup.id = '"+subgroupid+"'";
		 */
		else if (!groupid.isEmpty())
			qry = qry + " and groups.id = '" + groupid + "'";
		else if (!assetgroupid.isEmpty())
			qry = qry + " and assetgroup.id = '" + assetgroupid + "'";

		try {
			List<Gateway> gateways = this.sessionFactory.getCurrentSession().createQuery(qry).list();

			return gateways;
		} catch (Exception e) {
			log.error("exception catched while getting gateway");
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public Gateway saveORupdateGateway(Gateway gateway) {
		log.info(" Entered GatewayDao :: saveORupdateGateway ");
		boolean isSuccess = false;
		sessionFactory.getCurrentSession().saveOrUpdate(gateway);
		isSuccess = true;

		return gateway;
	}

	public Gateway getGateway(long id) {
		try {
			log.info(" Entered GatewayDao :: getGateway ");
			Session sessionNew = sessionFactory.openSession();
			try {
				Transaction tx = sessionNew.beginTransaction();
				Criteria criteria = sessionNew.createCriteria(Gateway.class).add(Restrictions.eq("id", id));
				Gateway gateway = (Gateway) criteria.list().get(0);
				tx.commit();
				return gateway;
			} finally {
				sessionNew.flush();
				sessionNew.close();
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Error in getGateway(id) :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error("Error in getGateway(id) :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	private String changeDateFormat(Timestamp myTimestamp) {
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(myTimestamp);
	}

	public List<GOverview> ObjectArrayToGOverview(List<Object[]> objarr) {
		log.info(" Entered GatewayDao :: ObjectArrayToGOverview ");
		List<GOverview> gatewayOverviews = new ArrayList<GOverview>();

		for (Object[] result : objarr) {
			gatewayOverviews.add(new GOverview((Boolean) result[0], (Long) result[1]));
		}
		return gatewayOverviews;
	}

	public int getcount(List<GOverview> gatewayoverview, boolean status) {
		log.info(" Entered GatewayDao :: gatewayoverview ");
		int count = 0;
		for (GOverview gateoverview : gatewayoverview) {
			if (gateoverview.getGStatus() == status) {
				count += gateoverview.getGCount();
			}
		}
		return count;
	}

	public String getCurrentDate(String format) {
		log.info(" Entered GatewayDao :: getCurrentDate ");
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("IST"));

		currDateCal.setTime(new Date(System.currentTimeMillis()));

		DateFormat dateFormat = new SimpleDateFormat(format);
		dateFormat.setTimeZone(TimeZone.getTimeZone("IST"));
		String dateStr = dateFormat.format(currDateCal.getTime());
		return dateStr;
	}

	public java.util.Date getDate(String dateFormat) {
		log.info(" Entered GatewayDao :: getDate ");
		SimpleDateFormat format = new SimpleDateFormat(dateFormat);
		java.util.Date parsed = null;
		try {
			parsed = format.parse(getCurrentDate(dateFormat));
		} catch (ParseException e) {
			log.error(e.getLocalizedMessage());
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return parsed;
	}

	public DataP getDataP(long id) {
		try {
			log.info(" Entered GatewayDao :: getDataP ");
			Session sessionNew = sessionFactory.openSession();
			try {
				Transaction tx = sessionNew.beginTransaction();
				Criteria criteria = sessionNew.createCriteria(DataP.class).add(Restrictions.eq("id", id));
				DataP datap = (DataP) criteria.list().get(0);
				tx.commit();
				return datap;
			} finally {
				sessionNew.flush();
				sessionNew.close();
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error(e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public Gateway getGatewayInMeid(String meid) {
		log.info(" Entered GatewayDao :: getGatewayInMeid ");
		try {

			Session sessionNew = sessionFactory.openSession();
			try {
				Transaction tx = sessionNew.beginTransaction();
				Criteria criteria = sessionNew.createCriteria(Gateway.class).add(Restrictions.eq("meid", meid));
				Gateway gateway = (Gateway) criteria.list().get(0);
				tx.commit();
				return gateway;
			} finally {
				sessionNew.flush();
				sessionNew.close();
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error(e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public JGateway gatewayExitsinDB(JGateway jgateway, long cmpid) {
		log.info(" Entered GatewayDao :: gatewayExitsinDB ");
		if (jgateway.getId() != 0)
			jgateway.setUserGatDis(false);

		try {
			Session sessionNew = sessionFactory.openSession();
			try {
				Transaction tx = sessionNew.beginTransaction();
				String qry = "select G from Gateway G, IN (G.users) user where G.meid ='" + jgateway.getMeid() + "' ";
				List<Gateway> gateways = this.sessionFactory.getCurrentSession().createQuery(qry).list();
				boolean toChnageExitGateway = false;
				Gateway gate = null;
				if (gateways.size() == 0 && jgateway.getId() == 0) {
					gate = getGatewayInMeid(jgateway.getMeid());
					if (gate != null) {
						toChnageExitGateway = true;
						jgateway.setId(gate.getId());

						if (jgateway.getOldMeid().equalsIgnoreCase("NA")) {

							updateCmpidInReport("gatewayreport", cmpid, gate.getId());
							updateCmpidInReport("lastgatewayreport", cmpid, gate.getId());
						}
					}
				}
				tx.commit();

				if (toChnageExitGateway && !jgateway.getOldMeid().equalsIgnoreCase("NA")) {
					String changedMeid = gatewayService.changeOldGatewayDetails(gate);
					if (!changedMeid.equalsIgnoreCase("NA")) {
						boolean changAsset = gatewayService.changeOldAssertDetails(gate.getId(), changedMeid);
					}
				}

			} finally {
				sessionNew.flush();
				sessionNew.close();
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Error in gatewayExitsinDB :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return jgateway;

		} catch (Exception e) {
			log.error("Error in gatewayExitsinDB :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return jgateway;
		}
		return jgateway;
	}

	public JGatewayUserDetails getGateway(String meid) {
		log.info(" Entered GatewayDao :: getGateway ");
		JGatewayUserDetails jGatewayUserDetails = null;
		try {
			String qry = "select G from Gateway G, IN (G.users) user where G.meid ='" + meid + "' ";
			List<Gateway> gateways = this.slave4SessionFactory.getCurrentSession().createQuery(qry).list();
			if (gateways.size() != 0) {
				Gateway mappedGateway = gateways.get(0);
				Set<User> users = mappedGateway.getUsers();// Fetch user details
				String username = null;
				long userid = 0;
				String pswd = null;
				String auth = null;
				for (User user : users) {
					// System.out.println("Username: "+user.getUsername());
					username = user.getUsername();
					userid = user.getId();
					pswd = user.getPassword();
					auth = user.getAuthKey();
				}
				log.info("getGateway: Gateway " + meid + " is mapped to username - " + username);

				jGatewayUserDetails = new JGatewayUserDetails(mappedGateway.getName(), username, pswd, userid,
						mappedGateway.getId(), auth);

			} else {
				log.info("getGateway: No gateway mappings exists for meid - " + meid);
			}
		} catch (Exception e) {
			log.error("Error in getGateway(meid) :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
		}

		return jGatewayUserDetails;
	}

	public boolean updateCmpidInReport(String tablename, long cmpid, long assetid) {
		log.info(" Entered GatewayDao :: updateCmpidInReport ");
		String qry = "UPDATE " + tablename + " SET cmp_id = " + cmpid + " WHERE gateway_id = " + assetid + ";";
		int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		return (resultVal == 1) ? true : false;
	}

	public boolean delGateway(User user, String assetid) {
		log.info(" Entered GatewayDao :: delGateway ");
		boolean result = false;
//		try {
//			userService.delUserGateway(user.giveCompany().getId(), assetid);
//
//			userService.updateMeidInOrderMap(user.getId() + "",
//					gatewayService.getGateway(Long.parseLong(assetid)).getMeid());
//
//			alertService.delAlert(user.getId(), assetid);
//			alertcfgService.delAlertcfg(user.getId(), assetid);
//
//			// update gateway name
//			gatewayService.updateGatewayName(assetid);
//
//			// included : delete - last powersavemode, powersavemode history details.
//			dynamiccmdService.delDynamicCmd(user.getId(), assetid);
//			gatewayService.updatepetprofile(assetid);
//
//			// new : delete wifi info for gateway.
//			wifiService.deleteWifiInfoForGateway(user.getId() + "", assetid);
//
//			Gateway gateway = gatewayService.getGatewayByid(Long.parseLong(assetid));
//
//			// delete entry in device_replaced table
//			boolean isRemovedInDeviceReplaced = gatewayServiceV4.removeNotComplitedDeviceReplaced(user.getId(),
//					gateway.getMeid());
//			log.info(" Gateway deleted in device_replaced table : " + isRemovedInDeviceReplaced);
//			result = true;
//		} catch (Exception e) {
//			log.error("Error while deleting gateway details : " + e.getLocalizedMessage());
//			return false;
//		}
		return result;
	}

	public int saveORupdatePetprofile(List<JPetprofile> jpetprofiles, long userid) {
		log.info(" Entered GatewayDao :: saveORupdatePetprofile ");
		int status = 1;
		for (JPetprofile jpetprofile : jpetprofiles) {
			PetProfile petProfile = null;
			if (jpetprofile != null) {
				if (!jpetprofile.getGateway_id().isEmpty() && !jpetprofile.getAge().isEmpty()
						&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
						&& !jpetprofile.getSex().isEmpty()) {

					// Check if a record with given gateway id already exists
					petProfile = getPetprofile(0, Long.valueOf(jpetprofile.getGateway_id()), null, null, null, null);
					if (petProfile != null) {
						// System.out.println("Id =
						// "+petprofiles.get(0).getId());

						// petProfile.setAge(jpetprofile.getAge());
						// TODO:Need to add for months and days here
						Date birth_date = getBirthDate("YEAR", jpetprofile.getAge());
						petProfile.setBirth_date(birth_date);
						petProfile.setBreed(jpetprofile.getBreed());
						petProfile.setHeight(jpetprofile.getHeight());
						petProfile.setWeight(jpetprofile.getWeight());
						petProfile.setSex(jpetprofile.getSex());
						petProfile.setRemarks(jpetprofile.getRemarks());
						petProfile.setImageurl(jpetprofile.getImageurl());
						petProfile.setName(jpetprofile.getName());

						PetSpecies species = petSpeciesServices.getPetSpeciesByName(jpetprofile.getSpecieName());

						if (species == null) {
							species = petSpeciesServices.getPetSpecies(1L);
						}

						petProfile.setPetSpecies(species);
						petProfile.setUser_id(userid);
						/*
						 * Gateway gateway = getGateway(Long.valueOf(jpetprofile.getGateway_id())) ;
						 * petProfile.setGateway(gateway);
						 */

					} else {// create a pet profile with this gateway for given
							// user
						petProfile = convertToPetprofile(jpetprofile, userid);

					}
					try {
						if (petProfile != null) {
							sessionFactory.getCurrentSession().saveOrUpdate(petProfile);

						} else {
							log.info("saveORupdatePetprofile::Error: Attempting to save null profile");
							status = -1;
							break;
						}
					} catch (Exception e) {
						log.error("saveORupdatePetprofile: Exception: " + e.getMessage());
						status = -1;
						break;
					}

				} else {
					log.info("saveORupdatePetprofile: Error:mandatory fields are missing");
					status = -2;
					break;
				}

			} else {
				log.info("saveORupdatePetprofile: Error: Input JPetprofile is null");
				// System.out.println("saveORupdatePetprofile: Error:
				// JPetprofile is null");
				status = -2;
				break;
			}
		}

		return status;
	}

	// Used for pet_profile table
	@Override
	public Date getBirthDate(String calendarValue, String age) {
		log.info(" Entered GatewayDao :: getBirthDate ");
		Date birth_date = null;
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		if (age.equals("0")) {// statically assign age of the pet as 6 months,
								// as months will not be entered by user in App
			currDateCal.add(Calendar.MONTH, -6);
			birth_date = currDateCal.getTime();
		} else {
			String no = "-" + age;
			if (calendarValue.equals("YEAR")) {// set x years back, assume x=
												// age
				currDateCal.add(Calendar.YEAR, Integer.parseInt(no));
				birth_date = currDateCal.getTime();
			} else if (calendarValue.equals("MONTH")) {// set x months back
				currDateCal.add(Calendar.MONTH, Integer.parseInt(no));
				birth_date = currDateCal.getTime();
			} else if (calendarValue.equals("DAYS")) {// set x days back
				currDateCal.add(Calendar.DAY_OF_YEAR, Integer.parseInt(no));
				birth_date = currDateCal.getTime();
			} else
				log.info("getBirthDate:calendarValue must be either 'YEAR' or 'MONTH'");
		}

		// System.out.println("getBirthDate: birth_date = "+ birth_date);
		return birth_date;
	}

	// Return the age from given birth_date stored in DB
	private String getAge(Date birth_date) {
		log.info(" Entered GatewayDao :: getAge ");
		// System.out.println("getAge:: input birth_date = "+birth_date);
		String age = null;
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		Date curDate = currDateCal.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-mm-dd");
		String strCurrdate = sdf.format(curDate);
		try {
			curDate = sdf.parse(strCurrdate);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
		}

		Calendar birthDateCal = Calendar.getInstance();
		birthDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		birthDateCal.setTime(birth_date);

		int diffYear = currDateCal.get(Calendar.YEAR) - birthDateCal.get(Calendar.YEAR);
		int monthsdiff = currDateCal.get(Calendar.MONTH) - birthDateCal.get(Calendar.MONTH);
		int totMonthsDiff = diffYear * 12 + monthsdiff;

		long diffdays = curDate.getTime() - birthDateCal.getTime().getTime();

		long yr = diffdays / 365;

		if (diffYear >= 1) {
			if (totMonthsDiff == 6)// ages <1 is statically stored in DB as 6
									// months
				age = "0";
			else
				age = String.valueOf(diffYear);
		} else {// diffYear = zero
			if (totMonthsDiff >= 1)
				/*
				 * Fix to return 0 if the month is > 0 . Initially it is returning as 6 so that
				 * it is showing 6 years in mobile app. age = String.valueOf(totMonthsDiff);
				 */
				age = "0";
			else // totMonthsDiff = zero
				age = String.valueOf(diffdays);
		}
		/*
		 * else if(totMonthsDiff < 12){ if(totMonthsDiff == 0) age =
		 * String.valueOf(diffdays);// +" days"; else age =
		 * String.valueOf(totMonthsDiff);// +" months"; } else if(totMonthsDiff == 12)
		 * age = "1" ;//+" year";//1 year else age = String.valueOf(diffYear);//
		 * +" years";
		 */ // System.out.println("getAge: age = "+age+ " diffYear = "+diffYear+
			// " monsdiff = "+ totMonthsDiff+" diffdays = "+diffdays+"
			// actual_year_diff = "+yr);
//		if (age == null)
//			log.info("getAge: returns null,diffYear = " + diffYear + " monsdiff = " + totMonthsDiff + " diffdays = "
//					+ diffdays);
		return age;

	}

	private PetProfile convertToPetprofile(JPetprofile jpetprofile, long userid) {
		log.info(" Entered GatewayDao :: convertToPetprofile ");
		PetProfile petProfile = null;
		// Get the gateway details of given gateway id and userid
		List<Gateway> gateways = getGateway(null, null, null, jpetprofile.getGateway_id(), userid, null);
		// getGateway(Long.valueOf(jpetprofile.getGateway_id()));
		if (gateways != null && gateways.size() != 0) {
			Gateway gateway = gateways.get(0);// only record exists for given
												// Gid and userid
			// TODO: need to add for MONTHS and DAYS
			Date birth_date = getBirthDate("YEAR", jpetprofile.getAge());

			petProfile = new PetProfile(jpetprofile.getId(), gateway, jpetprofile.getName(), birth_date,
					jpetprofile.getSex(), jpetprofile.getBreed(), jpetprofile.getHeight(), jpetprofile.getWeight(),
					jpetprofile.getRemarks(), jpetprofile.getImageurl(), userid);

			petProfile.setEnable(jpetprofile.isEnable());

			PetSpecies species = petSpeciesServices.getPetSpeciesByName(jpetprofile.getSpecieName());

			if (species == null) {
				species = petSpeciesServices.getPetSpecies(1L);
			}

			petProfile.setPetSpecies(species);
		} else {
			log.info("convertJPetprofileToPetprofile: Gateway does not exists for given user id - " + userid);
			// System.out.println("convertJPetprofileToPetprofile: Gateway does
			// not exists for given user id - "+ userid);
		}

		return petProfile;
	}

	private JPetprofile convertToJPetprofile(PetProfile petprofile) {
		log.info(" Entered GatewayDao :: convertToJPetprofile ");
		JPetprofile jPetprofile = null;
		if (petprofile.getGateway() != null) {

			String imgUrl = (petprofile.getImageurl() == null ? "" : petprofile.getImageurl());
			jPetprofile = new JPetprofile(petprofile.getId(), String.valueOf(petprofile.getGateway().getId()),
					petprofile.getName(), getAge(petprofile.getBirth_date()), petprofile.getSex(),
					petprofile.getBreed(), petprofile.getHeight(), petprofile.getWeight(), petprofile.getRemarks(),
					imgUrl, petprofile.getPetSpecies().getSpeciesName(), petprofile.getUser_id());
			jPetprofile.setEnable(petprofile.isEnable());
		} else {
			log.info("convertToJPetprofile:Error:PetProfile.getGateway returned null");
			// System.out.println("convertToJPetprofile:PetProfile.getGateway
			// returned null" );
		}

		return jPetprofile;
	}

	// Retrieve the PetProfiles matching given parameters
	public PetProfile getPetprofile(long id, long gatewayid, String name, String age, String sex, String breed) {
		log.info(" Entered GatewayDao :: getPetprofile ");
		PetProfile petProfile = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			Session session = sessionFactory.getCurrentSession();

			Criteria criteria = session.createCriteria(PetProfile.class);
			if (id != 0)
				criteria.add(Restrictions.eq("id", id));
			if (gatewayid != 0)
				criteria.add(Restrictions.eq("gateway.id", gatewayid));
			if (name != null && !name.isEmpty())
				criteria.add(Restrictions.eq("name", name));
			if (age != null && !age.isEmpty()) {

				Date birth_date = getBirthDate("YEAR", age);
				// System.out.println("search birth_date = "+birth_date);
				if (birth_date != null) {
					try {
						String strBirthDate = sdf.format(birth_date);
						birth_date = sdf.parse(strBirthDate);
					} catch (ParseException pe) {
						log.error("Exception pe = " + pe.getLocalizedMessage());
					}

					// System.out.println("Search age = "+age+" birth_date =
					// "+birth_date);
					criteria.add(Restrictions.eq("birth_date", birth_date));
				} else {
					log.info("getPetprofile::getBirthDate()returned null");
					// System.out.println("getPetprofile::getBirthDate()returned
					// null");
				}
			}
			if (sex != null && !sex.isEmpty())
				criteria.add(Restrictions.eq("sex", sex));
			if (breed != null && !breed.isEmpty())
				criteria.add(Restrictions.eq("breed", breed));
			petProfile = (PetProfile) criteria.list().get(0);
		} catch (Exception e) {
			log.error("getJPetprofile: Exception: " + e.getMessage());
			// System.out.println("getJPetprofile: Exception: "+e.getMessage());
		}
		return petProfile;
	}

	public List<JPetprofile> getJPetprofiles(long userid, long id, long gatewayid, String name, String age, String sex,
			String breed) {
		log.info(" Entered GatewayDao :: getJPetprofiles ");
		List<JPetprofile> jpetprofileList = null;
		// System.out.println("getJPetprofiles - userid = "+ userid);
		try {
			if (gatewayid != 0) {
				PetProfile petprofile = getPetprofile(id, gatewayid, name, age, sex, breed);
				if (petprofile != null) {
					petprofile.setUser_id(userid);
					JPetprofile jPetProfile = convertToJPetprofile(petprofile);
					if (jPetProfile != null) {
						jpetprofileList = new ArrayList<JPetprofile>();
						jpetprofileList.add(jPetProfile);
					} else
						log.info("getJPetprofiles: Error: jPetProfile is null. Not able to add in list");
				} else
					log.info("getJPetprofile: No pet profile exists for this gateway id - " + gatewayid);

			} else {// fetch all user pet profiles
				List<Gateway> gateways = getGateway(null, null, null, null, userid, null);
				if (gateways != null && gateways.size() > 0) {
					// System.out.println("gateway persent");
					jpetprofileList = new ArrayList<JPetprofile>();
					for (Gateway g : gateways) {

						PetProfile petprofile = getPetprofile(id, g.getId(), name, age, sex, breed);
						if (petprofile != null) {
							// System.out.println("profile present");
							JPetprofile jPetProfile = convertToJPetprofile(petprofile);
							if (jPetProfile != null)
								jpetprofileList.add(jPetProfile);
							else {
								log.info("getJPetprofiles: Error: jPetProfile is null. Not able to add in list");
								// System.out.println("getJPetprofiles: Error:
								// convertToJPetprofile returned null");
								break;
							}

						} else {
							log.info("getJPetprofile: No pet profile exists for this gateway id - " + g.getId());
							// System.out.println("getJPetprofile: No pet
							// profile exists for this gateway id - "+g.getId()
							// );
						}
					}

				} else {
					log.info("getJPetprofile: No gateways are mapped to this user id- " + userid);
					// System.out.println("getJPetprofile: No gateways are
					// mapped to this user id- "+ userid );

				}
			}

		} catch (Exception e) {
			log.error("getJPetprofile: Exception: " + e.getMessage());
			// System.out.println("getJPetprofile: Exception: "+e.getMessage());
		}
		return jpetprofileList;
	}

	@Override
	public JResponse getJPetprofilesByUser(long userid, long gatewayid, int monitortype) {
		log.info(" Entered GatewayDao :: getJPetprofilesByUser ");
		JResponse resp = new JResponse();
		List<HashMap<String, Object>> createpetprofileList = new ArrayList<HashMap<String, Object>>();
		List<HashMap<String, Object>> editpetprofileList = new ArrayList<HashMap<String, Object>>();
		HashMap<String, Object> petProf = new HashMap<String, Object>();
		PetProfile petprofile;
		Gateway gateway;
		JPetprofile jPetProfile = null;
		boolean createprofile = false;// this flag is to identify whether new device is available or not to create
										// profile -
		try {
			if (gatewayid != 0) {
				petprofile = getPetprofile(0, gatewayid, "", "", "", "");

				if (petprofile == null) {
					gateway = gatewayService.getGateway(gatewayid);
					createprofile = true;
				} else
					gateway = petprofile.getGateway();

				if (petprofile != null) {
					petprofile.setUser_id(userid);
					jPetProfile = convertToJPetprofile(petprofile);
				}

				petProf.put("gatewayid", gateway.getId());
				petProf.put("gatewayname", gateway.getName());
				petProf.put("qrc", gateway.getQrcode());
				petProf.put("monitortype", gateway.getModel().getMonitor_type().getId());
				petProf.put("jPetProfile", jPetProfile);

				if (jPetProfile == null)
					createpetprofileList.add(petProf);
				else
					editpetprofileList.add(petProf);

			} else {// fetch all user pet profiles
				List<Gateway> gatewayList = getGateway(null, null, null, null, userid, null);
				List<Gateway> gateways = new ArrayList<Gateway>();

				if (gatewayList != null && gatewayList.size() > 0) {

					if (monitortype > 0) {
						for (Gateway g : gatewayList) {
							if (monitortype == g.getModel().getMonitor_type().getId())
								gateways.add(g);
						}
					} else
						gateways.addAll(gatewayList);

					for (Gateway g : gateways) {
						petprofile = getPetprofile(0, g.getId(), "", "", "", "");

						if (petprofile != null) {
							petprofile.setUser_id(userid);
							jPetProfile = convertToJPetprofile(petprofile);
						} else {
							createprofile = true;
							jPetProfile = null;
						} // this flag is to identify whether new device is available or not to
							// create

						petProf = new HashMap<String, Object>();

						petProf.put("gatewayid", g.getId());
						petProf.put("gatewayname", g.getName());
						petProf.put("qrc", g.getQrcode());
						petProf.put("monitortype", g.getModel().getMonitor_type().getId());
						petProf.put("jPetProfile", jPetProfile);

						if (jPetProfile == null)
							createpetprofileList.add(petProf);
						else
							editpetprofileList.add(petProf);
					}

				} else {
					log.info("getJPetprofile: No gateways are mapped to this user id- " + userid);
				}
			}
			resp.put("createprofile", createprofile);
			resp.put("Status", 1);
			resp.put("Msg", "success");
			resp.put("createpetlist", createpetprofileList);
			resp.put("editpetlist", editpetprofileList);

		} catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Msg", "Error occured");
			log.error("getJPetprofile: Exception: " + e.getMessage());
		}
		return resp;
	}

	@Override
	public boolean updateGatewayName(String gatewayName, String gatewayId) {
		log.info(" Entered GatewayDao :: updateGatewayName ");
		boolean isSuccess = false;
		try {

			log.info("Update Gateway function");
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE Gateway set name = :name " + "WHERE id = :id";
			Query query = session.createQuery(hql);
			query.setParameter("name", gatewayName);
			query.setParameter("id", Long.parseLong(gatewayId));
			query.executeUpdate();
			isSuccess = true;
			return isSuccess;

		} catch (Exception e) {
			log.error("Error in updateGatewayName :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public Gateway saveORupdateQRCGateway(JGateway jgateway, long cmpid)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException {
		log.info(" Entered GatewayDao :: saveORupdateQRCGateway ");
		Gateway gateway = this.convetJGatewaytoGateway(jgateway, cmpid);
		Gateway gatewayPrev = null;
		try {
			if (jgateway.getId() != 0) {

				gatewayPrev = getGateway(jgateway.getId());
				gateway.saveDatap(getDataP(gatewayPrev.giveDatap().getId()));
				gateway.setName(jgateway.getName());

				gateway.setMinTemp(gatewayPrev.getMinTemp());
				gateway.setMaxTemp(gatewayPrev.getMaxTemp());
				gateway.setAlive(gatewayPrev.isAlive());
				gateway.setAssetinformation(gatewayPrev.getAssetinformation());
				gateway.setInstalled_date(gatewayPrev.getInstalled_date());
				if (jgateway.isWebapp() == false) {
					gateway.setStopreport(gatewayPrev.isStopreport());
					gateway.setStarttime(gatewayPrev.getStarttime());
					gateway.setStoptime(gatewayPrev.getStoptime());
				}
			} else if (jgateway.getId() == 0) {
				gateway.setInstalled_date(Timestamp.valueOf(DynamicCmdDaoImpl.getUtcDateTime()));
				gateway.saveDatap(getDataP(jgateway.getPasswordtype()));
			}

			/*
			 * webapp send default(NA) values in start & stoptime.Once webapp changed
			 * default values(0). this code is not need
			 */

			String starttime = gateway.getStarttime().trim();
			String stoptime = gateway.getStoptime().trim();
			if (starttime.equalsIgnoreCase("NA"))
				gateway.setStarttime("0");
			if (stoptime.equalsIgnoreCase("NA"))
				gateway.setStoptime("0");

			sessionFactory.getCurrentSession().saveOrUpdate(gateway);
			saveDynamiccmd(gateway, gatewayPrev, jgateway.getId());
		} catch (Exception e) {
			log.error("Error in saveORupdateQRCGateway :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
		}
		return gateway;
	}

	@Override
	public boolean updatepetprofile(String gatewayId) {
		log.info(" Entered GatewayDao :: updatepetprofile ");
		try {
			String qry = "update pet_profile  set gateway_id=NULL WHERE gateway_id = " + gatewayId + "";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		} catch (Exception e) {
			log.error(
					"Error in updatepetprofile :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}

	}

	public PetProfile getPetProfile(long gateway_id) {
		try {
			log.info(" Entered GatewayDao :: getPetProfile ");
			String qry = "from PetProfile where enable=1 and gateway = '" + gateway_id + "'";
			return (PetProfile) this.sessionFactory.getCurrentSession().createQuery(qry).list().get(0);
		} catch (Exception e) {
			log.error("Exception in getPetProfile : " + e.getLocalizedMessage());
			return null;
		}
	}

	public ProbeCategory getProbeCategory(long model_id) {
		try {
			String qry = "from ProbeCategory where model_id = '" + model_id + "'";
			return (ProbeCategory) this.sessionFactory.getCurrentSession().createQuery(qry).list().get(0);
		} catch (Exception iobe) {
			log.error(iobe.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean gatewayOnOff(String gatewayId, boolean isEnable, String message) {
		log.info(" Entered GatewayDao :: gatewayOnOff ");
		try {
			log.info("Update OnOff Status");

			String onOffStatus = "1";

			if (isEnable) {
				onOffStatus = "1";

			} else {
				onOffStatus = "0";
			}

			String qry = "UPDATE gateway  SET onoffstatus = '" + onOffStatus + "' WHERE id = '" + gatewayId + "';";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;

		} catch (Exception e) {
			log.error("Error in gatewayOnOff :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());

			return false;
		}
	}

	@Override
	public boolean enableOrDisableGateway(String gatewayId, String userId, boolean isEnable) {
		log.info(" Entered GatewayDao :: enableOrDisableGateway ");
		try {
			String subQry="";

			if (isEnable) {
				subQry = " isenable=1 ";
			}
			else {
				subQry = " isenable=0, isalive=0 ";
			}

			log.info("Making this Gateway isEnable : " + isEnable);
			String qry = "UPDATE gateway SET " + subQry + " WHERE id = '" + gatewayId + "';";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return false;
		}

	}

	@Override
	public int updateGoalSetting(String gatewayId, String default_goal) {
		log.info(" Entered GatewayDao :: updateGoalSetting ");
		// TODO Auto-generated method stub
		int goalSetting = 0;
		Session session = sessionFactory.openSession();

		try {

			Query qry = session.createQuery("UPDATE Gateway SET default_goal = :default_goal where id = :id");

			qry.setParameter("default_goal", Integer.valueOf(default_goal));
			qry.setParameter("id", Long.valueOf(gatewayId));
			goalSetting = qry.executeUpdate();

			return goalSetting;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		} finally {
			session.close();
		}
		return goalSetting;
	}

	@Override
	public int updateCaloriesGoalSetting(String gatewayId, String calories_goal) {
		log.info(" Entered GatewayDao :: updateCaloriesGoalSetting ");
		// TODO Auto-generated method stub
		int goalSetting = 0;
		Session session = sessionFactory.openSession();

		try {

			Query qry = session.createQuery("UPDATE Gateway SET calories_goal = :calories_goal where id = :id");

			qry.setParameter("calories_goal", Integer.valueOf(calories_goal));
			qry.setParameter("id", Long.valueOf(gatewayId));
			goalSetting = qry.executeUpdate();

			return goalSetting;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		} finally {
			session.close();
		}
		return goalSetting;
	}

	public List<Gateway> getGatewayByLastReport(long userid) {
		log.info(" Entered GatewayDao :: getGatewayByLastReport ");
		String qry = "select G from Gateway G, IN (G.users) user where user.id ='" + userid + "' ";

		try {
			List<Gateway> gateways = this.sessionFactory.getCurrentSession().createQuery(qry).list();
			return gateways;
		} catch (Exception e) {
			log.error("exception catched while getting gateway");
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public JGatewayUserDetails getGatewayAndUserDetails(String meid) {
		log.info(" Entered GatewayDao :: getGatewayAndUserDetails ");
		JGatewayUserDetails jGatewayUserDetails = null;
		String qry = "select G from Gateway G where G.meid ";

		if (meid.length() >= 14) {
			qry = qry + "='" + meid + "' ";

		} else {
			qry = qry + "like '%" + meid + "%'";
		}

		List<Gateway> gateways = this.sessionFactory.getCurrentSession().createQuery(qry).list();
		if (gateways.size() != 0) {

			Gateway mappedGateway = gateways.get(0);
			Set<User> users = mappedGateway.getUsers();// Fetch user details
			String username = null;
			long userid = 0;
			String pswd = null;
			String auth = null;
			for (User user : users) {
				// System.out.println("Username: "+user.getUsername());
				username = user.getUsername();
				userid = user.getId();
				pswd = user.getPassword();
				auth = user.getAuthKey();
			}
			log.info("getGateway: Gateway " + meid + " is mapped to username - " + username);

			jGatewayUserDetails = new JGatewayUserDetails(mappedGateway.getName(), username, pswd, userid,
					mappedGateway.getId(), auth);

		} else {
			log.info("getGateway: No gateway mappings exists for meid - " + meid);
		}
		return jGatewayUserDetails;
	}

	@Override
	public JGatewayUserDetails getGatewayByMAC(String macid, long user_id) {
		log.info(" Entered GatewayDao :: getGatewayByMAC ");
		JGatewayUserDetails jGatewayUserDetails = null;

		try {
			String qry = "SELECT G.id,G.name,U.authkey,U.username,U.password,U.id AS userid FROM gateway G JOIN usergateway UG ON G.id=UG.gatewayid\r\n"
					+ " JOIN  `user` U ON U.id =UG.userid WHERE G.macid=:mac_id AND U.id =:user_id ";
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("mac_id", macid);
			query.setParameter("user_id", user_id);

			List res = query.list();
			if (!res.isEmpty()) {
				Object[] gatewayObj = (Object[]) res.get(0);
				long gid = ((BigInteger) gatewayObj[0]).longValue();
				String gname = (String) gatewayObj[1];
				String authkey = (String) gatewayObj[2];
				String username = (String) gatewayObj[3];
				String password = (String) gatewayObj[4];
				long userid = ((BigInteger) gatewayObj[5]).longValue();

				jGatewayUserDetails = new JGatewayUserDetails(gname, username, password, userid, gid, authkey);
			} else {
				log.info("getGateway: No gateway mappings exists for macid - " + macid);
			}
		} catch (Exception e) {
			log.error("Error in getGatewayByMAC :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
		}
		return jGatewayUserDetails;
	}

	@Override
	public List<FurbitLastGatewayReport> getFLastGatewayReportByUser(long userid) {
		log.info(" Entered GatewayDao :: getFLastGatewayReportByUser ");
		String qry = "SELECT * FROM furbitlastgatewayreport ";
		// WHERE gateway_id IN (SELECT gatewayid FROM usergateway JOIN gateway g JOIN
		// \r\n" +
		// " assetmodel a ON g.model_id = a.id WHERE a.monitor_type_id=2 and
		// userid="+userid+")
		try {
			List<FurbitLastGatewayReport> rpt = this.sessionFactory.getCurrentSession().createSQLQuery(qry)
					.addEntity(FurbitLastGatewayReport.class).list();
			return rpt;
		} catch (Exception e) {
			log.error("exception in FurbitLastGatewayReport : " + e.getLocalizedMessage());
			return null;
		}
	}

	public ArrayList<JGatewayConfig> getGatewayConfig(long userid) {
		log.info(" Entered GatewayDao :: getGatewayConfig ");
		ArrayList<JGatewayConfig> configList = new ArrayList<JGatewayConfig>();

		try {
			String qry = "SELECT G.id,G.onoffstatus,G.gatewayconfig FROM gateway G , usergateway UG "
					+ " WHERE G.id=UG.gatewayid AND UG.userid=" + userid + ";";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {
					Object[] tuple = (Object[]) res.get(i);

					JGatewayConfig config = new JGatewayConfig();
					config.setAssetid(((BigInteger) tuple[0]).longValue());
					config.setOnOffStatus((Boolean) tuple[1]);
					config.setGatewayConfig((String) tuple[2]);
					configList.add(config);
				}
			}
		} catch (Exception e) {
			log.error("getGatewayConfig : " + e.getLocalizedMessage());
		}
		return configList;
	}

	@Override
	public boolean updatePetProfile(PetProfile petProfile) {
		log.info(" Entered GatewayDao :: updatePetProfile ");
		boolean isMerged = false;
		try {
			Session ses = sessionFactory.getCurrentSession();
			ses.merge(petProfile);
			isMerged = true;
		} catch (Exception e) {
			log.error("updatePetProfile : " + e.getLocalizedMessage());
			isMerged = false;
		}
		return isMerged;
	}

	@Override
	public AssetModel getAssetModelByName(String inventorymodelname) {
		log.info(" Entered GatewayDao :: getAssetModelByName ");
		Session session = slave4SessionFactory.openSession();
		try {

			String qry = "SELECT * FROM assetmodel WHERE inventorymodelname='" + inventorymodelname + "';";
			Query query = session.createSQLQuery(qry).addEntity(AssetModel.class);
			AssetModel assetModel = (AssetModel) query.list().get(0);
			return assetModel;
		} catch (Exception e) {
			log.error("Error in getAssetModelByName :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return null;
		} finally {
			session.close();
		}
	}

	@Override
	public boolean checkQrcExist(long id, String qrcode) {
		log.info(" Entered GatewayDao :: checkQrcExist ");
		try {
			Session ses = slave3SessionFactory.getCurrentSession();
			SQLQuery qry = ses.createSQLQuery(
					"SELECT G.qrcode FROM gateway G JOIN usergateway UG ON G.id = UG.gatewayId WHERE UG.userId = '" + id
							+ "' AND G.qrcode = '" + qrcode + "'");
			List<Object[]> list = qry.list();
			if (list.size() > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error("Error in checkQrcExist :: Session Name : slave3SessionFactory :: error : "
					+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public Gateway getGatewayByid(long id) {
		try {
			log.info(" Entered GatewayDao :: getGatewayByid ");
			String qry = "  SELECT * FROM gateway  WHERE id=" + id;
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(Gateway.class);
			Gateway gateway = (Gateway) query.list().get(0);
			return gateway;
		} catch (Exception e) {
			log.error("Error in getGatewayByid :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public int updateGateway(Gateway gateway) {
		log.info(" Entered GatewayDao :: updateGateway ");
		int result = 0;
		try {
			String qry = "update gateway set minval=" + gateway.getMinTemp() + ",maxval=" + gateway.getMaxTemp()
					+ " where id =" + gateway.getId() + ";";
			result = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("updateGateway : " + e.getLocalizedMessage());
		}
		return result;
	}

	@Override
	public JGateway getJGateway(String columnname, String value) {
		log.info(" Entered GatewayDao :: getJGateway ");
		JGateway jgateway = new JGateway();
		String qry = "SELECT G.id,G.name,G.meid,G.model_id FROM gateway G  WHERE " + columnname + " ='" + value + "' ";
		try {
			Query qry_sql = sessionFactory.getCurrentSession().createSQLQuery(qry);

			List<Object[]> gatewayList = (List<Object[]>) qry_sql.list();

			if (!gatewayList.isEmpty()) {
				for (Object[] gat : gatewayList) {
					jgateway.setId(Long.valueOf(gat[0].toString()));
					jgateway.setName(gat[1].toString());
					jgateway.setMeid((String) gat[2]);
					jgateway.setModelid(Long.valueOf(gat[3].toString()));
				}
			}
		} catch (Exception e) {
			log.error("Exception : getJGateway : " + e.getLocalizedMessage());
			return null;
		}

		return jgateway;
	}

	@Override
	public List<JGateway> getJGatewayByUser(long userid, String monitor_type) {
		log.info(" Entered GatewayDao :: getJGatewayByUser ");
		List<JGateway> jgatewaylist = new ArrayList<JGateway>();
		monitor_type = monitor_type.trim();
		String qry = "";
		Query qry_sql = null;

		if (!monitor_type.isEmpty()) {
			qry = "SELECT G.id,G.name,G.meid,G.model_id,G.macid,G.qrcode,G.show_order_id, G.purchased_from_others,"
					+ "AM.monitor_type_id,AM.temp_alert,G.order_channel FROM gateway G JOIN usergateway UG ON G.id= UG.gatewayid JOIN assetmodel AM ON "
					+ " G.model_id=AM.id WHERE UG.userid = :userid AND  AM.monitor_type_id IN ( :monitorid ) "
					+ " ORDER BY G.id";
			qry_sql = slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			qry_sql.setParameter("userid", userid);
			qry_sql.setParameterList("monitorid", monitor_type.split(","));
		} else {
			qry = "SELECT G.id,G.name,G.meid,G.model_id,G.macid,G.qrcode,G.show_order_id,G.purchased_from_others,"
					+ "AM.monitor_type_id,AM.temp_alert,G.order_channel FROM gateway G"
					+ " JOIN usergateway UG ON G.id= UG.gatewayid JOIN `assetmodel` AM ON AM.id=G.model_id  "
					+ "WHERE UG.userid =:userid ORDER BY G.id";
			qry_sql = slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			qry_sql.setParameter("userid", userid);
		}

		try {
			List<Object[]> gatewayList = (List<Object[]>) qry_sql.list();

			if (!gatewayList.isEmpty()) {
				for (Object[] gat : gatewayList) {
					JGateway jgateway = new JGateway();

					jgateway.setId(Long.valueOf(gat[0].toString()));
					jgateway.setName(gat[1].toString());
					jgateway.setMeid((String) gat[2]);
					jgateway.setModelid(Long.valueOf(gat[3].toString()));
					jgateway.setMacid(gat[4].toString());
					jgateway.setQrcode(gat[5].toString());
					jgateway.setShowOrderId((Boolean) gat[6]);
					jgateway.setPurchased_from_others((Boolean) gat[7]);
					jgateway.setMonitorTypeId(Long.valueOf(gat[8].toString()));// monitor_type_id
					jgateway.setTemp_alert((Boolean) gat[9]);// monitor_type_id
					if(gat[10]!=null)
						jgateway.setOrder_channel(Long.valueOf(gat[10].toString()));
					jgatewaylist.add(jgateway);
				}
			}
		} catch (Exception e) {
			log.error("Error in getJGatewayByUser :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return null;
		}

		return jgatewaylist;
	}

	@Override
	public List<JGatewayForPet> getNotMappedGateway(long userid) {
		log.info(" Entered GatewayDao :: getNotMappedGateway ");
		List<JGatewayForPet> jgatewaylist = new ArrayList<JGatewayForPet>();
		String qry = "SELECT G.id,G.name,G.meid,G.qrcode,MT.id as monitortype, MT.name as mname,G.macid FROM gateway G JOIN usergateway UG ON G.id = UG.gatewayid JOIN assetmodel AM "
				+ " ON G.model_id = AM.id JOIN monitortype MT ON MT.id = AM.monitor_type_id  WHERE UG.userid = "
				+ userid + " AND gatewayid NOT IN "
				+ " (SELECT gateway_id FROM pet_profile P WHERE P.enable =1 and P.user_id = " + userid
				+ " AND gateway_id IS NOT NULL)";
		try {
			Query qry_sql = slave5SessionFactory.getCurrentSession().createSQLQuery(qry);

			List<Object[]> gatewayList = (List<Object[]>) qry_sql.list();

			if (!gatewayList.isEmpty()) {
				for (Object[] gat : gatewayList) {
					JGatewayForPet jgateway = new JGatewayForPet();

					jgateway.setGatewayId(Long.valueOf(gat[0].toString()));
					jgateway.setGatewayName(gat[1].toString());
					jgateway.setMeid((String) gat[2]);
					String qrc = (gat[3] != null) ? "QRC: " + (String) gat[3] : "";
					jgateway.setQrcode(qrc);
					jgateway.setMonitortype(Long.valueOf(gat[4].toString()));
					jgateway.setMtypename((String) gat[5]);
					jgateway.setMacid((String) gat[6]);
					jgatewaylist.add(jgateway);
				}
			}
		} catch (Exception e) {
			log.error("Error in getNotMappedGateway :: Session Name : slave5SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return null;
		}

		return jgatewaylist;
	}

	@Override
	public long getOrderchannelid(String orderchannel) {
		log.info(" Entered GatewayDao :: getOrderchannelid ");
		String qry = "SELECT id FROM `order_channel` WHERE  shortdescription='" + orderchannel + "'";
		long orderchannelid = 1;
		try {
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty())
				orderchannelid = Long.parseLong(res.get(0).toString());
		} catch (Exception e) {
			log.error("getOrderchannelid : ", e.getLocalizedMessage());
		}
		return orderchannelid;
	}

//	@Override
//	public int saveOrderchannelDetail(long user_id,long gateway_id,long orderchannel) {
//		int result =0;
//		try {
//			String qry = "insert into `usergateway_orderchannel` (`user_id`, `gateway_id`, `orderchannel_id`) "
//					+ " values("+user_id+","+gateway_id+","+orderchannel+");";
//
//			result = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
//			log.info("saveOrderchannelDetail : result:"+result+" : "+qry);
//		}
//		catch (Exception e) {
//			log.error("saveOrderchannelDetail : Exception: ",e.getLocalizedMessage());
//		}		
//		return result;		
//	}

	@Override
	public boolean updateProfileImgPath(long ppid, String imgpath) {
		log.info(" Entered GatewayDao :: updateProfileImgPath ");
		int result = 0;
		try {
			String qry = "update pet_profile set imageurl='" + imgpath + "' where id ='" + ppid + "';";
			result = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("updateProfileImgPath : " + e.getLocalizedMessage());
			return false;
		}

		return true;
	}

	@Override
	public int getDeviceCount(long userid) {
		log.info(" Entered GatewayDao :: getDeviceCount ");
		String qry = "SELECT COUNT(*) AS cnt FROM usergateway WHERE userid=" + userid;
		int cnt = 0;
		try {
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List res = query.list();

			if (!res.isEmpty())
				cnt = ((BigInteger) res.get(0)).intValue();
		} catch (Exception e) {
			log.error("getOrderchannelid : ", e.getLocalizedMessage());
		}
		return cnt;
	}

	@Override
	public boolean enableOrDisablePetProfile(long userId, List<EnableOrDisablePetProfile> enableOrDisablePetPrfList) {
		log.info("Entered into disablePetProfile : " + userId);
		try {
			Session session = sessionFactory.getCurrentSession();
			if (enableOrDisablePetPrfList.size() > 0) {
				for (EnableOrDisablePetProfile enableOrDisablePetPrfObj : enableOrDisablePetPrfList) {
					if (!enableOrDisablePetPrfObj.isEnable()) {
						String disableQry = "UPDATE pet_profile SET enable=" + enableOrDisablePetPrfObj.isEnable()
								+ " , " + " gateway_id=NULL, `name` = CONCAT('temp_', `name`)  WHERE id ='"
								+ enableOrDisablePetPrfObj.getPetprofileid() + "' AND user_id='" + userId + "';";
						int modifications = session.createSQLQuery(disableQry).executeUpdate();
						log.info("Pet profile updated status : " + modifications);
					}
				}
			} else {
				log.info("Pet profile list not found or list empty!");
			}
		} catch (Exception e) {
			log.error("Exception occured at enableOrDisablePetProfile Dao : " + e.getLocalizedMessage());
			return false;
		}
		return true;
	}

	@Override
	public boolean updateGatewayName(String gatewayid) {
		log.info("Entered :: updateGatewayName : gatewayid : " + gatewayid);
		boolean update_status = false;
		try {
			Session ses = this.sessionFactory.getCurrentSession();
			String qry = "UPDATE gateway SET `isalive`=0,`isenable`=0,`name` = CONCAT(`name`, '-" + gatewayid + "') WHERE id = " + gatewayid
					+ "";
			// System.out.println(qry);
			int cnt = ses.createSQLQuery(qry).executeUpdate();

			log.info(cnt > 0 ? "updateGatewayName -success " : "updateGatewayName - failed");

			if (cnt > 0) {
				update_status = true;
			} else {
				update_status = false;
			}
		} catch (Exception e) {
			log.error("Error in updateGatewayName :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error("Error while updateGatewayName : " + e.getLocalizedMessage());
			update_status = false;
		}
		return update_status;
	}

	@Override
	public boolean changeGatewayOrderidStatus(long gatewayid, boolean pFromStatus) {
		log.info("Entered ::  : gateway id : " + gatewayid);

		boolean update_orderid_status = false;
		try {
			Session ses = this.sessionFactory.getCurrentSession();
			String qry = "";
			if (pFromStatus)
				qry = "UPDATE gateway SET`purchased_from_others` = " + pFromStatus + " WHERE id = " + gatewayid + "";
			else
				qry = "UPDATE gateway SET `show_order_id` = true, `purchased_from_others` = " + pFromStatus
						+ " WHERE id = " + gatewayid + "";
			int cnt = ses.createSQLQuery(qry).executeUpdate();
			if (cnt > 0) {
				update_orderid_status = true;
			} else {
				update_orderid_status = false;
			}
			return update_orderid_status;
		} catch (Exception e) {
			log.error("Error in changeGatewayOrderidStatus :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public Gateway saveORupdateRecallGateway(JGateway jgateway, long cmpid) throws InvalidAssetGroupIdException,
			InvalidSubgroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException {
		log.info(" Entered GatewayDao :: saveORupdateRecallGateway ");
		log.info(" OldMeid : " + jgateway.getOldMeid());
		log.info("New Meid : " + jgateway.getMeid());
		Gateway gateway = this.convetJGatewaytoRecallGateway(jgateway, cmpid);
		Gateway gatewayPrev = null;

		if (jgateway.getId() != 0) {

			gatewayPrev = getGateway(jgateway.getId());
			gateway.saveDatap(getDataP(gatewayPrev.giveDatap().getId()));
			gateway.setMinTemp(gatewayPrev.getMinTemp());
			gateway.setMaxTemp(gatewayPrev.getMaxTemp());
			gateway.setAlive(gatewayPrev.isAlive());
			gateway.setOnOffStatus(gatewayPrev.isOnOffStatus());
			gateway.setAssetinformation(gatewayPrev.getAssetinformation());
			gateway.setInstalled_date(irisUtil.getDateTime_TS(irisUtil.getCurrentTimeUTC()));

			if (jgateway.getRecallReplaceAction() == 2) { // 2 - Recall Device
				gateway.setShowOrderId(jgateway.isShowOrderId());
			} else {
				gateway.setShowOrderId(gatewayPrev.isShowOrderId());
			}

			if (jgateway.isWebapp() == false) {
				gateway.setStopreport(gatewayPrev.isStopreport());
				gateway.setStarttime(gatewayPrev.getStarttime());
				gateway.setStoptime(gatewayPrev.getStoptime());
			}
		} else if (jgateway.getId() == 0) {
			gateway.setInstalled_date(Timestamp.valueOf(DynamicCmdDaoImpl.getUtcDateTime()));
			gateway.saveDatap(getDataP(jgateway.getPasswordtype()));
			gateway.setOnOffStatus(true);
		}

		/*
		 * webapp send default(NA) values in start & stoptime.Once webapp changed
		 * default values(0). this code is not need
		 */

		String starttime = gateway.getStarttime().trim();
		String stoptime = gateway.getStoptime().trim();
		if (starttime.equalsIgnoreCase("NA"))
			gateway.setStarttime("0");
		if (stoptime.equalsIgnoreCase("NA"))
			gateway.setStoptime("0");

//			Session session = sessionFactory.openSession();
		try {
//			Transaction tx = session.beginTransaction();

			log.info("recall gateway merge :: new meid : " + gateway.getMeid());
			
			gateway.setOrder_channel(jgateway.getOrder_channel());
			gateway.setSim_vendor(jgateway.getSim_vendor());

//			gateway = (Gateway) session.merge(gateway);
//			tx.commit();

			saveDynamiccmd(gateway, gatewayPrev, jgateway.getId());

		} catch (Exception e) {
			log.error("Error in  saveORupdateRecallGateway " + e.getLocalizedMessage());
			e.getLocalizedMessage();
		} finally {
//			session.flush();
//			session.close();
		}

		return gateway;
	}

	private Gateway convetJGatewaytoRecallGateway(JGateway jg, long cmpid) throws InvalidAssetGroupIdException,
			InvalidGroupIdException, InvalidModelIdException, InvalidAsseIdException {
		log.info(" Entered GatewayDao :: convetJGatewaytoRecallGateway ");
		AssetGroup assetgroup = new AssetGroup();
		// SubGroup subgroup = new SubGroup();
		AssetModel model = new AssetModel();
		Groups groups = new Groups();
		Asset asset = null;

		long gatewayId = gatewayServiceV4.getGatewayByMeid(jg.getOldMeid()).getId();
		jg.setId(gatewayId);

		// Asset asset
		try {
			if (jg.getAssetgroupid() == 0)
				assetgroup = null;
			else
				assetgroup = groupservices.getAssetGroup(Long.toString(jg.getAssetgroupid()), cmpid).get(0);
		} catch (IndexOutOfBoundsException e) {

			throw new InvalidAssetGroupIdException();
		}
		try {
			// subgroup =
			// groupservices.getSubGroup("",Long.toString(jg.getSubgroupid()),
			// cmpid).get(0);

			List<Groups> grpLis = (List<Groups>) groupdao.getGroupsByLevel(Long.toString(jg.getGroupid()), "", cmpid,
					"Groups");
			groups = grpLis.get(0);
			// groups =
			// groupservices.getGroups(Long.toString(jg.getGroupid()),"", "1",
			// cmpid).get(0); /*Gateway assigns only level 1*/
		} catch (IndexOutOfBoundsException e) {

			throw new InvalidGroupIdException();
		}
		try {
			model = this.getAssetModel(jg.getModelid());
			// MonitorType monitorType = monitorTypeService.getMonitorTypeById((int)
			// jg.getMonitorTypeId());
			// model.setMonitor_type(monitorType);

		} catch (IndexOutOfBoundsException e) {

			throw new InvalidModelIdException();
		}
		try {
			asset = getAssetById(jg.getId());
			if (!asset.getAssettype().equals("G"))
				throw new InvalidAsseIdException();
			asset.setAssetaddress(jg.getMeid());
			asset.setModel(model);
			gatewayService.updateRecallAsset(asset);
		} catch (IndexOutOfBoundsException ne) {
			log.error("no asset found");
			Asset sasset = new Asset("G", jg.getMeid(), model);
			this.saveAsset(sasset);
			asset = this.getAssetByAddr(jg.getMeid());

		}
		Company company = companyDao.getCompany(cmpid); // cmpid
		Timestamp lastrptdatetime = null;

		Gateway gateway = new Gateway(asset.getId(), jg.getName(), jg.getMeid(), jg.getMdn(), jg.getCarrier(),
				jg.isEnable(), false, jg.getLocation(), jg.getDescription(), jg.getSensorEnable(), jg.getOwner(), asset,
				assetgroup, groups, model, company, lastrptdatetime, jg.isStopreport(), jg.getStarttime(),
				jg.getStoptime(), jg.getQrcode(), jg.getGatewayConfig(), jg.getOnSleepTime(), jg.getOffSleepTime(),
				jg.getDefault_goal(), jg.getCalories_goal(), jg.getMacid(), jg.getSensor_type_id(), model.getDnrInterval(),jg.isIswithoutsub(),jg.isIsnewverdevice());

		return gateway;
	}

	@Override
	public void updateRecallAsset(Asset asset) throws InvalidAsseIdException {
		Session session = this.sessionFactory.getCurrentSession();
		try {
			//Transaction tx = session.beginTransaction();
			session.update(asset);
			//tx.commit();
		} catch (Exception e) {
			//e.printStackTrace();
			throw new InvalidAsseIdException();
		} finally {
			//session.flush();
			//session.close();
		}
	}

	@Override
	public boolean updateAssetMeid(long id, String meid) {
		log.info("Entered into updateAssetMeid :: asset ID : " + id);
		Session session = sessionFactory.getCurrentSession();
//		Transaction tx=null;
		try {
//			session = this.sessionFactory.getCurrentSession();
//			Asset asset = this.getAssetById(id);
//			asset.setAssetaddress(meid);
//			session.merge(asset);
			String updateQry = "UPDATE asset SET assetaddr='" + meid + "' WHERE id='" + id + "'";
			int update = session.createSQLQuery(updateQry).executeUpdate();
//			tx = session.beginTransaction();
			return (update > 0 ? true : false);
		} catch (Exception e) {
			log.error("Error in updateAssetMeid : " + e.getLocalizedMessage());
			return false;
		} finally {
//			tx.commit();
//			session.close();
		}
	}

	@Override
	public boolean saveRecallGateway(Gateway gateway) {
		log.info("Entered into saveRecallGateway :: meid :" + gateway.getMeid());
		try {

			String assetGroupId = gateway.getAssetgroup() == null ? null : "'" + gateway.getAssetgroup().getId() + "'";
			String assetInformationId = gateway.getAssetinformation() == null ? null
					: "'" + gateway.getAssetinformation().getId() + "'";
			String extsensortype = gateway.getExtsensortype() == null ? null : "'" + gateway.getExtsensortype() + "'";
			String lastrptdatetime = gateway.getLastrptdatetime() == null ? null
					: "'" + gateway.getLastrptdatetime() + "'";
			String location = gateway.getLocation() == null ? null : "'" + gateway.getLocation() + "'";
			String owner = gateway.getOwner() == null ? null : "'" + gateway.getOwner() + "'";
			
			boolean alive = gateway.isEnable();
			if(!gateway.isEnable())
				alive = false;
			
			String qry = " UPDATE gateway SET " + "isalive=" + alive + ", asset_id='"
					+ gateway.giveAsset().getId() + "', assetgroup_id=" + assetGroupId + ", assetinfo_id="
					+ assetInformationId + "," + "calories_goal='" + gateway.getCalories_goal() + "', carrier='"
					+ gateway.getCarrier() + "', cmp_id='" + gateway.getCompany().getId() + "', default_goal='"
					+ gateway.getDefault_goal() + "', description='" + gateway.getDescription() + "', isenable="
					+ gateway.isEnable() + ",extsensortype=" + extsensortype + "," + "gatewayconfig='"
					+ gateway.getGatewayConfig() + "', groups_id='" + gateway.getGroups().getId()
					+ "', installed_date='" + gateway.getInstalled_date() + "',lastrptdatetime=" + lastrptdatetime
					+ ",location=" + location + "," + "macid='" + gateway.getMacid() + "', maxval='"
					+ gateway.getMaxTemp() + "', mdn='" + gateway.getMdn() + "', meid='" + gateway.getMeid()
					+ "', minval='" + gateway.getMinTemp() + "', model_id='" + gateway.getModel().getId() + "', name='"
					+ gateway.getName() + "', offsleeptime='" + gateway.getOffSleepTime() + "', onoffstatus="
					+ gateway.isOnOffStatus() + ", onsleeptime='" + gateway.getOnSleepTime() + "', owner=" + owner + ","
					+ "passwordtype='" + gateway.getPasswordtype().getId() + "', qrcode='" + gateway.getQrcode()
					+ "', sensorenable='" + gateway.getSensorenable() + "', show_order_id=" + gateway.isShowOrderId()
					+ ", starttime='" + gateway.getStarttime() + "', stopreport=" + gateway.isStopreport()
					+ ", stoptime='" + gateway.getStoptime() + "' WHERE id =" + gateway.getId();

			int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("saveRecallGateway status : " + status);
//			this.sessionFactory.getCurrentSession().merge(gateway);
			return true;
		} catch (Exception e) {
			log.error("Error in saveRecallGateway : " + e.getLocalizedMessage());
			return false;
		}

	}

	@Override
	public boolean changeDefalutRptInLastGateway(long gatewayId, String currentTimeUTC, int defaultRpt) {
		log.info("Entered into changeDefalutRptInLastGateway :: gatewayId : " + gatewayId);
		try {
			String qry = "update lastgatewayreport set updatedon = '" + currentTimeUTC + "' ,defalut_rpt = "
					+ defaultRpt + " where gateway_id = '" + gatewayId + "'";
			int isUpdated = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (isUpdated >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error(" Error in changeDefalutRptInLastGateway " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public Gateway saveORupdateReturnGateway(JGateway jgateway, long cmpid) throws ConstraintViolationException,
			InvalidAssetGroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException,
			DataIntegrityViolationException, InvalidSubgroupIdException {
		log.info(" Entered GatewayDao :: saveORupdateReturnGateway ");
		Gateway gateway = this.convetJGatewaytoGateway(jgateway, cmpid);
		Gateway gatewayPrev = null;

		if (jgateway.getId() != 0) {
			boolean alive = gateway.isAlive();
			if(!gateway.isEnable())
				alive = false;
			gatewayPrev = getGateway(jgateway.getId());
			gateway.saveDatap(getDataP(gatewayPrev.giveDatap().getId()));
			gateway.setMinTemp(gatewayPrev.getMinTemp());
			gateway.setMaxTemp(gatewayPrev.getMaxTemp());
			gateway.setAlive(alive);
			gateway.setOnOffStatus(gatewayPrev.isOnOffStatus());
			gateway.setAssetinformation(gatewayPrev.getAssetinformation());
			gateway.setInstalled_date(gatewayPrev.getInstalled_date());

			if (jgateway.isWebapp() == false) {
				gateway.setStopreport(gatewayPrev.isStopreport());
				gateway.setStarttime(gatewayPrev.getStarttime());
				gateway.setStoptime(gatewayPrev.getStoptime());
			}
		} else if (jgateway.getId() == 0) {
			gateway.setInstalled_date(Timestamp.valueOf(DynamicCmdDaoImpl.getUtcDateTime()));
			gateway.saveDatap(getDataP(jgateway.getPasswordtype()));
			gateway.setOnOffStatus(true);
		}

		/*
		 * webapp send default(NA) values in start & stoptime.Once webapp changed
		 * default values(0). this code is not need
		 */

		String starttime = gateway.getStarttime().trim();
		String stoptime = gateway.getStoptime().trim();
		if (starttime.equalsIgnoreCase("NA"))
			gateway.setStarttime("0");
		if (stoptime.equalsIgnoreCase("NA"))
			gateway.setStoptime("0");

		try {
			gateway.setShowOrderId(jgateway.isShowOrderId());
			saveDynamiccmd(gateway, gatewayPrev, jgateway.getId());
		} catch (Exception e) {
			e.getLocalizedMessage();
		}
		return gateway;
	}

	@Override
	public Gateway saveReturnGateway(Gateway gateway) {
		log.info("Entered into saveReturnGateway :: meid :" + gateway.getMeid());
		try {

			String assetGroupId = gateway.getAssetgroup() == null ? null : "'" + gateway.getAssetgroup().getId() + "'";
			String assetInformationId = gateway.getAssetinformation() == null ? null
					: "'" + gateway.getAssetinformation().getId() + "'";
			String extsensortype = gateway.getExtsensortype() == null ? null : "'" + gateway.getExtsensortype() + "'";
			String lastrptdatetime = gateway.getLastrptdatetime() == null ? null
					: "'" + gateway.getLastrptdatetime() + "'";
			String location = gateway.getLocation() == null ? null : "'" + gateway.getLocation() + "'";
			String owner = gateway.getOwner() == null ? null : "'" + gateway.getOwner() + "'";
			boolean alive = gateway.isAlive();
			if(!gateway.isEnable())
				alive = false;
			String qry = " insert into gateway\r\n"
					+ "	(isalive, asset_id, assetgroup_id, assetinfo_id, calories_goal, carrier, cmp_id, default_goal, description, isenable, extsensortype, gatewayconfig, groups_id, installed_date, lastrptdatetime, location, macid, maxval, mdn, meid, minval, model_id, name, offsleeptime, onoffstatus, onsleeptime, owner, passwordtype, qrcode, sensorenable, show_order_id, starttime, stopreport, stoptime, id) \r\n"
					+ "	values\r\n" + "	(" + alive + ", '" + gateway.giveAsset().getId() + "', "
					+ assetGroupId + "," + assetInformationId + "," + "'" + gateway.getCalories_goal() + "', '"
					+ gateway.getCarrier() + "', '" + gateway.getCompany().getId() + "', '" + gateway.getDefault_goal()
					+ "', '" + gateway.getDescription() + "', " + gateway.isEnable() + "," + extsensortype + "," + "'"
					+ gateway.getGatewayConfig() + "', '" + gateway.getGroups().getId() + "', '"
					+ gateway.getInstalled_date() + "'," + lastrptdatetime + "," + location + "," + "'"
					+ gateway.getMacid() + "', '" + gateway.getMaxTemp() + "', '" + gateway.getMdn() + "', '"
					+ gateway.getMeid() + "', '" + gateway.getMinTemp() + "', '" + gateway.getModel().getId() + "', '"
					+ gateway.getName() + "', '" + gateway.getOffSleepTime() + "', " + gateway.isOnOffStatus() + ", '"
					+ gateway.getOnSleepTime() + "', " + owner + "," + "'" + gateway.getPasswordtype().getId() + "', '"
					+ gateway.getQrcode() + "', '" + gateway.getSensorenable() + "', " + gateway.isShowOrderId() + ", '"
					+ gateway.getStarttime() + "', " + gateway.isStopreport() + ", '" + gateway.getStoptime() + "', '"
					+ gateway.giveAsset().getId() + "')";

			int status = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("return login :: save or update return gateway status : " + status);

//			gateway = (Gateway) this.sessionFactory.getCurrentSession().merge(gateway);
			return gateway;
		} catch (Exception e) {
			log.error("Error in saveReturnGateway : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<String> getMeidsList(Gateway exGatewayInDB) {
		log.info("Entered into getMeidsLike :: meid :" + exGatewayInDB.getMeid());
		try {
			String meid = exGatewayInDB.getMeid();
			meid = "%" + (meid.substring(1, meid.toString().length()));
			String qry = "SELECT meid FROM gateway WHERE meid LIKE '" + meid + "' ORDER BY meid ASC;";
			List<String> rpt = this.sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if (rpt.isEmpty()) {
				return null;
			}
			return rpt;
		} catch (Exception e) {
			log.error("Error in getMeidsLike : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<String> getOldAssertList(Asset asset) {
		try {
			log.info("Entered into getOldAssertList :: Assetaddress :" + asset.getAssetaddress());
			String assetaddr = asset.getAssetaddress();
			assetaddr = "%" + (assetaddr.substring(1, assetaddr.toString().length()));
			String qry = "SELECT assetaddr FROM asset WHERE assetaddr LIKE '" + assetaddr + "' ORDER BY assetaddr ASC;";
			List<String> rpt = this.sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if (rpt.isEmpty()) {
				return null;
			}
			return rpt;
		} catch (Exception e) {
			log.error("Error in getMeidsLike  : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean changeOldAssertDetailsByAddr(String meid, String changedMeid) {
		log.info("Entered into changeOldAssertDetailsByAddr :: asset address : " + meid);
		Session session = sessionFactory.getCurrentSession();
		try {
			String updateQry = "UPDATE asset SET assetaddr='" + changedMeid + "' WHERE assetaddr='" + meid + "'";
			int update = session.createSQLQuery(updateQry).executeUpdate();

			return (update > 0 ? true : false);
		} catch (Exception e) {
			log.error("Error in changeOldAssertDetailsByAddr :: Error :  " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public AssetModel getAssetModelByMeid(String imei) {
		log.info(" Entered GatewayDao :: getAssetModelByMeid ");
		Session session = sessionFactory.getCurrentSession();
		try {

			String qry = "SELECT * FROM assetmodel WHERE" + " `id`=(SELECT `model_id` FROM `gateway` WHERE `meid` = '"
					+ imei + "');";
			Query query = session.createSQLQuery(qry).addEntity(AssetModel.class);
			AssetModel assetModel = (AssetModel) query.list().get(0);
			return assetModel;
		} catch (Exception e) {
			log.error("Error in getAssetModelByMeid :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public List<JGateway> checkDeviceExist(String meid) {
		log.info("Entered checkDeviceExist DAO Impl!");
		String qry = "select `id`,`description`,`isalive`,`isenable`,`meid`,`name`,`model_id`,`qrcode` from `gateway` where `meid` = '"
				+ meid + "' ";
		List<JGateway> jgatewaylist = null;

		try {
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> gatewayList = (List<Object[]>) query.list();

			if (!gatewayList.isEmpty()) {
				jgatewaylist = new ArrayList<JGateway>();
				for (Object[] gatewayObj : gatewayList) {
					JGateway jg = new JGateway();

					if ((gatewayObj[0]) != null)
						jg.setId(Long.valueOf(gatewayObj[0].toString()));
					if ((gatewayObj[1]) != null)
						jg.setDescription((String) gatewayObj[1]);
					if ((gatewayObj[2]) != null)
						jg.setAlive((Boolean) gatewayObj[2]);
					if ((gatewayObj[3]) != null)
						jg.setEnable((Boolean) gatewayObj[3]);
					if ((gatewayObj[4]) != null)
						jg.setMeid((String) gatewayObj[4]);
					if ((gatewayObj[5]) != null)
						jg.setName((String) gatewayObj[5]);
					if ((gatewayObj[6]) != null)
						jg.setModelid(Long.valueOf(gatewayObj[6].toString()));
					if ((gatewayObj[7]) != null)
						jg.setQrcode((String) gatewayObj[7]);

					jgatewaylist.add(jg);
				}
			}
		} catch (Exception e) {
			log.error("Exception in checkDeviceExist : ", e.getLocalizedMessage());
		}
		return jgatewaylist;
	}

	@Override
	public boolean checkQrcRegistered(String qrcode) {
		log.info(" Entered GatewayDao :: checkQrcExist ");
		try {
			Session ses = slave3SessionFactory.getCurrentSession();
			SQLQuery qry = ses.createSQLQuery(
					"SELECT G.qrcode FROM gateway G  JOIN usergateway UG ON G.id=UG.gatewayid  WHERE G.qrcode = '"
							+ qrcode + "'");
			List<Object[]> list = qry.list();
			if (list.size() > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error("Error in checkQrcExist :: Session Name : slave3SessionFactory :: error : "
					+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public long getGatewayId(String meid) {
		log.info("Entered getGatewayId : meid : " + meid);
		try {
			String qry = "select id from gateway where meid= :meid ;";
			List<BigInteger> gatewayIds = (List<BigInteger>) sessionFactory.getCurrentSession().createSQLQuery(qry)
					.setParameter("meid", meid).list();

			if (gatewayIds.size() > 0) {
				long id = gatewayIds.get(0).longValue();
				return id;
			} else {
				return 0;
			}

		} catch (Exception e) {
			log.error("Error in assignGatewayToLogin:" + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public boolean assignGatewayToLogin(String gid, long userId) {
		boolean isInserted = false;
		log.info("Entered assignGatewayToLogin:");
		try {
			String qry = "Insert into `usergateway` (`userId`, `gatewayId`) values(:userId,:gid);";
			int queryStatus = sessionFactory.getCurrentSession().createSQLQuery(qry).setParameter("userId", userId)
					.setParameter("gid", gid).executeUpdate();

			if (queryStatus > 0) {
				isInserted = true;
			} else {
				isInserted = false;
			}

		} catch (Exception e) {
			log.error("Error in assignGatewayToLogin:" + e.getLocalizedMessage());
		}
		return isInserted;
	}

	@Override
	public JQrcDetails getQrcDetails(String qrc) {
		log.info("Entered getQrcDetails:");
		JQrcDetails jQrcDetails = new JQrcDetails();

		try {
			String qry = "SELECT"
					+ " lgr.fota_ver ,"
					+ " am.id ,"
					+ " am.model,"
					+ " u.username,"
					+ " u.email,"
					+ " u.mobileno,"
					+ " u.firstname,"
					+ " u.lastname,"
					+ " g.meid, "
					+ " am.monitor_type_id,"
					+ " am.temp_alert,"
					+ " mt.display_name "
					+ " FROM gateway g "
					+ " LEFT JOIN lastgatewayreport lgr ON g.id=lgr.gateway_id"
					+ " JOIN assetmodel am ON g.model_id=am.id"
					+ " JOIN usergateway ug ON ug.gatewayId = g.id"
					+ " JOIN `user` u ON u.id = ug.userId"
					+ " JOIN monitortype mt ON mt.id=am.monitor_type_id "
					+ " WHERE g.qrcode='"
					+ qrc + "';";

			log.info("Query is " + qry);

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			Object[] result = (Object[]) query.list().get(0);

			if (result.length != 0) {
				if ((String) result[0] != null)
					jQrcDetails.setFota_version((String) result[0]);
				if ((String.valueOf(result[1])) != null)
					jQrcDetails.setNew_model_id(Long.parseLong(String.valueOf(result[1])));
				if ((String) result[2] != null)
					jQrcDetails.setNew_model_name((String) result[2]);
				if ((String) result[3] != null)
					jQrcDetails.setUser_name((String) result[3]);
				if ((String) result[4] != null)
					jQrcDetails.setEmail((String) result[4]);
				if ((String) result[5] != null)
					jQrcDetails.setPhone_no((String) result[5]);

				String name = "NA";
				if ((String) result[6] != null)
					name = (String) result[6];

				if ((String) result[7] != null) {
					if (name.equalsIgnoreCase("NA"))
						name = (String) result[7];
					else {
						String lname = (String) result[7];
						if (!lname.equalsIgnoreCase("NA"))
							name += " " + lname;
					}
				}
				jQrcDetails.setCx_name(name);
				
				if ((String) result[8] != null)
					jQrcDetails.setMeid((String) result[8]);
				if (result[9] != null) {
					if (((BigInteger) result[9]).intValue() == 4 && (boolean) result[10]) {
						jQrcDetails.setDevice_name("WaggleCam Ultra");
					} else {
						jQrcDetails.setDevice_name((String) result[11]);
					}
				}
					
			}
		} catch (Exception e) {
			log.error("Error in getQrcDetails:" + e.getLocalizedMessage());
		}
		return jQrcDetails;
	}

	@Override
	public JQrcDetails getOldModelNameandModelId(String deviceModelNo) {

		log.info("Entered getModel_Name: " + deviceModelNo);

		try {
			String model_name = "NA";
			long model_id = 0;

			String qry = "SELECT model, id FROM assetmodel WHERE inventorymodelname = '" + deviceModelNo + "'";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			List<?> resultList = query.list();
			if (!resultList.isEmpty()) {
				Object[] resultArray = (Object[]) resultList.get(0);
				model_name = String.valueOf(resultArray[0]);
				model_id = Long.parseLong(String.valueOf(resultArray[1]));

				JQrcDetails jqrcDetails = new JQrcDetails();
				jqrcDetails.setOld_model_name(model_name);
				jqrcDetails.setOld_model_id(model_id);

				return jqrcDetails;
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("Error in getModel_Name:" + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean isUserRegistered(String qrc) {
		log.info("Entered isUserRegistered:  " + qrc);

		try {
			String qry = "SELECT UG.userid FROM usergateway UG JOIN gateway G ON UG.gatewayid = G.id WHERE G.qrcode = :qrc";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).setParameter("qrc", qrc);

			return query.uniqueResult() != null;

		} catch (Exception e) {
			log.error("Error in isUserRegistered:" + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public long getOtpByEmail(String email) {
		log.info("Entered getOtpByEmail: " + email);

		try {
			String sql = "SELECT otp FROM email_verify_otp WHERE email = :email";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(sql);
			query.setParameter("email", email);

			List<Object> result = query.list();

			if (!result.isEmpty()) {
				return Long.parseLong(String.valueOf(result.get(0)));
			}
		} catch (Exception e) {
			log.error("Error in getOtpByEmail: " + e.getLocalizedMessage());
		}
		return -1;
	}

	@Override
	public boolean createOrResetGatewayStatus(long gateway_id, String meid, String serial_number,long mtype,int isRecall) {
		log.info("Entered into createOrResetGatewayStatus :: gateway_id : " + gateway_id + " :: meid : " + meid
				+ " :: serial_number : " + serial_number);
		try {
			String CurrentTime = _helper.getCurrentTimeinUTC();

			String qry = "SELECT GS.id,GS.created_on FROM gateway_status GS WHERE meid = '" + meid + "'";

			GatewayStatus gatewayStatus = new GatewayStatus();
			gatewayStatus.setGateway_id(gateway_id);
			gatewayStatus.setMeid(meid);
			gatewayStatus.setUpdated_on(CurrentTime);
			gatewayStatus.setStream_url("NA");
			gatewayStatus.setSerial_number(serial_number);
			if(mtype == 8){
				gatewayStatus.setNotification(true);
                gatewayStatus.setMotion_detection_person(true);
			}

			if(mtype == 12){
				gatewayStatus.setNotification(true);
				gatewayStatus.setMotion_detection(true);
			}

			if(isRecall == 1){
				qry = "SELECT GS.id,GS.created_on FROM gateway_status GS WHERE gateway_id = '" + gateway_id + "'";
				gatewayStatus.setDevice_unique_id("NA");
				updateReplaceMearikey(gateway_id);
			}
			List res = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if (!res.isEmpty() && res.size() > 0) {
				Object[] obj = (Object[]) res.get(0);
				gatewayStatus.setId((((BigInteger) obj[0])).longValue());

				// need to discuss
				// gatewayStatus.setCreated_on(_helper.convertTimeStampToDateTime((Timestamp)
				// obj[1]));
				gatewayStatus.setCreated_on(CurrentTime);
				sessionFactory.getCurrentSession().merge(gatewayStatus);
				return true;
			} else {
				gatewayStatus.setCreated_on(CurrentTime);
				sessionFactory.getCurrentSession().merge(gatewayStatus);
				return true;
			}
		} catch (Exception e) {
			log.error("Error in createOrResetGatewayStatus :: Error : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public List<String> getDistinctProductCategory() {
		log.info("Entered getMonitorList ::");
		List<String> monitorList = new ArrayList<String>();
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria criteria = session.createCriteria(MonitorType.class)
					.setProjection(Projections.projectionList().add(Projections.groupProperty("category")));
			monitorList = criteria.list();
			return monitorList;
		} catch (Exception e) {
			log.error("Error in getMonitorList : " + e.getLocalizedMessage());
		}
		return monitorList;
	}

	@Override
	public JGatewayToAlexa getJGatewayToAlexa(String user_id) {
		log.info("Entered into getJGatewayToAlexa :: user_id : " + user_id);
		try {

			String qry = "SELECT GA.user_id, GA.gateway_id, G.meid FROM gateway_to_alexa GA "
					+ " JOIN usergateway UG ON UG.gatewayId = GA.gateway_id " + " JOIN user U ON U.id = UG.userId "
					+ " JOIN gateway G ON G.id = GA.gateway_id " + " WHERE UG.userId = :user_id";

			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", user_id);
			query.setResultTransformer(new AliasToBeanResultTransformer(JGatewayToAlexa.class));
			query.addScalar("user_id", new LongType());
			query.addScalar("gateway_id", new LongType());
			query.addScalar("meid", new StringType());

			List<JGatewayToAlexa> gatewayToAlexaList = query.list();

			if (gatewayToAlexaList.isEmpty()) {
				log.info("gateway_to_alexa or usergateway not found for user_id : " + user_id);
				return null;
			}

			return gatewayToAlexaList.get(0);

		} catch (Exception e) {
			log.error("Error in getJGatewayToAlexa :: Error : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public GatewayStatus getGatewayStatusByMeid(String meid) {
		log.info("Entered into getGatewayStatusByMeid :: MEID : " + meid);
		try {

			List gatewayStatusList = sessionFactory.getCurrentSession().createCriteria(GatewayStatus.class)
					.add(Restrictions.eq("meid", meid)).list();

			if (gatewayStatusList == null || gatewayStatusList.isEmpty()) {
				log.info("gateway status not found for meid : " + meid);
				return null;
			}

			return (GatewayStatus) gatewayStatusList.get(0);

		} catch (Exception e) {
			log.error("Error in getGatewayStatusByMeid :: Error : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean updateGatewayNameByPetId(String name, long profileId) {
		log.info(" Entered into updateGatewayNameByPetId :: gateway_name : " + name + " :: pet_profile_id : "
				+ profileId);
		boolean isSuccess = false;
		try {
			Session session = sessionFactory.getCurrentSession();

			String hql = "UPDATE gateway SET name = :name WHERE id IN "
					+ "(SELECT gateway_id FROM gateway_profile WHERE petprofile_id = :profileId)";

			Query query = session.createSQLQuery(hql);
			query.setParameter("name", name);
			query.setParameter("profileId", profileId);
			int status = query.executeUpdate();
			isSuccess = true;
			return isSuccess;

		} catch (Exception e) {
			log.error("Error in updateGatewayNameByPetId :: Error : " + e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public double getPetFoodCaloriesByProfileId(long profileId) {
		log.info("Entered getPetFoodCaloriesByProfileId: " + profileId);

		try {
			String sql = "SELECT pf.calories FROM pet_feed_details pfd JOIN pet_food pf ON pf.id = pfd.pet_food_id WHERE gateway_id IN (SELECT gateway_id FROM gateway_profile WHERE petprofile_id = :profileId)";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(sql);
			query.setParameter("profileId", profileId);

			List<Object> result = query.list();

			if (!result.isEmpty()) {
				return Double.parseDouble(String.valueOf(result.get(0)));
			}
		} catch (Exception e) {
			log.error("Error in getPetFoodCaloriesByProfileId: " + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public List<JAlertRangeType> getJAlertRangeType(String country) {
		List<JAlertRangeType> jcfgList = new ArrayList<JAlertRangeType>();
		try {

			String qry = "SELECT ar.id AS id, ar.name AS name ,ar.description AS description, ar.alerttype_id,ar.maxval,ar.minval FROM alert_range_type ar WHERE ar.country = '"
					+ country + "';";

			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			if (!res.isEmpty()) {
				long id = 0;
				for (Object[] gcObj : res) {

					id = ((BigInteger) gcObj[0]).longValue();
					long alerttype = ((BigInteger) gcObj[3]).longValue();

					JAlertRangeType jGC = new JAlertRangeType();
					jGC.setId(id);
					jGC.setName((String) gcObj[1]);
					jGC.setDescription((String) gcObj[2]);
					jGC.setAlerttype_id(alerttype);
					jGC.setMaxval((float) gcObj[4]);
					jGC.setMinval((float) gcObj[5]);
					jcfgList.add(jGC);

				}

			}
		} catch (Exception e) {
			log.error("Error in getJAlertRangeType :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return jcfgList;
		}
		return jcfgList;
	}

	public boolean delGatewayStatus(long gateway_id) {
		log.info("Entered delGatewayStatus: " + gateway_id);
		int resultVal = 0;
		try {
			String qry = "DELETE FROM gateway_status WHERE gateway_id=" + gateway_id + ";";
			resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("Error in delGatewayStatus : " + e.getLocalizedMessage());
		}

		return (resultVal == 1) ? true : false;
	}

	public boolean delGatewayProfile(long gateway_id) {
		log.info("Entered delGatewayStatus: " + gateway_id);
		int resultVal = 0;
		try {
			String qry = "DELETE FROM gateway_profile WHERE gateway_id=" + gateway_id + ";";
			resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("Error in delGatewayProfile : " + e.getLocalizedMessage());
		}

		return (resultVal == 1) ? true : false;
	}

	public boolean delGateway(long gateway_id) {
		log.info("Entered delGatewayStatus: " + gateway_id);
		int resultVal = 0;
		try {
			String qry = "DELETE FROM gateway WHERE id=" + gateway_id + ";";
			resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("Error in delGateway : " + e.getLocalizedMessage());
		}

		return (resultVal == 1) ? true : false;
	}

	@Override
	public List<JMeariNotification> getMeariNotification() {
		List<JMeariNotification> jcfgList = new ArrayList<JMeariNotification>();
		try {

			String qry = "SELECT mr.id AS id, mr.title AS title ,mr.description AS description, mr.img_url FROM meari_notification mr;";

			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			if (!res.isEmpty()) {
				long id = 0;
				for (Object[] gcObj : res) {

					id = ((BigInteger) gcObj[0]).longValue();

					JMeariNotification jGC = new JMeariNotification();
					jGC.setId(id);
					jGC.setTitle((String) gcObj[1]);
					jGC.setDescription((String) gcObj[2]);
					jGC.setImg_url((String) gcObj[3]);
					jcfgList.add(jGC);

				}

			}
		} catch (Exception e) {
			log.error("Error in getMeariNotification :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return jcfgList;
		}
		return jcfgList;
	}

	@Override
	public boolean delAsset(String meid) {
		log.info("Entered delAsset: " + meid);
		int resultVal = 0;
		try {
			String qry = "DELETE FROM asset WHERE assetaddr='" + meid + "';";
			resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
		} catch (Exception e) {
			log.error("Error in delAsset : " + e.getLocalizedMessage());
		}

		return (resultVal == 1) ? true : false;
	}

	@Override
	public JResponse getInvalidPacket(String otype, long offset, long limit, String from_date, String to_date,
			String type, JResponse response) {
		ArrayList<InvalidPacket> packet = new ArrayList<InvalidPacket>();
		log.info("Entered :: OptimizedDoaImpl :: getInvalidPacket:: ");
		try {
			Session session = sessionFactory.getCurrentSession();

			// Get Total count From Database
			String sub_query = "", count_sub_query = "";
			String selectQuery = "select rawdata, ip_address, createdon, type,meid from invalid_packet ";
			String countQuery = "SELECT count(id) FROM `invalid_packet` ";

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA")) {
				sub_query = "where createdon between :from_date and :to_date ";
			}
			if (!type.equalsIgnoreCase("NA") && !type.trim().isEmpty()) {
				if (sub_query.trim().isEmpty())
					sub_query = "where type = :type ";
				else
					sub_query += " AND type = :type ";
			}

			count_sub_query = sub_query;

			sub_query += "ORDER BY createdon " + otype + " limit " + limit * (offset - 1) + "," + limit;

			Query countQry = session.createSQLQuery(countQuery + count_sub_query);
			Query listQry = session.createSQLQuery(selectQuery + sub_query);

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA")) {
				countQry.setParameter("from_date", from_date);
				countQry.setParameter("to_date", to_date);
				listQry.setParameter("from_date", from_date);
				listQry.setParameter("to_date", to_date);
			}
			if (!type.equalsIgnoreCase("NA") && !type.trim().isEmpty()) {
				countQry.setParameter("type", type);
				listQry.setParameter("type", type);
			}

			try {
				List<BigInteger> countRes = countQry.list();
				long totalPacket = Long.valueOf(((BigInteger) countRes.get(0)) + "");

				response.put("TotalPackets", totalPacket);
				response.put("offset", offset);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Error occur While Get totalPacket .");
				response.put("Error", e.getLocalizedMessage());
				log.error("Error occur While Get totalPacket : " + e.getMessage());
			}

			List<Object[]> listRes = (List<Object[]>) listQry.list();

			for (int i = 0; i < listRes.size(); i++) {
				InvalidPacket pac = new InvalidPacket();
				Object[] obj = (Object[]) listRes.get(i);

				if (obj[0] != null)
					pac.setPacket((String) obj[0]);
				if (obj[1] != null)
					pac.setIp_address((String) obj[1]);
				if (obj[2] != null) {
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String dt = dateFormat.format(new Date());
					pac.setCreated_on(dt);
				}
				if (obj[3] != null)
					pac.setType((String) obj[3]);
				if (obj[4] != null) {
					if (!((String) obj[4]).equals("0"))
						pac.setMeid((String) obj[4]);
				}

				packet.add(pac);
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("packetlist", packet);

		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occur while getting invalid packet.");
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occur while getting invalid packet.");
			response.put("Error", e.getLocalizedMessage());
			log.error("Error occur While Get totalPacket : " + e.getMessage());
		}
		return response;
	}

	@Override
	public ArrayList<InvalidPacket> getInvalidPacketExport(String from_date, String to_date, String type) {
		ArrayList<InvalidPacket> packet = new ArrayList<InvalidPacket>();
		log.info("Entered :: OptimizedDoaImpl :: getInvalidPacketExport:: ");
		try {
			Session session = sessionFactory.getCurrentSession();

			// Get Total count From Database
			String sub_query = "";
			String selectQuery = "select rawdata, ip_address, createdon, type,meid from invalid_packet ";

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA")) {
				sub_query = "where createdon between :from_date and :to_date ";
			}
			if (!type.equalsIgnoreCase("NA") && !type.trim().isEmpty()) {
				if (sub_query.trim().isEmpty())
					sub_query = "where type = :type ";
				else
					sub_query += " AND type = :type ";
			}

			Query listQry = session.createSQLQuery(selectQuery + sub_query);

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA")) {
				listQry.setParameter("from_date", from_date);
				listQry.setParameter("to_date", to_date);
			}
			if (!type.equalsIgnoreCase("NA") && !type.trim().isEmpty())
				listQry.setParameter("type", type);

			List<Object[]> listRes = (List<Object[]>) listQry.list();

			for (int i = 0; i < listRes.size(); i++) {
				InvalidPacket pac = new InvalidPacket();
				Object[] obj = (Object[]) listRes.get(i);

				if (obj[0] != null)
					pac.setPacket((String) obj[0]);
				if (obj[1] != null)
					pac.setIp_address((String) obj[1]);
				if (obj[2] != null) {
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String dt = dateFormat.format(new Date());
					pac.setCreated_on(dt);
				}
				if (obj[3] != null)
					pac.setType((String) obj[3]);
				if (obj[4] != null) {
					if (!((String) obj[4]).equals("0"))
						pac.setMeid((String) obj[4]);
				}

				packet.add(pac);
			}

		} catch (Exception e) {
			log.error("Error occur While Get totalPacket : " + e.getMessage());
		}
		return packet;
	}

	@Override
	public Map getInvalidFieldMeidCount(String from_date, String to_date) {
		log.info("Entered getInvalidFieldMeidCount");
		Map count = new HashMap();
		try {
			String selectQuery = "SELECT meid, COUNT(id) FROM invalid_packet where meid != 'NA' AND meid != '0' ";

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA"))
				selectQuery += "AND createdon between :from_date and :to_date ";
			
			selectQuery += "GROUP BY meid;";
			
			Query listQry = sessionFactory.getCurrentSession().createSQLQuery(selectQuery);

			if (!from_date.equalsIgnoreCase("NA") && !to_date.equalsIgnoreCase("NA")) {
				listQry.setParameter("from_date", from_date);
				listQry.setParameter("to_date", to_date);
			}
			
			List<Object[]> listRes = (List<Object[]>) listQry.list();

			for (int i = 0; i < listRes.size(); i++) {
				Object[] obj = (Object[]) listRes.get(i);
				
				if (obj[0] != null && obj[1] != null)
					count.put((String) obj[0], ((BigInteger) obj[1]).longValue());
			}
			
		} catch (Exception e) {
			log.error("Error occur While Get getInvalidFieldMeidCount : " + e.getMessage());
		}
		return count;
	}

	@Override
	public AssetModel getAssetModelByGatewayId(long gateway_id) {
		log.info("Entered getAssetModelByGatewayId");
		try {
			String qry = "SELECT * FROM assetmodel WHERE" + " `id`=(SELECT `model_id` FROM `gateway` WHERE `id`=:id );";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(AssetModel.class)
					.setParameter("id", gateway_id);
			AssetModel assetModel = (AssetModel) query.list().get(0);
			return assetModel;
		} catch (Exception e) {
			log.error("Error occur in getAssetModelByGatewayId : " + e.getMessage());
		}
		return null;
	}

	@Override
	public boolean updateuseremaildetails(String from_email, String to_email) {
		log.info(" Entered updateuseremaildetails :: updateuseremaildetails ");

		try {

			String qry = "UPDATE user SET email =:to_email, username =:to_email, authkey =:authKey WHERE username =:from_email ;";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry)
					.setParameter("to_email", to_email).setParameter("from_email", from_email)
					.setParameter("authKey", _helper.encryptAndSetUser(to_email.trim())).executeUpdate();

//			String qryAlert = " UPDATE alertcfg SET emailids = REPLACE(emailids, :from_email , :to_email ) "
//					+ "WHERE emailids LIKE CONCAT('%', :from_email, '%');";
//			int resultValAlert = this.sessionFactory.getCurrentSession().createSQLQuery(qryAlert)
//					.setParameter("to_email", to_email).setParameter("from_email", from_email).executeUpdate();
//
//			String qryAlertWc = " UPDATE alert_cfg_wc SET email_ids = REPLACE(email_ids, :from_email , :to_email ) "
//					+ "WHERE email_ids LIKE CONCAT('%', :from_email, '%');";
//			int resultValAlertWc = this.sessionFactory.getCurrentSession().createSQLQuery(qryAlertWc)
//					.setParameter("to_email", to_email).setParameter("from_email", from_email).executeUpdate();

			String qryChargebee = "UPDATE all_chargebee_subscription SET billing_email =:to_email WHERE billing_email =:from_email ;";
			int resultValChargebee = this.sessionFactory.getCurrentSession().createSQLQuery(qryChargebee)
					.setParameter("to_email", to_email).setParameter("from_email", from_email).executeUpdate();

			return (resultVal == 1) ? true : false;
		} catch (Exception e) {
			log.error("Error occur in getAssetModelByGatewayId : " + e.getMessage());
			return false;
		}
	}
	
	@Override
	public JGatewaySensorType getSensorDetailByGatewayId(long gateway_id) {

		log.info("getSensorDetailByGatewayId: " + gateway_id);
		JGatewaySensorType jqrcDetails = new JGatewaySensorType();
		try {

			String qry = "SELECT G.id, G.sensor_type_id,ST.sensortype,AM.monitor_type_id FROM gateway G "
					+ "JOIN sensortype ST ON ST.id = G.sensor_type_id "
					+ "JOIN assetmodel AM ON AM.id = G.model_id WHERE G.id =:gateway_id ";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry).setParameter("gateway_id", gateway_id);

			List<?> resultList = query.list();
			if (!resultList.isEmpty()) {
				Object[] resultArray = (Object[]) resultList.get(0);

				
				long gatewayId = ((BigInteger) resultArray[0]).longValue();
				long sensId = ((Integer) resultArray[1]).longValue();
				long monitorId = ((BigInteger) resultArray[3]).longValue();
				jqrcDetails.setGatewayId(gatewayId);
				jqrcDetails.setSensorName((String) resultArray[2]);
				jqrcDetails.setSensorId(sensId);
				jqrcDetails.setMonitorId(monitorId);

				return jqrcDetails;
			} else {
				return jqrcDetails;
			}
		} catch (Exception e) {
			log.error("Error in getSensorDetailByGatewayId:" + e.getLocalizedMessage());
			return jqrcDetails;
		}
	
		
	}
	
	@Override
	public boolean updateGatewayFotoversion(String verVal, String gatewayId) {
		log.info(" Entered GatewayDao :: updateGatewayFotoversion ");
		boolean isSuccess = false;
		try {

			log.info("Update Gateway function");
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE Gateway set firmware_ver = :firmver " + "WHERE id = :id";
			Query query = session.createQuery(hql);
			query.setParameter("firmver", verVal);
			query.setParameter("id", Long.parseLong(gatewayId));
			query.executeUpdate();
			isSuccess = true;
			return isSuccess;

		} catch (Exception e) {
			log.error("Error in updateGatewayFotoversion :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public double[] getDeviceLatLon(long gateway_id) {
		log.info(" Entered getDeviceLatLon :"+gateway_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT LGR.lat,LGR.latdir,LGR.lon,LGR.londir,"
					+ "LGR.lastvalidlat,LGR.lastvalidlatdir,LGR.lastvalidlon,LGR.lastvalidlondir "
					+ "FROM lastgatewayreport LGR "
					+ "JOIN gateway G ON G.id=LGR.gateway_id "
					+ "JOIN assetmodel AM ON AM.id=G.model_id AND AM.isgps=1 "
					+ "WHERE LGR.gateway_id = :id AND LGR.`datetime`!='2019-01-01 00:00:00';";
			Query query = session.createSQLQuery(qry);
			query.setParameter("id", gateway_id);
			List<Object[]> objList = query.list();
			if (objList != null && !objList.isEmpty()) {
				Object[] obj = objList.get(0);
				double lat = (double) obj[0];
				String latDir = (String) obj[1];
				double lon = (double) obj[2];
				String lonDir = (String) obj[3];
				double lastValidLat = (double) obj[4];
				String lastValidLatDir = (String) obj[5];
				double lastValidLon = (double) obj[6];
				String lastValidLonDir = (String) obj[7];
				
				if ((lat == 270.0 || lon == 270.0) && (lastValidLat != 32.89146560 || lastValidLon != 117.15064370)) {
					lat = lastValidLat;
					lon = lastValidLon;
					latDir = lastValidLatDir;
					lonDir = lastValidLonDir;
				}
				
				if (latDir.contains("S")) {
					lat = -lat;
				}
				if (lonDir.contains("W")) {
					lon = -lon;
				}
				
				double latLon[] = new double[2];
				latLon[0] = lat;
				latLon[1] = lon;
				return latLon;				
			}

		} catch (Exception e) {
			log.error("Error in getDeviceLatLon : " + e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public boolean createSolarcamSubscription(long gateway_id, long userid, long mType) {
		log.info(" Entered createSolarcamSubscription :"+gateway_id);
		try {
	
		Session ses = sessionFactory.getCurrentSession();
		String sqlQry = "SELECT feature_id,PF.`enable`,txn_limit,F.feature_code,pl.id,PF.extra_txn_limit FROM "
				+ " plan_feature PF JOIN feature F ON F.id=PF.feature_id "
				+ " JOIN plan pl ON PF.plan_id = pl.id and pl.monitor_type = "+mType+" and pl.enable = 1 and pl.is_freeplan = 1";

		SQLQuery reviewQry = ses.createSQLQuery(sqlQry);

		List<Object[]> reviewRes = reviewQry.list();
		
		String cur_date = IrisservicesUtil.getUtcDateTime();
		
		Calendar tdy = Calendar.getInstance();
		Calendar expiry = Calendar.getInstance();
		expiry.add(Calendar.DATE, 3);
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String strActivate = sdf.format(tdy.getTime());
		String strExpiry = sdf.format(expiry.getTime());

		for (Object[] result : reviewRes) {

			try {
				String insertQry = "INSERT INTO `gateway_feature`(`gateway_id`,`feature_id`,`enable`,`txn_limit`,`extra_txn_limit`,"
						+ "`resettype_id`,`remaining_limit`,`last_reset_on`,`feature_code`,plan_id,period_id,sub_id)"
					+ " VALUES('" + gateway_id + "','" + ((BigInteger) result[0]).longValue() + "','1','" + ((Integer) result[2]).intValue()
					+ "','" + ((Integer) result[5]).intValue() + "','1','" + ((Integer) result[2]).intValue() + "','"
					+ strActivate + "','" + (String) result[3] + "',"+((BigInteger) result[4]).longValue()+",1,'NA');";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
			}catch (Exception e) {
				log.error("Error in createSolarcamSubscription : " + e.getLocalizedMessage());
			}
		
		}
		String insertMeariQry = "INSERT INTO `meari_subscription`(`period_id`,`is_activated`,`activated_date`,`expiry_date`,`user_id`,`gateway_id`,`is_solarcam`)"
			+ " VALUES(1, 1, '"+strActivate+"', '"+strExpiry+"', "+userid+", "+gateway_id+",true);";

		int insertMeariCount = ses.createSQLQuery(insertMeariQry).executeUpdate();
		
		return true;
		
		}catch(Exception e) {
			log.error("Error in createSolarcamSubscription : " + e.getLocalizedMessage());
			return false;
		}
	}

        @Override
	public JGateway getJGatewayByGateway(long gatewayid) {
		log.info(" Entered GatewayDao :: getJGatewayByUser ");
		JGateway jgateway = new JGateway();
		String qry = "";
		Query qry_sql = null;

			
			qry = "SELECT G.id,G.name,G.meid,G.model_id,G.macid,G.qrcode,G.show_order_id,G.purchased_from_others,"
					+ "AM.monitor_type_id,AM.temp_alert FROM gateway G"
					+ " JOIN `assetmodel` AM ON AM.id=G.model_id  "
					+ " WHERE G.id =:gatewayid";
			qry_sql = slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			qry_sql.setParameter("gatewayid", gatewayid);

		try {
			List<Object[]> gatewayList = (List<Object[]>) qry_sql.list();

			if (!gatewayList.isEmpty()) {

					jgateway.setId(Long.valueOf(gatewayList.get(0)[0].toString()));
					jgateway.setName(gatewayList.get(0)[1].toString());
					jgateway.setMeid((String) gatewayList.get(0)[2]);
					jgateway.setModelid(Long.valueOf(gatewayList.get(0)[3].toString()));
					jgateway.setMacid(gatewayList.get(0)[4].toString());
					jgateway.setQrcode(gatewayList.get(0)[5].toString());
					jgateway.setShowOrderId((Boolean) gatewayList.get(0)[6]);
					jgateway.setPurchased_from_others((Boolean) gatewayList.get(0)[7]);
					jgateway.setMonitorTypeId(Long.valueOf(gatewayList.get(0)[8].toString()));// monitor_type_id
					jgateway.setTemp_alert((Boolean) gatewayList.get(0)[9]);// monitor_type_id

			}
		} catch (Exception e) {
			log.error("Error in getJGatewayByUser :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			return null;
		}

		return jgateway;
	}
        
    public Gateway getActiveGatewayByid(long id) {
		try {
			log.info(" Entered GatewayDao :: getGatewayByid ");
			String qry = "  SELECT * FROM gateway g join usergateway ug on ug.gatewayId = g.id WHERE g.id=" + id;
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(Gateway.class);
			Gateway gateway = (Gateway) query.list().get(0);
			return gateway;
		} catch (Exception e) {
			log.error("Error in getGatewayByid :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}

	}
    
	@Override
	public long getUserIdByGatewayId(long gateway_id) {
	    log.info("Entered into getUserIdByGatewayId : gatewayId : " + gateway_id);
	    long userId = 0;
	    try {
	        String qry = "SELECT UG.userId from usergateway UG WHERE UG.gatewayId =:gatewayId";
	        Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
	        query.setParameter("gatewayId", gateway_id);

	        List res = query.list();
	        if (!res.isEmpty()) {
	            userId = ((BigInteger) res.get(0)).longValue();
	        }
	    } catch (Exception e) {
	        log.error("Error while getting userid getUserIdByGatewayId: " + e.getLocalizedMessage());
	        userId = 0;
	    }
	    return userId;
	}
	
	public PlanToPeriod getPlanAndPeriodId(String planName) {
		log.info(" Entered in getPlanAndPeriodId :: DAO ");
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria cr = session.createCriteria(PlanToPeriod.class);
			cr.add(Restrictions.eq("chargebee_planid", planName));
			List<PlanToPeriod> list = (List<PlanToPeriod>) cr.list();

			if (list.isEmpty()) {
				return new PlanToPeriod();
			}
			return list.get(0);
		} catch (Exception e) {
			log.error("Error in getPlanAndPeriodId : " + e.getLocalizedMessage());
			return new PlanToPeriod();
		}
	}

	@Override
	public List<JPacketReport> getPacketReport(String meid, String startTime, int seconds) {

		log.info(" Entered in getPacketReport :: DAO ");
		List<JPacketReport> packetReportList = new ArrayList<>();
		try {
			Session session = sessionFactory.getCurrentSession();
			String query = "SELECT gateway_id, qrc, datetime, temperature, humidity, heat_index " +
					"FROM gatewayreport WHERE gateway_id " +
					"IN (SELECT id FROM gateway WHERE meid = :meid) AND `datetime` BETWEEN :start_time " +
					"AND DATE_ADD(:start_time, INTERVAL :seconds SECOND)";

			Query queryResult = session.createSQLQuery(query);
			queryResult.setParameter("meid", meid);
			queryResult.setParameter("start_time", startTime);
			queryResult.setParameter("seconds", seconds);

			List<Object[]> resultList = queryResult.list();

			for(Object[] row : resultList) {
				JPacketReport packetReport = new JPacketReport();

                if(row[0] != null) {packetReport.setGatewayId(((BigInteger) row[0]).longValue());}
				if(row[1] != null) {packetReport.setQrc((String) row[1]);}
				if(row[2] != null) {packetReport.setReportDatetime(((Timestamp) row[2]).toString());}
				if(row[3] != null) {packetReport.setTemperature((float) row[3]);}
				if(row[4] != null) {packetReport.setHumidity((float) row[4]);}
				if(row[5] != null) {packetReport.setHeatIndex((float) row[5]);}

                packetReportList.add(packetReport);
			}
		} catch (Exception e) {
			log.error("Error in getPacketReport : {}", e.getLocalizedMessage());
		}

		return packetReportList;
	}

@Override
	public boolean delGatewayAllProdSub(long gatewayId) {
		log.info("Entered delGatewayAllProdSub: " + gatewayId);
		int resultVal = 0;
		try {
			String qryChargebee = "UPDATE all_product_subscription SET gateway_id = 0 WHERE gateway_id =:gatewayId ;";
			resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qryChargebee)
					.setParameter("gatewayId", gatewayId).executeUpdate();

			return (resultVal == 1) ? true : false;
		} catch (Exception e) {
			log.error("Error in delGatewayAllProdSub : " + e.getLocalizedMessage());
		}

		return (resultVal == 1) ? true : false;
	}

	public List<JGateway> getJGatewayV1(String assetgroupid, String groupid, String subgroupid, String gatewayid,
									  long userid, String meid,String chargebeeId) {
		log.info(" Entered GatewayDao :: getJGatewayV1 ");
		List<JGateway> jGateways = new ArrayList<JGateway>();
		try {
			List<Gateway> gateways = this.getGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);

			for (Gateway g : gateways) {
				JGateway jg = this.convertGatewayintoJGateway(g);
				boolean mappedSub = false;
				List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(chargebeeId);
				if (subscription != null && g.getModel().getMonitor_type().getId() == 1) {
					mappedSub = true;
				}

				AllProductSubscription allProdSub = chargebeeService.getProductSubscriptionByGatewayId(g.getId(), chargebeeId, g.getModel().getMonitor_type().getId());
				if (allProdSub != null) {
					mappedSub = true;
				}
				jg.setMapped(mappedSub);
				jGateways.add(jg);
			}
			return jGateways;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			return null;
		}
	}

	public boolean updateReplaceMearikey(long gatewayId) {
		log.info(" Entered into updateReplaceMearikey :: gateway_id : " + gatewayId);
		boolean isSuccess = false;
		try {
			Session session = sessionFactory.getCurrentSession();

			Calendar tdy = Calendar.getInstance();
			tdy.add(Calendar.HOUR_OF_DAY, -1);

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String expiredDate = sdf.format(tdy.getTime());

			String hql = "UPDATE meari_subscription SET expiry_date = :expiredDate WHERE gateway_id = :gatewayId ;";

			Query query = session.createSQLQuery(hql);
			query.setParameter("gatewayId", gatewayId);
			query.setParameter("expiredDate",expiredDate);
			int status = query.executeUpdate();
			isSuccess = true;
			return isSuccess;

		} catch (Exception e) {
			log.error("Error in updateReplaceMearikey :: Error : " + e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public boolean enableOrDisableBtUpdate(String gatewayId, int bleEnable) {

		log.info(" Entered into enableOrDisableBtUpdate :: gateway_id : {}", gatewayId);
		boolean isSuccess = false;
		try {
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE gateway SET is_bt_update = :enable WHERE id = :gatewayId";

			Query query = session.createSQLQuery(hql);
			query.setParameter("gatewayId", gatewayId);
			query.setParameter("enable", bleEnable);

			query.executeUpdate();
			isSuccess = true;

			return isSuccess;
		} catch (Exception e) {
			log.error("Error in enableOrDisableBtUpdate :: Error : {}", e.getLocalizedMessage());
			return isSuccess;
		}
	}

	@Override
	public boolean updatePasscode(String gatewayId) {

		log.info(" Entered into updatePasscode :: gateway_id : {}", gatewayId);
		boolean isSuccess = false;
		try {
			String otp = IrisservicesUtil.generateOtp();
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE gateway SET update_passcode = :otp WHERE id = :gatewayId";

			Query query = session.createSQLQuery(hql);
			query.setParameter("gatewayId", gatewayId);
			query.setParameter("otp", otp);

			query.executeUpdate();
			isSuccess = true;
		} catch (Exception e) {
			log.error("Error in updatePasscode :: Error : {}", e.getLocalizedMessage());
		}

		return isSuccess;
	}

	@Override
	public boolean enableOrDisableFotaVersionMapping(String fotaVersionId, int enable, String currVersion) {

		log.info("Entered into enableOrDisableFotaVersionMapping :: fotaversion id : {}, currVersion: {}", fotaVersionId, currVersion);
		boolean isSuccess = false;

		try {
			Session session = sessionFactory.getCurrentSession();

			String disableOthersHql = "UPDATE fota_version_mapping SET is_bt_fota = 0 WHERE curr_version = :currVersion";
			Query disableQuery = session.createSQLQuery(disableOthersHql);
			disableQuery.setParameter("currVersion", currVersion);
			disableQuery.executeUpdate();

			if (enable == 1) {
				String enableHql = "UPDATE fota_version_mapping SET is_bt_fota = 1 WHERE id = :id";
				Query enableQuery = session.createSQLQuery(enableHql);
				enableQuery.setParameter("id", fotaVersionId);
				enableQuery.executeUpdate();
			}

			isSuccess = true;
		} catch (Exception e) {
			log.error("Error in enableOrDisableFotaVersionMapping :: Error : {}", e.getLocalizedMessage());
		}

		return isSuccess;
	}

	public boolean minicamaddresavilable(long userId) {
		log.info("Entered into minicamaddresavilable : gatewayId : " + userId);
		boolean avil = true;
		try {
			String qry = "SELECT UG.user_id from minicam_shipping_addr UG WHERE UG.user_id =:user_id";
			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userId);

			List res = query.list();
			if (res.isEmpty()) {
				return false;
			}
		} catch (Exception e) {
			log.error("Error while getting userid minicamaddresavilable: " + e.getLocalizedMessage());
			userId = 0;
		}
		return avil;
	}

	public String getUsernameByQrc(String qrcode) {
		log.info(" Entered GatewayDao :: getUsernameByQrc ");
		String email = null;
		try {
			Session ses = slave3SessionFactory.getCurrentSession();
			SQLQuery qry = ses.createSQLQuery(
					"SELECT U.username FROM gateway G  JOIN usergateway UG ON G.id=UG.gatewayid " +
							" JOIN user U ON U.id = UG.userid WHERE G.qrcode = '"
							+ qrcode + "'");
			List res = qry.list();
			if (!res.isEmpty()) {
				email = ((String) res.get(0));
			}
		} catch (Exception e) {
			log.error("Error in getUsernameByQrc :: Session Name : slave3SessionFactory :: error : "
					+ e.getLocalizedMessage());
		}
		return email;
	}
	@Override
	public boolean updateGatewaySkip(boolean isSkip, long gatewayId) {

			log.info(" Entered GatewayDao :: updateGatewaySkip ");
			boolean isSuccess = false;
			try {

				log.info("Update Gateway function");
				Session session = slave3SessionFactory.getCurrentSession();
				String hql = "UPDATE Gateway set warranty_skipped = :isSkip " + "WHERE id = :id";
				Query query = session.createQuery(hql);
				query.setParameter("isSkip", isSkip);
				query.setParameter("id", gatewayId);
				query.executeUpdate();
				isSuccess = true;
				return isSuccess;

			} catch (Exception e) {
				log.error("Error in updateGatewaySkip :: Session Name : sessionFactory :: error : "
						+ e.getLocalizedMessage());
				return isSuccess;
			}
	}

	public boolean saveWarrantyClaimType(String claimType, long gatewayId) {

		log.info(" Entered GatewayDao :: saveWarrantyClaimType :: gatewayId : {} ", gatewayId);
		boolean isSuccess = false;
		try {
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE gateway SET warranty_type = :claimType WHERE id = :gatewayId";

			Query query = session.createSQLQuery(hql);
			query.setParameter("claimType", claimType);
			query.setParameter("gatewayId", gatewayId);

			query.executeUpdate();
			isSuccess = true;
			log.info("Updated warranty claim type : {}", isSuccess);
		} catch (Exception e) {
			log.error("Error in saveWarrantyClaimType :: Error : {}", e.getLocalizedMessage());
		}

		return isSuccess;
	}

	@Override
	public boolean checkFotaVersionAvailable(long gatewayId, String meid) {

		log.info("Entered GatewayDao :: checkFotaVersionAvailable");

		boolean isFotaValid = false;
		try {
			Session session = sessionFactory.getCurrentSession();

			String sql1 = "SELECT fota_ver FROM iris.lastgatewayreport WHERE gateway_id = :gatewayId";
			Query query1 = session.createSQLQuery(sql1).setParameter("gatewayId", gatewayId);
			String fotaVersion = (String) query1.uniqueResult();

			if (fotaVersion == null || "NA".equals(fotaVersion)) {
				String sql2 = "SELECT curr_fota_version FROM niom.inventory WHERE meid = :meid";
				Query query2 = session.createSQLQuery(sql2).setParameter("meid", meid);
				fotaVersion = (String) query2.uniqueResult();
			}

			if (fotaVersion != null) {
				String sql3 = "SELECT COUNT(*) FROM fota_version_mapping WHERE curr_version = :fotaVersion AND is_bt_fota = 1";
				Query query3 = session.createSQLQuery(sql3).setParameter("fotaVersion", fotaVersion);
				Number count = (Number) query3.uniqueResult();
				isFotaValid = count.intValue() > 0;
			}

		} catch (Exception e) {
			log.error("Error in checkFotaVersionAvailable :: Error : {}", e.getLocalizedMessage());
		}

		return isFotaValid;
	}

	@Override
	public boolean checkFreeplanavilinmearidevice(long gatewayId, long userId) {
		log.info("Entered into getMeariKey :: userid : "+ userId);
		try {
			String qry = "SELECT gateway_id FROM meari_subscription where gateway_id=:gatewayid AND is_activated = 1 AND user_id=:userid ";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayid", gatewayId);
			query.setParameter("userid", userId);

			List<BigInteger> meariKeyList = (List<BigInteger>) query.list();

			if( meariKeyList.isEmpty() ) {
				log.info("There is no key found");
				return false;
			}

			return true;
		} catch (Exception e) {
			log.error("Error in getMeariKey :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean enableOrDisableMeariUpdate(String gatewayId, int btnEnable) {

			log.info(" Entered into enableOrDisableMeariUpdate :: gateway_id : {}", gatewayId);
			boolean isSuccess = false;
			try {
				Session session = sessionFactory.getCurrentSession();
				String hql = "UPDATE gateway SET meari_update_popup = :enable WHERE id = :gatewayId";

				Query query = session.createSQLQuery(hql);
				query.setParameter("gatewayId", gatewayId);
				query.setParameter("enable", btnEnable);

				query.executeUpdate();
				isSuccess = true;

				return isSuccess;
			} catch (Exception e) {
				log.error("Error in enableOrDisableMeariUpdate :: Error : {}", e.getLocalizedMessage());
				return isSuccess;
			}
	}

	@Override
	public void updatePurchaseMonthYear(long userId, long gatewayId, String purchasedMonthYear){

		log.info(" Entered into updatePurchaseMonthYear :: gateway_id : {}", gatewayId);
		try {
			Session session = sessionFactory.getCurrentSession();
			String sql = "UPDATE device_subscription SET purchase_date=:purchasedMonthYear WHERE gateway_id=:gateway_id";

			Query query = session.createSQLQuery(sql);
			query.setParameter("purchasedMonthYear", purchasedMonthYear+" 00:00:00");
			query.setParameter("gateway_id", gatewayId);
			query.executeUpdate();

			String sql1 = "UPDATE gateway SET purchased_month_year=:purchasedMonthYear WHERE id=:gateway_id";

			Query query1 = session.createSQLQuery(sql1);
			query1.setParameter("purchasedMonthYear", purchasedMonthYear);
			query1.setParameter("gateway_id", gatewayId);
			query1.executeUpdate();


		} catch (Exception e) {
			log.error("Error in enableOrDisableMeariUpdate :: Error : {}", e.getLocalizedMessage());

		}


	}

	public String getNewQrc(String oldQrc) {

		log.info("Entered into getNewQrc : oldQrc : {}", oldQrc);

		String newQrc = "NA";

        try {
            Session session = sessionFactory.getCurrentSession();

            String qry = "SELECT new_qrc from upgrade_device_history where old_qrc =:oldqrc";

            Query query = session.createSQLQuery(qry);

            query.setParameter("oldqrc", oldQrc);

            Object result = query.setMaxResults(1).uniqueResult();
			newQrc = result != null ? result.toString() : "NA";
        } catch (Exception e) {
           log.error("Error in getNewQrc :: Error : {}", e.getLocalizedMessage());
        }

		return newQrc;
    }
}
