package com.nimble.irisservices.dao.impl;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IAlexaDao;
import com.nimble.irisservices.dto.AlexaTokenSet;

@Repository
public class AlexaDaoImpl implements IAlexaDao {

	private static final Logger log = LogManager.getLogger(AlexaDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;
	
	@Value("${amazon.alexa.clientid}")
	private String alexaClientId;

	@Value("${amazon.alexa.clientsecret}")
	private String alexaClientSecret;
	
	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;
	
	@Value("${amazon.alexa.accesstoken_url}")
	private String alexaAccessTokenUrl;
	
	@Autowired
	private SessionFactory slave1SessionFactory;

	@Override
	public long getUserIdByAlexaId(String alexaId) {
		log.info("Entered :: AlexaDaoImpl :: getUserIdByAlexaId :: DAO");
		try {
			Query qry = sessionFactory.getCurrentSession()
					.createSQLQuery("SELECT user_id  FROM `user_alexa` WHERE  alexa_id = '" + alexaId + "'");

			List res = qry.list();
			if (res.size() > 0) {
				BigInteger userId = (BigInteger) res.get(0);

				log.info("userId " + userId + " AlexaId " + alexaId);
				return userId.longValue();
			} else {
				log.info("Invalid Alexa Id : " + alexaId);
			}

		} catch (IndexOutOfBoundsException e) {
			log.error("Invalid Alexa Id exception : " + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public void updateAlexaId(String userid, String alexaId) {
		log.info("Entered :: AlexaDaoImpl :: updateAlexaId:: DAO");
		try {
			Session ses = sessionFactory.getCurrentSession();
			SQLQuery qry = ses.createSQLQuery(
					"INSERT INTO user_alexa(alexa_id,user_id) VALUES('" + alexaId + "','" + userid + "');");
			int status = qry.executeUpdate();

			if (status > 0)
				log.info("Alexa_Id Updated to iris Database.");
				
		} catch (Exception e) {
			log.error("Error while Updating Alexa_Id to iris Database "+e.getLocalizedMessage());
		}
		return;
	}
	
	@Override
	public AlexaTokenSet getAlexaTokens(long userId) {
		log.info("Entered :: getAlexaTokens :: userId :: "+userId);
		AlexaTokenSet ats = new AlexaTokenSet();
		try {				
			Session session = slave1SessionFactory.getCurrentSession();
			String getTokensQry = "SELECT ATM.awsaccesstoken, ATM.awsrefreshtoken, ATM.token_type, ATM.expires_in, ATM.created_on, ATM.updated_on FROM alexatokensmanager ATM WHERE ATM.user_id='"+userId+"'";			
			List<Object> objList = session.createSQLQuery(getTokensQry).list();			
			if(objList.size()>0) {
				log.info("Got AlexaTokenSet");
				Object[] tknObj = (Object[]) objList.get(0);
				if(tknObj[0] != null) {
					byte[] byts = (byte[]) tknObj[0];
					ats.setAccessToken(new String(byts));
				}
				if(tknObj[1] != null) {
					byte[] byts = (byte[]) tknObj[1];
					ats.setRefreshToken(new String(byts));
				}
				if(tknObj[2] != null) {
					ats.setTokenType((String) tknObj[2]);
				}
				if(tknObj[3] != null) {
					ats.setExpiresIn(((BigInteger)tknObj[3]).longValue());
				}
				if(tknObj[4] != null) {
					ats.setCreatedOn((Timestamp) tknObj[4]);
				}
				if(tknObj[5] != null) {
					ats.setUpdatedOn((Timestamp) tknObj[5]);
				}
				
				Timestamp updatedOn = ats.getUpdatedOn();				
				Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());		
				long diffHours = TimeUnit.HOURS.convert(currentTimestamp.getTime() - updatedOn.getTime(), TimeUnit.MILLISECONDS);
				
				if(diffHours >= 1) {
					log.info("Obtain new pair of amazon tokens");
					ats = getNewAlexaTokens(ats.getRefreshToken(), userId);
				}				
			}else{
				log.info("No AlexaTokenSet found in database");
				return null;
			}
		}catch(Exception e) {
			log.error("Exception occured at saveORUpdateAlexaTokens : "+e.getLocalizedMessage());
		}
		return ats;
	}

	@Override
	public boolean insertAlexaTokens(AlexaTokenSet alexaTokenSet) {
		log.info("Entered :: insertAlexaTokens :: userId :: "+alexaTokenSet.getUserId());
		try {			
			Session session = sessionFactory.getCurrentSession();
			String insertQry = "INSERT INTO alexatokensmanager(awsaccesstoken,awsrefreshtoken,user_id,token_type,expires_in,created_on,updated_on) "
					+ "VALUES('"+alexaTokenSet.getAccessToken()+"','"+alexaTokenSet.getRefreshToken()+"','"+alexaTokenSet.getUserId()+"','"
					+ alexaTokenSet.getTokenType()+"','"+alexaTokenSet.getExpiresIn()+"','"+alexaTokenSet.getCreatedOn()+"','"+alexaTokenSet.getUpdatedOn()+"')";
			int modifications = session.createSQLQuery(insertQry).executeUpdate();
			if(modifications > 0)
				return true;
		}catch(Exception e) {
			log.error("Exception occured at insertAlexaTokens : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateAlexaTokens(AlexaTokenSet alexaTokenSet) {
		log.info("Entered :: updateAlexaTokens :: userId :: "+alexaTokenSet.getUserId());
		try {			
			Session session = sessionFactory.getCurrentSession();
			String updateTokenQry = "UPDATE alexatokensmanager SET awsaccesstoken='"+alexaTokenSet.getAccessToken()+"', awsrefreshtoken='"+alexaTokenSet.getRefreshToken()
			+"', token_type ='"+alexaTokenSet.getTokenType()+"', expires_in='"+alexaTokenSet.getExpiresIn()+"', updated_on='"+alexaTokenSet.getUpdatedOn()+"' WHERE user_id='"+alexaTokenSet.getUserId()+"'";
			int updatedCount = session.createSQLQuery(updateTokenQry).executeUpdate();
			if(updatedCount > 0) {
				return true;
			}
		}catch(Exception e) {
			log.error("Exception occured at updateAlexaTokens : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	public AlexaTokenSet getNewAlexaTokens(String refresh_token, long userId) {
		String authCode = "";
		log.info("Entered into getNewAlexaTokens : userId : "+userId );
		try {
			String urlParameters  = "grant_type=refresh_token"
					+ "&refresh_token="+refresh_token
					+ "&client_id="+alexaClientId
					+ "&client_secret="+alexaClientSecret  
					+ "&redirect_uri="+waggleUniversalUrl;
			byte[] postData = urlParameters.getBytes( StandardCharsets.UTF_8 );
			int postDataLength = postData.length;
			String alexaTokensEndpoint = alexaAccessTokenUrl;
			URL url = new URL(alexaTokensEndpoint);
			HttpURLConnection conn= (HttpURLConnection) url.openConnection();           
			conn.setDoOutput(true);
			conn.setInstanceFollowRedirects(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); 
			conn.setRequestProperty("charset", "utf-8");
			conn.setRequestProperty("Content-Length", Integer.toString(postDataLength ));
			conn.setUseCaches(false);
			try(DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
				wr.write( postData );
			}
			StringBuilder content;
			try (BufferedReader br = new BufferedReader(
					new InputStreamReader(conn.getInputStream()))) {
				String line;
				content = new StringBuilder();
				while ((line = br.readLine()) != null) {
					content.append(line);
					content.append(System.lineSeparator());
				}
			}
			log.info("Access token response from amazon : "+content.toString());
			org.json.JSONObject jobj = new org.json.JSONObject(content.toString());
			String accessToken = jobj.getString("access_token");
			long expiresIn = jobj.getLong("expires_in");
			String refreshToken = jobj.getString("refresh_token");
			String tokenType = jobj.getString("token_type");	
			String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT, IrisservicesConstants.UTCFORMAT);
			Timestamp currTimeStamp=IrisservicesUtil.getDateTime_TS(CurrentTime);

			AlexaTokenSet ats = new AlexaTokenSet(accessToken, refreshToken, userId);
			ats.setExpiresIn(expiresIn);
			ats.setTokenType(tokenType);
			ats.setUpdatedOn(currTimeStamp);

			boolean isUpdated = this.updateAlexaTokens(ats);
			log.info("isUpdated : "+isUpdated);
			
			return ats;
		}catch (Exception e) {
			log.error("Exception occured at getAwsAccessToken : "+e.getLocalizedMessage());
		}
		return null;
	}


}
