package com.nimble.irisservices.dao.impl;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IGatewayDaoV4;
import com.nimble.irisservices.dao.IWifiInfoDao;
import com.nimble.irisservices.dao.IWifiInfoDaoV4;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayPendingEvent;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.GatewayToAlexa;
import com.nimble.irisservices.entity.NightVisionMode;
import com.nimble.irisservices.entity.OldDevice;
import com.nimble.irisservices.entity.PlThresholdStatus;
import com.nimble.irisservices.entity.RemoveGatewayRequest;
import com.nimble.irisservices.entity.RemoveGatewayRequestHistory;
import com.nimble.irisservices.entity.RemoveGatewayType;
import com.nimble.irisservices.entity.TempCalibStatus;
import com.nimble.irisservices.entity.UpgradeDeviceHistory;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IWifiInfoServiceV4;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.criterion.Restrictions;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.type.LongType;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

@Repository
public class GatewayDaoImplV4 implements IGatewayDaoV4 {

	private static final Logger log = LogManager.getLogger(GatewayDaoImplV4.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	private SessionFactory slave1SessionFactory;

	@Autowired
	private SessionFactory slave3SessionFactory;

	@Autowired
	private SessionFactory slave4SessionFactory;

	@Autowired
	private SessionFactory slave5SessionFactory;

	@Autowired
	private SessionFactory niomSessionFactory;
	
	@Autowired
	IWifiInfoDao wifiService;
	
	@Autowired
	IWifiInfoDaoV4 wifiServiceV4;
	
	@Value("${remove_gateway_valid_hours_to_show}")
	private int remove_gateway_valid_hours_to_show;
	
	@Autowired
	Helper _helper;
	
	@Value("#{${supportcontactnumber}}")
	private Map<String, String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String, String> supportContactEmail;
	
	@Value("${wc_ssid_password}")
	private String wc_wifi_password;
	
	@Value("${wc.show.hd.all.freaquency}")
	private boolean wc_show_hd_all_freq;
	
	@Value("${path.prefix}")
	private String path_prefix;
	
	@Value("${path.postfix.device}")
	private String path_postfix_device;
	
	@Value("${wc_continuous_playback}")
	private boolean wc_continuous_playback;
	
	@Value("${minicam_plan_content}")
	private String minicam_plan_content;
	
	@Value("${minicam_btn_content}")
	private String minicam_btn_content;
	
	@Value("${wcpro_plan_content}")
	private String wcpro_plan_content;
	
	@Value("${wcpro_btn_content}")
	private String wcpro_btn_content;
	
	@Value("${wcultra_plan_content}")
	private String wcultra_plan_content;
	
	@Value("${wcultra_btn_content}")
	private String wcultra_btn_content;
	
	@Value("${start_video_recording_wowza_post_m}")
	private String start_video_recording_wowza_post_m;
	
	@Value("${start_video_recording_wowza_post_m_body}")
	private String start_video_recording_wowza_post_m_body;
	
	@Value("${stop_video_recording_wowza_put_m}")
	private String stop_video_recording_wowza_put_m;
	
	@Value("${wowza.api.admin.dns}")
	private String wowza_api_admin_dns;
	
	@Value("${wowza_video_record_passcode}")
	private String wowza_video_record_passcode;
	
	@Value("${hangup_battery_percentage}")
	private int hangup_battery_percentage;
	
	@Value("${reset_minits}")
	private int reset_minits;

	@Value("${show_smart_light_setting}")
	private boolean show_smart_light_setting;

	@Value("${solarcam_plan_content}")
	private String solarcam_plan_content;

	@Value("${solarcam_btn_content}")
	private String solarcam_btn_content;

	@Value("${solarminicam_plan_content}")
	private String solarminicam_plan_content;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;
	
	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;
	
	@Autowired
	@Lazy
	ICreditSystemService iCreditSystemService;
	
	@Autowired
	IAlertCfgServiceV4 alertcfgServiceV4;
	
	@Autowired
	IWifiInfoServiceV4 wifiInfoServiceV4;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Override
	public JGatewayDetails getJGatewayDetails(String key, String value) {
		log.info("Entered getGatewayDetails , " + key + " : " + value);
		try {
			Session sessionNew = sessionFactory.openSession();
			JGatewayDetails gateway = new JGatewayDetails();
			try {
				Transaction tx = sessionNew.beginTransaction();

				String qry = "SELECT G.id, G.carrier, G.description, G.extsensortype, G.isalive, G.isenable, G.location, G.mdn, G.meid, G.name, G.asset_id, "
						+ "G.assetgroup_id, G.cmp_id, G.group_id, G.model_id, G.subgroup_id, G.sensorenable, G.owner, G.assetinfo_id, G.groups_id, G.minval, G.maxval, "
						+ "G.lastrptdatetime, G.passwordtype, G.stopreport, G.starttime, G.stoptime, G.installed_date, G.qrcode, G.onoffstatus, G.gatewayconfig, G.onsleeptime, "
						+ "G.offsleeptime, G.default_goal, G.calories_goal FROM gateway G WHERE G." + key + "='" + value
						+ "';";

				List<Object[]> list = sessionNew.createSQLQuery(qry).list();

				if (list.size() > 0) {

					Object[] obj = list.get(0);

					if (obj[0] != null)
						gateway.setId(((BigInteger) obj[0]).longValue());
					if (obj[1] != null)
						gateway.setCarrier((String) obj[1]);
					if (obj[2] != null)
						gateway.setDescription((String) obj[2]);
					if (obj[3] != null)
						gateway.setExtsensortype((String) obj[3]);
					if (obj[4] != null)
						gateway.setAlive((boolean) obj[4]);
					if (obj[5] != null)
						gateway.setEnable((boolean) obj[5]);
					if (obj[6] != null)
						gateway.setLocation((String) obj[6]);
					if (obj[7] != null)
						gateway.setMdn((String) obj[7]);
					if (obj[8] != null)
						gateway.setMeid((String) obj[8]);
					if (obj[9] != null)
						gateway.setName((String) obj[9]);
					if (obj[10] != null)
						gateway.setAsset_id(((BigInteger) obj[10]).longValue());
					if (obj[11] != null)
						gateway.setAssetgroupid(((BigInteger) obj[11]).longValue());
					if (obj[12] != null)
						gateway.setCmp_id(((BigInteger) obj[12]).longValue());
					if (obj[13] != null)
						gateway.setGroupid(((BigInteger) obj[13]).longValue());
					if (obj[14] != null)
						gateway.setModelid(((BigInteger) obj[14]).longValue());
					if (obj[15] != null)
						gateway.setSubgroupid(((BigInteger) obj[15]).longValue());
					if (obj[16] != null)
						gateway.setSensorEnable((String) obj[16]);
					if (obj[17] != null)
						gateway.setOwner((String) obj[17]);
					if (obj[18] != null)
						gateway.setAssetinfo_id(((BigInteger) obj[18]).longValue());
					if (obj[19] != null)
						gateway.setGroups_id(((BigInteger) obj[19]).longValue());
					if (obj[20] != null)
						gateway.setMinval((float) obj[20]);
					if (obj[21] != null)
						gateway.setMaxval((float) obj[21]);
					if (obj[22] != null)
						gateway.setLastrptdatetime((Timestamp) obj[22]);
					if (obj[23] != null)
						gateway.setPasswordtype(((BigInteger) obj[23]).longValue());
					if (obj[24] != null)
						gateway.setStopreport((boolean) obj[24]);
					if (obj[25] != null)
						gateway.setStarttime((String) obj[25]);
					if (obj[26] != null)
						gateway.setStoptime((String) obj[26]);
					if (obj[27] != null)
						gateway.setInstalled_date((Timestamp) obj[27]);
					if (obj[28] != null)
						gateway.setQrcode((String) obj[28]);
					if (obj[29] != null)
						gateway.setOnoffstatus((boolean) obj[29]);
					if (obj[30] != null)
						gateway.setGatewayConfig((String) obj[30]);
					if (obj[31] != null)
						gateway.setOnsleeptime((String) obj[31]);
					if (obj[32] != null)
						gateway.setOffsleeptime((String) obj[32]);
					if (obj[33] != null)
						gateway.setDefault_goal(((BigInteger) obj[33]).longValue());
					if (obj[34] != null)
						gateway.setCalories_goal(((BigInteger) obj[34]).longValue());

				} else {
					log.info("No gateway found for : " + value);
				}

				tx.commit();
				return gateway;
			} finally {
				sessionNew.flush();
				sessionNew.close();
			}
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Exception ArrayIndexOutOfBoundsException occured @getGateway : " + e.getLocalizedMessage());
			return null;
		} catch (Exception e) {
			log.error("Exception occured @getGateway : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public JResponse getJPetprofilesByUserv4(long userid, long gatewayid, int monitortype) {
		long startTime1 = System.currentTimeMillis();

		JResponse resp = new JResponse();
		log.info("Entered :: OptimizedDoaImpl :: getJPetprofilesByUserv4:: ");
		List<HashMap<String, Object>> createpetprofileList = new ArrayList<HashMap<String, Object>>();
		List<HashMap<String, Object>> editpetprofileList = new ArrayList<HashMap<String, Object>>();
		HashMap<String, Object> petProf = new HashMap<String, Object>();
		// PetProfile petprofile;
		// Gateway gateway;
		JPetprofile jPetProfile = null;
		boolean createprofile = false;
		// this flag is to identify whether new device is available or not to create
		// profile -
		try {

			String query = "SELECT PP.id,PP.gateway_id,PP.name,PP.birth_date,PP.sex,PP.breed,PP.height,PP.weight,PP.remarks,PP.imageurl"
					+ ",PS.speciesname,PP.intact,PP.working,PP.user_id,PP.activitylevel,PP.enable,G.name AS gateway_name,G.qrcode,MT.id AS monitor_type_id FROM \r\n"
					+ "petspecies PS JOIN pet_profile PP \r\n" + " ON PP.speciesid= PS.id RIGHT JOIN  gateway G \r\n"
					+ " ON PP.gateway_id = G.id \r\n" + "JOIN assetmodel AM  ON G.model_id = AM.id \r\n"
					+ "JOIN monitortype MT ON MT.id = AM.monitor_type_id ";

			if (gatewayid > 0)
				query = query + "WHERE PP.enable=1 and G.id =" + gatewayid + " ";
			else
				query = query + "WHERE PP.enable=1 and G.id IN ( SELECT gatewayId FROM usergateway ug WHERE userId = "
						+ userid + " ) ";

			if (monitortype > 0)
				query = "AND MT.id =" + monitortype + " ;";

			Query qry = slave1SessionFactory.getCurrentSession().createSQLQuery(query);

			List res = qry.list();

			if (!res.isEmpty() && res.size() > 0) {

				for (int i = 0; i < res.size(); i++) {
					Object[] tuple = (Object[]) res.get(i);

					jPetProfile = convertToJPetprofilev4(tuple);

					petProf = new HashMap<String, Object>();
					petProf.put("gatewayid", (BigInteger) tuple[1]);
					petProf.put("gatewayname", (String) tuple[16]);
					petProf.put("qrc", (String) tuple[17]);
					petProf.put("monitortype", (BigInteger) tuple[18]);
					petProf.put("jPetProfile", jPetProfile);

					if (jPetProfile == null)
						createprofile = true;

					if (jPetProfile == null)
						createpetprofileList.add(petProf);
					else
						editpetprofileList.add(petProf);
				}

			} else {
				log.info("getJPetprofile: No gateways are mapped to this user id- " + userid);
			}

			resp.put("createprofile", createprofile);
			resp.put("Status", 1);
			resp.put("Msg", "success");
			resp.put("createpetlist", createpetprofileList);
			resp.put("editpetlist", editpetprofileList);

		} catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Msg", "Error occured");
			log.error("getJPetprofile: Exception: " + e.getMessage());
		}
		log.info(">> getJPetprofilesByUserV4 ElapsTime :@Daoimpl:  "
				+ ((System.currentTimeMillis() - startTime1) / 1000.0));
		;

		return resp;
	}

	private JPetprofile convertToJPetprofilev4(Object[] tuple) {
		log.info(" Entered GatewayDaoV4 :: convertToJPetprofilev4 ");
		JPetprofile jPetprofile = null;
		if (tuple[0] != null) {
			Date age = (Date) tuple[3];
			/*
			 * [long id,String gateway_id,String name,String age,String sex,String
			 * breed,String height, String weight,String remarks,String imageurl,String
			 * specieName,boolean intact, boolean working,long user_id, String
			 * activitylevel, boolean enable]
			 */

			jPetprofile = new JPetprofile(Long.parseLong(tuple[0] + ""), String.valueOf((BigInteger) tuple[1]),
					(String) tuple[2], getAge(age), (String) tuple[4], (String) tuple[5], (String) tuple[6],
					(String) tuple[7], (String) tuple[8], (String) tuple[9], (String) tuple[10], (boolean) tuple[11],
					(boolean) tuple[12], Long.parseLong(tuple[13] + ""), petSpeciesServicesv4.getFindActivitylevelVal(((String) tuple[14])), (boolean) tuple[15]);
		} else {
			log.info("convertToJPetprofile:Error:PetProfile.getGateway returned null");
		}

		return jPetprofile;
	}

	// Return the age from given birth_date stored in DB
	private String getAge(Date birth_date) {
		log.info(" Entered GatewayDaoV4 :: getAge ");
		String age = null;
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		Date curDate = currDateCal.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-mm-dd");
		String strCurrdate = sdf.format(curDate);
		try {
			curDate = sdf.parse(strCurrdate);
		} catch (ParseException e) {
			log.error("getAge: " + e.getMessage());
		}

		Calendar birthDateCal = Calendar.getInstance();
		birthDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		birthDateCal.setTime(birth_date);

		int diffYear = currDateCal.get(Calendar.YEAR) - birthDateCal.get(Calendar.YEAR);
		int monthsdiff = currDateCal.get(Calendar.MONTH) - birthDateCal.get(Calendar.MONTH);
		int totMonthsDiff = diffYear * 12 + monthsdiff;

		long diffdays = curDate.getTime() - birthDateCal.getTime().getTime();

		long yr = diffdays / 365;

		if (diffYear >= 1) {
			if (totMonthsDiff == 6)// ages <1 is statically stored in DB as 6
				// months
				age = "0";
			else
				age = String.valueOf(diffYear);
		} else {// diffYear = zero
			if (totMonthsDiff >= 1)
				/*
				 * Fix to return 0 if the month is > 0 . Initially it is returning as 6 so that
				 * it is showing 6 years in mobile app. age = String.valueOf(totMonthsDiff);
				 */
				age = "0";
			else // totMonthsDiff = zero
				age = String.valueOf(diffdays);
		}
//		if (age == null)
//			log.info("getAge: returns null,diffYear = " + diffYear + " monsdiff = " + totMonthsDiff + " diffdays = "
//					+ diffdays);
		return age;

	}

	@Override
	public List<JGateway> getJGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		log.info(" Entered GatewayDaoV4 :: getJGateway ");
		List<JGateway> Jgatewaylist = new ArrayList<JGateway>();
		String qry = "SELECT G.id,G.name,G.meid,AM.monitor_type_id FROM gateway G , usergateway UG , assetmodel AM "
				+ "WHERE G.id=UG.gatewayid AND AM.id=G.model_id AND UG.userid ='"
				+ userid + "' ";
		if (gatewayid != null && !(gatewayid.isEmpty()))
			qry = qry + " and G.id = '" + gatewayid + "'";
		else if (groupid != null && !(groupid.isEmpty()))
			qry = qry + " and G.groups.id = '" + groupid + "'";
		else if (assetgroupid != null && !(assetgroupid.isEmpty()))
			qry = qry + " and G.assetgroup.id = '" + assetgroupid + "'";
		else if (meid != null && !(meid.isEmpty()))
			qry = qry + " and G.meid = '" + meid + "'";

		try {
			Query qry_SQL = slave1SessionFactory.getCurrentSession().createSQLQuery(qry);

			List<Object[]> gatewayList = (List<Object[]>) qry_SQL.list();

			if (!gatewayList.isEmpty()) {
				for (Object[] gat : gatewayList) {
					JGateway Jgatewayl = new JGateway();
					Jgatewayl.setId(Long.valueOf(gat[0].toString()));
					Jgatewayl.setName(gat[1].toString());
					Jgatewayl.setMeid((String) gat[2]);
					Jgatewayl.setMonitorTypeId(Long.valueOf(gat[3].toString()));
					Jgatewaylist.add(Jgatewayl);
				}
			}
		} catch (Exception e) {
			log.error("Error in getJGateway :: Session Name : slave1SessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error("Exception : getJGateway : " + e.getLocalizedMessage());
			return null;
		}

		return Jgatewaylist;
	}

	// Get Gateway List By Filter --> Savitha
	@Override
	public JResponse getGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response) {
		ArrayList<GatewayV4Web> gateway = new ArrayList<GatewayV4Web>();
		log.info("Entered :: OptimizedDoaImpl :: getGatewayListByFilter:: ");
		try {
			Session session = sessionFactory.getCurrentSession();
			// Get Total count From Database
			String query = "", joinQuery = "", getCountQuery = "", gatewayQuery = "", genericSearchQuery = "";
			getCountQuery = "SELECT count(*) FROM gateway g ";

			gatewayQuery = "SELECT g.id,g.name AS gateway_name,g.meid,g.macid,g.qrcode,u.username,g.model_id FROM gateway g ";
			joinQuery = "JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId ";

			if (sKey != null && !sKey.equalsIgnoreCase("all")) {
				if (fType.equalsIgnoreCase("equal")) {
					if (sKey.equalsIgnoreCase("name") || sKey.equalsIgnoreCase("meid") || sKey.equalsIgnoreCase("macid")
							|| sKey.equalsIgnoreCase("qrcode")) {
						query += "WHERE g." + sKey + " ='" + sValue + "' ";
					} else if (sKey.equalsIgnoreCase("mobileno") || sKey.equalsIgnoreCase("username")
							|| sKey.equalsIgnoreCase("email")) {
						query += "WHERE u." + sKey + " ='" + sValue + "' ";
						joinQuery = "JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId JOIN devp p ON g.passwordtype = p.id JOIN company c ON c.id = u.cmp_id JOIN companytype ct ON c.cmptype_id = ct.id ";
					} else if (sKey.equalsIgnoreCase("companyname")) {
						query += "WHERE c.name='" + sValue + "' ";
						joinQuery += "JOIN company c ON c.id = g.cmp_id ";
					} else if (sKey.equalsIgnoreCase("cmptype")) {
						query += "WHERE c.cmptype_id='" + sValue + "' ";
						joinQuery += "JOIN company c ON c.id = g.cmp_id ";
					}
				} else {
					if (sKey.equalsIgnoreCase("name") || sKey.equalsIgnoreCase("meid") || sKey.equalsIgnoreCase("macid")
							|| sKey.equalsIgnoreCase("qrcode")) {
						query += "WHERE g." + sKey + " like ('%" + sValue + "%')";
					} else if (sKey.equalsIgnoreCase("mobileno") || sKey.equalsIgnoreCase("username")
							|| sKey.equalsIgnoreCase("email")) {
						query += "WHERE u." + sKey + " like ('%" + sValue + "%')";
						joinQuery = "JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId JOIN devp p ON g.passwordtype = p.id JOIN company c ON c.id = u.cmp_id JOIN companytype ct ON c.cmptype_id = ct.id ";
					} else if (sKey.equalsIgnoreCase("companyname")) {
						query += "WHERE c.name like ('%" + sValue + "%')";
						joinQuery += "JOIN company c ON c.id = g.cmp_id ";
					} else if (sKey.equalsIgnoreCase("cmptype")) {
						query += " WHERE c.cmptype_id like ('%" + sValue + "%')";
						joinQuery += "JOIN company c ON c.id = g.cmp_id ";
					}
				}
			} else if (sKey != null && sKey.equalsIgnoreCase("all")) {
				joinQuery = " JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId ";

				genericSearchQuery = gatewayQuery + joinQuery + " where u.username like ('%" + sValue + "%') union "
						+ gatewayQuery + joinQuery + " where u.email like ('%" + sValue + "%') union " + gatewayQuery
						+ joinQuery + " where u.mobileno like ('%" + sValue + "%')  union " + gatewayQuery + joinQuery
						+ " where g.id like ('%" + sValue + "%') union " + gatewayQuery + joinQuery
						+ " where g.meid like ('%" + sValue + "%') union " + gatewayQuery + joinQuery
						+ " where g.qrcode like ('%" + sValue + "%') union " + gatewayQuery + joinQuery
						+ " where g.macid like ('%" + sValue + "%') union " + gatewayQuery + joinQuery
						+ " where g.name like ('%" + sValue + "%') ";
			}

			SQLQuery countQry = null;
			try {
				if (sKey != null) {
					if (sKey.equalsIgnoreCase("all"))
						countQry = session.createSQLQuery("SELECT count(*) FROM ( " + genericSearchQuery + " ) As u");
					else
						countQry = session.createSQLQuery(getCountQuery + joinQuery + query);
				} else
					countQry = session.createSQLQuery(getCountQuery + joinQuery + query);

				List<BigInteger> countRes = countQry.list();
				long totalGateways = Long.valueOf(((BigInteger) countRes.get(0)) + "");
				response.put("Totalgateways", totalGateways);
				response.put("Offset", offset);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Error occur while getting gatewayCount .");
				response.put("Error", e.getLocalizedMessage());
				log.error(e.getMessage());
			}

			// Get data From Database
			if (oKey != null)
				query += "order by g." + oKey + " " + otype;
			query += " limit " + limit * (offset - 1) + "," + limit;
			SQLQuery listQry = null;

			if (sKey != null) {
				if (sKey.equalsIgnoreCase("all")) {
					if (oKey != null && oKey != "")
						genericSearchQuery += " order by " + oKey + " " + otype;
					listQry = session
							.createSQLQuery(genericSearchQuery + " limit " + limit * (offset - 1) + "," + limit);
				} else
					listQry = session.createSQLQuery(gatewayQuery + joinQuery + query);
			} else
				listQry = session.createSQLQuery(gatewayQuery + joinQuery + query);

			List<Object[]> listRes = listQry.list();
			for (int i = 0; i < listRes.size(); i++) {
				GatewayV4Web gatewayData = new GatewayV4Web();
				Object[] gData = (Object[]) listRes.get(i);
				if (gData[0] != null)
					gatewayData.setGatewayid(((BigInteger) gData[0]).longValue());
				if (gData[1] != null)
					gatewayData.setGateway_name((String) gData[1]);
				if (gData[2] != null)
					gatewayData.setMeid((String) gData[2]);
				if (gData[3] != null)
					gatewayData.setMacid((String) gData[3]);
				if (gData[4] != null)
					gatewayData.setQrc((String) gData[4]);
				if (gData[5] != null)
					gatewayData.setUsername((String) gData[5]);
				if (gData[6] != null)
					gatewayData.setModel_id(((BigInteger) gData[6]).intValue());

				gateway.add(gatewayData);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewaylist", gateway);
		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting gatewaylist - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting gatewaylist - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return response;
	}

	// Get Gateway By Id Web --> Savitha
	@Override
	public JResponse getGatewayByIdWeb(String gatewayId, JResponse response) {
		ArrayList<GatewayV4Web> gateway = new ArrayList<GatewayV4Web>();
		log.info("Entered :: OptimizedDoaImpl :: getGatewayByIdWeb:: ");
		try {
			Session session = sessionFactory.getCurrentSession();
			SQLQuery listQry = session.createSQLQuery(
					"SELECT g.id,g.name AS gateway_name,g.meid,g.macid,g.qrcode,u.id as userId,u.username,u.password,u.email,u.mobileno,c.name AS cmp_name,ct.name AS ctype_name,p.name AS pwd_name FROM gateway g JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId JOIN devp p ON g.passwordtype = p.id JOIN company c ON c.id = u.cmp_id JOIN companytype ct ON c.cmptype_id = ct.id WHERE g.id = '"
							+ gatewayId + "';");
			List<Object[]> listRes = listQry.list();

			if (listRes.isEmpty()) {
				GatewayV4Web gatewayData = new GatewayV4Web();
				gatewayData.setGateway_name("NA");
				gateway.add(gatewayData);
			} else {
				for (int i = 0; i < listRes.size(); i++) {
					GatewayV4Web gatewayData = new GatewayV4Web();
					Object[] gData = (Object[]) listRes.get(i);
					if (gData[0] != null)
						gatewayData.setGatewayid(((BigInteger) gData[0]).longValue());
					if (gData[1] != null)
						gatewayData.setGateway_name((String) gData[1]);
					if (gData[2] != null)
						gatewayData.setMeid((String) gData[2]);
					if (gData[3] != null)
						gatewayData.setMacid((String) gData[3]);
					if (gData[4] != null)
						gatewayData.setQrc((String) gData[4]);
					if (gData[5] != null)
						gatewayData.setUserId(((BigInteger) gData[5]).longValue());
					if (gData[6] != null)
						gatewayData.setUsername((String) gData[6]);
					if (gData[7] != null)
						gatewayData.setPassword((String) gData[7]);
					if (gData[8] != null)
						gatewayData.setEmail((String) gData[8]);
					if (gData[9] != null)
						gatewayData.setMobileno((String) gData[9]);
					if (gData[10] != null)
						gatewayData.setCompany_name((String) gData[10]);
					if (gData[11] != null)
						gatewayData.setCompany_type((String) gData[11]);
					if (gData[12] != null)
						gatewayData.setPassword_type((String) gData[12]);
					gateway.add(gatewayData);
				}
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("usergateway", gateway);
		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting getGatewayByIdWeb - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting getGatewayByIdWeb - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return response;
	}

	// Get user pet profile By userId for Web --> Savitha
	@Override
	public JResponse listUserPetProfileWeb(long id, JResponse response) {
		ArrayList<JPetprofile> petProfile = new ArrayList<JPetprofile>();
		log.info("Entered :: OptimizedDoaImpl :: listUserPetProfileWeb :: ");
		try {
			Session session = sessionFactory.getCurrentSession();
			SQLQuery listQry = session.createSQLQuery(
					"SELECT p.id AS pet_profile_id, p.gateway_id,p.name AS pet_profile_name,g.name AS gateway_name,p.user_id,p.enable FROM `pet_profile` p LEFT OUTER JOIN `gateway` g ON p.gateway_id = g.id WHERE p.user_id= '"
							+ id + "';");
			List<Object[]> listRes = listQry.list();
			if (!listRes.isEmpty()) {
				for (int i = 0; i < listRes.size(); i++) {
					JPetprofile petProfileData = new JPetprofile();
					Object[] pData = (Object[]) listRes.get(i);
					if (pData[0] != null)
						petProfileData.setId(((BigInteger) pData[0]).longValue());
					if (pData[1] != null)
						petProfileData.setGatewayId(((BigInteger) pData[1]).longValue());
					else
						petProfileData.setGatewayId(0);
					if (pData[2] != null)
						petProfileData.setName((String) pData[2]);
					if (pData[3] != null)
						petProfileData.setGatewayName((String) pData[3]);
					else
						petProfileData.setGatewayName("NA");
					if (pData[4] != null)
						petProfileData.setUser_id(((BigInteger) pData[4]).longValue());
					if (pData[5] != null)
						petProfileData.setEnable((Boolean) pData[5]);
					petProfile.add(petProfileData);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("user_petprofile", petProfile);
			} else {
				response.put("Status", 1);
				response.put("Msg", "No pet profile found for this user.");
				response.put("user_petprofile", petProfile);
			}
		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting listUserPetProfileWeb - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting listUserPetProfileWeb - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return response;
	}

	@Override
	public Gateway getGatewayByMeid(String meid) {
		log.info(" Entered into getGatewayByMeid :: meid : " + meid);
		Session session = null;
		try {
			Gateway gateway = null;
			session = sessionFactory.openSession();
			Transaction tx = session.beginTransaction();
			Criteria cr = session.createCriteria(Gateway.class);
			cr.add(Restrictions.eq("meid", meid));
			List list = cr.list();
			tx.commit();
			if (list.isEmpty()) {
				return gateway;
			} else {
				return (Gateway) list.get(0);
			}
		} catch (Exception e) {
			log.error(" Error in getGatewayByMeid : " + e.getLocalizedMessage());
		} finally {
			session.flush();
			session.close();
		}
		return null;
	}

	@Override
	public boolean saveDeviceReplaced(DeviceReplaced deviceReplaced) {
		log.info("Entered into saveDeviceReplaced :: meid : " + deviceReplaced.getMeid());
		try {
			this.sessionFactory.getCurrentSession().saveOrUpdate(deviceReplaced);
			return true;
		} catch (Exception e) {
			log.error("Error  in saveDeviceReplaced : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public List<DeviceReplaced> getReplacedDeviceData() {
		log.info(" Entered into getReplacedDeviceData ");
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(DeviceReplaced.class);
			List<DeviceReplaced> deviceReplacedList = (List<DeviceReplaced>) criteria.list();
			return deviceReplacedList;
		} catch (Exception e) {
			log.error(" Error in getReplacedDeviceData ");
			return null;
		}
	}

	@Override
	public long getUserIdForGateway(String meid, String username) {
		log.info("Entered gateway Dao impl for getUserIdForGateway!");
		Session session = sessionFactory.getCurrentSession();
		long userId = 0;
		try {
			SQLQuery listQry = session.createSQLQuery(
					"SELECT u.id AS userId, u.username, g.id AS gatewayId, g.meid FROM `user` u JOIN usergateway ug ON ug.userId = u.id JOIN\r\n"
							+ "gateway g ON g.id = ug.gatewayId WHERE g.meid='" + meid + "' and u.username = '"
							+ username + "';");
			List<Object[]> listRes = listQry.list();
			if (!listRes.isEmpty()) {
				Object[] gData = (Object[]) listRes.get(0);
				userId = ((BigInteger) gData[0]).longValue();
			}
		} catch (Exception e) {
			log.error("Error occurred in get user id for gateway id!!! - " + e.getLocalizedMessage());
			return 0;
		}
		return userId;
	}

	@Override
	public boolean removeDeviceReplaced(long userId, String meid) {
		log.info(" Entered into removeDeviceReplaced :: userId : " + userId + " meid : " + meid);
		try {

			String deleteQry = "DELETE FROM device_replaced where user_id = " + userId + " AND meid = '" + meid + "';";

			int deleted = sessionFactory.getCurrentSession().createSQLQuery(deleteQry).executeUpdate();

			if (deleted >= 1)
				return true;

		} catch (Exception e) {
			log.error(" Error in removeDeviceReplaced " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public JUser getUsergatewaydetails(long gatewayId) {
		log.info("Entered getUsergatewaydetails :: ");
		JUser userGateway = new JUser();
		try {
			SQLQuery qry = sessionFactory.getCurrentSession().createSQLQuery(
					"SELECT U.id,G.meid FROM user U JOIN usergateway UG ON UG.userId=U.id JOIN gateway G ON G.id=UG.gatewayId WHERE G.id='"
							+ gatewayId + "';");
			List<Object[]> ugList = (List<Object[]>) qry.list();
			if (ugList != null && !ugList.isEmpty()) {
				Object[] obj = ugList.get(0);
				Set<Gateway> gateway = new HashSet<Gateway>();
				Gateway gt = new Gateway();
				userGateway.setId(((BigInteger) obj[0]).longValue());
				gt.setMeid((String) obj[1]);
				gateway.add(gt);
				userGateway.setGateways(gateway);
				return userGateway;
			}

		} catch (Exception e) {
			log.error("getUsergatewaydetails : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public LinkedList<JGateway> getGatewaysByInstalledDate(long userid) {
		LinkedList<JGateway> gatewaylist = new LinkedList<JGateway>();
		try {
			String qry = "SELECT G.id,G.meid  FROM gateway G JOIN usergateway UG ON G.id=UG.gatewayid JOIN assetmodel AM ON AM.id=G.model_id WHERE AM.monitor_type_id=1"
					+ " AND UG.userid=" + userid+ "  ORDER BY isenable DESC,installed_date ASC ";

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List<Object[]> res = query.list();

			if (!res.isEmpty()) {
				for (Object[] gid : res) {
					JGateway jgateway = new JGateway();

					long gatewayid = ((BigInteger) gid[0]).longValue();
					String meid = gid[1].toString();

					jgateway.setId(gatewayid);
					jgateway.setMeid(meid);

					gatewaylist.add(jgateway);
				}
			}
		} catch (Exception e) {
			log.error("Error in getGatewaysByInstalledDate : "+e.getLocalizedMessage());
		}
		return gatewaylist;
	}

	// Get Gateway List By Filter --> Savitha
	@Override
	public JResponse getUnmappedGatewayListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, JResponse response) {
		ArrayList<GatewayV4Web> gateway = new ArrayList<GatewayV4Web>();
		log.info("Entered :: OptimizedDoaImpl :: getGatewayListByFilter:: ");
		try {
			Session session = sessionFactory.getCurrentSession();
			// Get Total count From Database
			String query = "", joinQuery = "", getCountQuery = "", gatewayQuery = "", genericSearchQuery = "";
			getCountQuery = "SELECT count(*) FROM gateway g ";

			gatewayQuery = "SELECT g.id,g.name AS gateway_name,g.meid,g.macid,g.qrcode,u.username,u.email,u.authkey FROM gateway g ";
			joinQuery = "JOIN usergateway ug ON g.id = ug.gatewayId JOIN `user` u ON u.id = ug.userId ";

			if (sKey != null && !sKey.equalsIgnoreCase("all")) {
				if (fType.equalsIgnoreCase("equal")) {
					if (sKey.equalsIgnoreCase("meid") || sKey.equalsIgnoreCase("macid")
							|| sKey.equalsIgnoreCase("qrcode")) {
						query += "WHERE g." + sKey + " ='" + sValue + "' and g.show_order_id = '0' ";
					} else if (sKey.equalsIgnoreCase("mobileno") || sKey.equalsIgnoreCase("username")
							|| sKey.equalsIgnoreCase("email")) {
						query += "WHERE u." + sKey + " ='" + sValue + "' and g.show_order_id = '0' ";
					}
				} else {
					if (sKey.equalsIgnoreCase("meid") || sKey.equalsIgnoreCase("macid")
							|| sKey.equalsIgnoreCase("qrcode")) {
						query += "WHERE g." + sKey + " like ('%" + sValue + "%') and g.show_order_id = '0' ";
					} else if (sKey.equalsIgnoreCase("mobileno") || sKey.equalsIgnoreCase("username")
							|| sKey.equalsIgnoreCase("email")) {
						query += "WHERE u." + sKey + " like ('%" + sValue + "%') and g.show_order_id = '0' ";
					}
				}
			} else if (sKey != null && sKey.equalsIgnoreCase("all")) {
				genericSearchQuery = gatewayQuery + joinQuery + " where u.username like ('%" + sValue
						+ "%')  and g.show_order_id = '0' union " + gatewayQuery + joinQuery + " where u.email like ('%"
						+ sValue + "%') and g.show_order_id = '0' union " + gatewayQuery + joinQuery
						+ " where u.mobileno like ('%" + sValue + "%') and g.show_order_id = '0' union " + gatewayQuery
						+ joinQuery + " where g.id like ('%" + sValue + "%')  and g.show_order_id = '0' union "
						+ gatewayQuery + joinQuery + " where g.meid like ('%" + sValue
						+ "%') and g.show_order_id = '0' union " + gatewayQuery + joinQuery + " where g.qrcode like ('%"
						+ sValue + "%') and g.show_order_id = '0' union " + gatewayQuery + joinQuery
						+ " where g.macid like ('%" + sValue + "%') and g.show_order_id = '0' union " + gatewayQuery
						+ joinQuery + " where g.name like ('%" + sValue + "%') and g.show_order_id = '0' ";
			}

			SQLQuery countQry = null;
			try {
				if (sKey != null) {
					if (sKey.equalsIgnoreCase("all"))
						countQry = session.createSQLQuery("SELECT count(*) FROM ( " + genericSearchQuery + " ) As u");
					else
						countQry = session.createSQLQuery(getCountQuery + joinQuery + query);
				} else
					countQry = session
							.createSQLQuery(getCountQuery + joinQuery + query + " where g.show_order_id = '0' ");

				List<BigInteger> countRes = countQry.list();
				long totalGateways = Long.valueOf(((BigInteger) countRes.get(0)) + "");
				response.put("Totalgateways", totalGateways);
				response.put("Offset", offset);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Error occur while getting gatewayCount .");
				response.put("Error", e.getLocalizedMessage());
				log.error(e.getMessage());
			}

			// Get data From Database
			if (oKey != null)
				query += "order by g." + oKey + " " + otype;
			query += " limit " + limit * (offset - 1) + "," + limit;
			SQLQuery listQry = null;

			if (sKey != null) {
				if (sKey.equalsIgnoreCase("all")) {
					if (oKey != null && oKey != "")
						genericSearchQuery += " order by " + oKey + " " + otype;
					listQry = session
							.createSQLQuery(genericSearchQuery + " limit " + limit * (offset - 1) + "," + limit);
				} else
					listQry = session.createSQLQuery(gatewayQuery + joinQuery + query);
			} else
				listQry = session.createSQLQuery(gatewayQuery + joinQuery + " where g.show_order_id = '0' " + query);

			List<Object[]> listRes = listQry.list();
			for (int i = 0; i < listRes.size(); i++) {
				GatewayV4Web gatewayData = new GatewayV4Web();
				Object[] gData = (Object[]) listRes.get(i);
				if (gData[0] != null)
					gatewayData.setGatewayid(((BigInteger) gData[0]).longValue());
				if (gData[1] != null)
					gatewayData.setGateway_name((String) gData[1]);
				if (gData[2] != null)
					gatewayData.setMeid((String) gData[2]);
				if (gData[3] != null)
					gatewayData.setMacid((String) gData[3]);
				if (gData[4] != null)
					gatewayData.setQrc((String) gData[4]);
				if (gData[5] != null)
					gatewayData.setUsername((String) gData[5]);
				if (gData[6] != null)
					gatewayData.setEmail((String) gData[6]);
				if (gData[7] != null)
					gatewayData.setAuthKey((String) gData[7]);

				gateway.add(gatewayData);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewaylist", gateway);
		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting gatewaylist - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occurs while getting gatewaylist - " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return response;
	}

	@Override
	public boolean removeNotComplitedDeviceReplaced(long userId, String meid) {
		log.info(" Entered into removeNotComplitedDeviceReplaced :: userId : " + userId + " meid : " + meid);
		try {

			String deleteQry = "DELETE FROM device_replaced where user_id = " + userId + " AND meid = '" + meid
					+ "' AND ( action=1 OR action=2 ) ;";

			int deleted = sessionFactory.getCurrentSession().createSQLQuery(deleteQry).executeUpdate();

			if (deleted >= 1)
				return true;

		} catch (Exception e) {
			log.error("Error in removeNotComplitedDeviceReplaced :: Session Name : sessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error(" Error in  removeNotComplitedDeviceReplaced " + e.getLocalizedMessage());
		}
		return false;
	}

	// Currently not in use
//	@Override
//	public boolean updateNewMeidInOrderMap(long user_id, String new_meid, String old_meid) {
//		log.info("Entered updateNewMeidInOrderMap DAO Impl!!!");
//		try {
//			String qry = "update ordermap set meid = '" + new_meid + "' where meid='" + old_meid + "' and user_id='"+user_id+"';";
//			int result = niomSessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
//			if (result >= 1) {
//				return true;
//			} else {
//				return false;
//			}
//		} catch (Exception e) {
//			log.error("Error occurred in update new meid in  fota version!!!");
//			return false;
//		}
//	}

	@Override
	public String getGatewayImg(long gatewayid) {
		String qry = "SELECT P.imageurl FROM gateway  G JOIN `pet_profile` P ON G.id= P.gateway_id WHERE G.id= "
				+ gatewayid + " limit 1;";
		String imgPath = "NA";
		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (!res.isEmpty()) {
				imgPath = (String) res.get(0);

			}
		} catch (Exception e) {
			log.error("Error in getGatewayImg :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error("getGatewayImg: " + e.getMessage());
		}
		return imgPath;
	}

	@Override
	public String getGatewayImgv5(long gatewayid) {
		String qry = "SELECT P.imageurl FROM gateway_profile  G JOIN `pet_profile` P ON G.petprofile_id= P.id WHERE G.gateway_id= "+gatewayid+" LIMIT 1;";
		String imgPath = "NA";
		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (!res.isEmpty()) {
				imgPath = (String) res.get(0);
				if(imgPath.trim().isEmpty() || imgPath==null)
					imgPath = "NA";
			}
		} catch (Exception e) {
			log.error("Error in getGatewayImg :: Session Name : slave4SessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error("getGatewayImg: " + e.getMessage());
		}
		return imgPath;
	}
	@Override
	public boolean checkRecallQRC(String qrcode) {
		log.info(" Entered into checkRecallQRC :: qrc : " + qrcode);
		try {
			Criteria criteria = this.slave3SessionFactory.getCurrentSession().createCriteria(OldDevice.class)
					.add(Restrictions.eq("qrc", qrcode));
			List<OldDevice> oldDeviceList = (List<OldDevice>) criteria.list();

			if (oldDeviceList.isEmpty()) {
				return false;
			} else {
				return true;
			}

		} catch (Exception e) {
			log.error("Error in checkRecallQRC :: Session Name : slave3SessionFactory :: error : "
					+ e.getLocalizedMessage());
			log.error(" Error in checkRecallQRC ");
			return false;
		}
	}

	@Override
	public boolean saveTempCalib(TempCalibStatus calibStatus) {
		log.info("Entered saveTempCalib DAO IMPL for gateway id : " + calibStatus.getGateway_id());

		boolean isMerged = false;
		try {
			String qry = "SELECT id FROM temp_calib_status WHERE gateway_id = '" + calibStatus.getGateway_id() + "';";
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

			if (!query.list().isEmpty()) {
				long id = ((BigInteger) query.list().get(0)).longValue();
				calibStatus.setId(id);
			}
			Session ses = sessionFactory.getCurrentSession();
			ses.merge(calibStatus);
			isMerged = true;
		} catch (Exception e) {
			isMerged = false;
			log.error("Exception in saveTempCalib DAO Impl : " + e.getLocalizedMessage());
		}
		return isMerged;
	}

	@Override
	public boolean updateTempUnit(long cmpid, String temperatureunit) {
		try {
			log.info("Entered :: OptimizedDoaImpl :: updateTempUnit:: ");
			Session ses = sessionFactory.getCurrentSession();
			String qry1 = "update companyconfig set temperatureunit='" + temperatureunit + "' where cmp_id = " + cmpid + ";";

			SQLQuery qry = ses.createSQLQuery(qry1);
			int status = qry.executeUpdate();

			if (status > 0) {
				log.info("updated CompanyCfg");
				return true;
			}
			return false;
		} catch (Exception e) {
			log.error("Exception in updateTempUnit: " + e.getMessage());
		}
		return false;
	}

	@Override
	public boolean savePLDelayFreq(PlThresholdStatus plThresholdStatus)
	{
		log.info("Entered savePLDelayFreq DAO IMPL for gateway id : " + plThresholdStatus.getGateway_id());

		boolean isMerged = false;
		try {
			String qry = "SELECT id FROM pl_threshold_status WHERE gateway_id = '" + plThresholdStatus.getGateway_id() + "';";
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

			if (!query.list().isEmpty()) {
				long id = ((BigInteger) query.list().get(0)).longValue();
				plThresholdStatus.setId(id);
			}
			Session ses = sessionFactory.getCurrentSession();
			ses.merge(plThresholdStatus);
			isMerged = true;
		} catch (Exception e) {
			isMerged = false;
			log.error("Exception in savePLDelayFreq DAO Impl : " + e.getLocalizedMessage());
		}
		return isMerged;
	}

	@Override
	public boolean updateWarrantyPopup(long gateway_id, boolean popup) {
		try {
			log.info("Entered :: OptimizedDoaImpl :: updateWarrantyPopup:: ");
			Session ses = sessionFactory.getCurrentSession();
			int purchased_from_others = 0;
			if(popup)
				purchased_from_others = 1;

			int isWithoutsub = 1;
			if(popup)
				isWithoutsub = 0;

			String qry1 = "update gateway set purchased_from_others='" + purchased_from_others + "',iswithoutsub='" +isWithoutsub+ "' where id = " + gateway_id + ";";

			SQLQuery qry = ses.createSQLQuery(qry1);
			int status = qry.executeUpdate();

			if (status > 0) {
				log.info("updated purchased_from_others");
				return true;
			}
			return false;
		} catch (Exception e) {
			log.error("Exception in purchased_from_others: " + e.getMessage());
		}
		return false;
	}

	@Override
	public List<RemoveGatewayType> getRemoveGatewayType() {
		log.info("Entered into getRemoveGatewayType");
		List<RemoveGatewayType> removeGatewayTypeList = new ArrayList<>();
		try {
			
			removeGatewayTypeList = sessionFactory.getCurrentSession().createCriteria(RemoveGatewayType.class)
					.add( Restrictions.eq("enable", true))
					.list();
			
			if( removeGatewayTypeList.isEmpty() ) 
				log.info("RemoveGatewayType is empty");
				
			return removeGatewayTypeList;
		} catch (Exception e) {
			log.error("Error in getRemoveGatewayType :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public RemoveGatewayType getRemoveGatewayTypeById(int remove_gateway_type_id) {
		log.info("Entered into getRemoveGatewayTypeById :: remove_gateway_type_id : "+ remove_gateway_type_id);
		List<RemoveGatewayType> removeGatewayTypeList = new ArrayList<>();
		try {
			removeGatewayTypeList = sessionFactory.getCurrentSession().createCriteria(RemoveGatewayType.class)
					.add( Restrictions.eq("id", (long) remove_gateway_type_id))
					.list();
			if( removeGatewayTypeList.isEmpty() ) { 
				log.info("removeGatewayTypeList is empty");
				return null;
			}
			
			return removeGatewayTypeList.get(0);
		} catch (Exception e) {
			log.error("Error in getRemoveGatewayTypeById :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public RemoveGatewayRequest getRemoveGatewayRequest(long user_id) {
		log.info("Entered into getRemoveGatewayRequestByUserId :: user_id : "+ user_id);
		List<RemoveGatewayRequest> removeGatewayRequestList = new ArrayList<>();
		try {
			removeGatewayRequestList = sessionFactory.getCurrentSession().createCriteria(RemoveGatewayRequest.class)
					.add( Restrictions.eq("user_id", user_id))
//					.add( Restrictions.eq("gateway_id", gateway_id))
					.list();
			if( removeGatewayRequestList.isEmpty() ) { 
				log.info("removeGatewayRequest is empty");
				return null;
			}
				
			return removeGatewayRequestList.get(0);
		} catch (Exception e) {
			log.error("Error in getRemoveGatewayRequestByUserId :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public RemoveGatewayRequest saveOrUpdateRemoveGatewayRequest(RemoveGatewayRequest removeGatewayRequest) {
		log.info("Entered into saveOrUpdateRemoveGatewayRequest :: user_id : "+ removeGatewayRequest.getUser_id());
		try {
			return (RemoveGatewayRequest) sessionFactory.getCurrentSession().merge(removeGatewayRequest);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateRemoveGatewayRequest :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}


	@Override
	public RemoveGatewayRequestHistory saveOrUpdateRemoveGatewayRequestHistory(
			RemoveGatewayRequestHistory removeGatewayRequest) {
		log.info("Entered into saveOrUpdateRemoveGatewayRequest :: user_id : "+ removeGatewayRequest.getUser_id());
		try {
			return (RemoveGatewayRequestHistory) sessionFactory.getCurrentSession().merge(removeGatewayRequest);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateRemoveGatewayRequest :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public List<RemoveGatewayRequest> getValidRemovalGateway(long user_id) {
		log.info("Entered into getValidRemovalGateway :: user_id");
		List<RemoveGatewayRequest> validRemovalGatewayList = new ArrayList<>();
		try {
			
			String qry = "SELECT RGR.* "
					   + " FROM remove_gateway_request RGR"
					   + " JOIN usergateway UG ON UG.gatewayId = RGR.gateway_id "
					   + " WHERE UG.userId =:userid AND "
					   + " DATE_ADD(RGR.updated_on, INTERVAL "+ remove_gateway_valid_hours_to_show +" HOUR) > :dateString";
			
			log.info("qry : "+ qry);
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry).addEntity(RemoveGatewayRequest.class);
			query.setParameter("userid", user_id);
			query.setParameter("dateString", _helper.getCurrentTimeinUTC());
			
			validRemovalGatewayList = query.list();
			
			if( validRemovalGatewayList.isEmpty() ) {
				log.info("No remove_gateway_request found");
				return null;
			}
			
			return validRemovalGatewayList;
		} catch (Exception e) {
			log.error("Error in getValidRemovalGateway :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public List<JGatewayInfo> getJGatewayInfo(long user_id) {
		log.info("Entered into getJGatewayInfo :: user_id : "+ user_id);
		List<JGatewayInfo> gatewayInfoList = new ArrayList<>();
		try {
			
			String qry = "SELECT G.id AS gateway_id,G.name,G.qrcode AS qr_code,P.imageurl AS image_url"
					   + " FROM gateway G "
					   + " JOIN usergateway UG ON UG.gatewayId = G.id "
					   + " LEFT JOIN pet_profile P ON P.gateway_id = UG.gatewayId "
					   + " WHERE UG.userId =:userid";
			
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("userid", user_id);
			
			query.setResultTransformer(new AliasToBeanResultTransformer(JGatewayInfo.class));
			query
			.addScalar("gateway_id", new LongType())
			.addScalar("name", new StringType())
			.addScalar("qr_code", new StringType())
			.addScalar("image_url", new StringType());
			
			gatewayInfoList = query.list();
			
			if( gatewayInfoList.isEmpty() ) {
				log.info("No gateway found");
				return null;
			}
			
			return gatewayInfoList;
		} catch (Exception e) {
			log.error("Error in getJGatewayInfo :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public List<Object> getWCDeviceList( long userID, long gateway_id, long monitor_type_id) {

		log.info("Entered :: getDeviceList :  userId : " + userID);

		List<Object> wcDeviceList = new ArrayList<Object>();

		try {
			Session ses = sessionFactory.getCurrentSession();

			String sqlQry = "SELECT DISTINCT "
					+ " G.id, "						//0
					+ " G.macid,"					//1
					+ " G.name,"					//2
					+ " AM.monitor_type_id," 		//3
					+ " G.qrcode,"					//4
					+ " UG.gatewayId,"				//5
					+ " UG.userId,"					//6
					+ " G.imageurl,"				//7
					+ " AM.is_wifidelete,"			//8
					+ " AM.model,"					//9
					+ " AM.id as assetmodelid,"		//10
					+ " AM.is_wifi_connection,"		//11
					+ " AM.total_ssid_count,"		//12
					+ " AM.is_manual_connection,"	//13
					+ " G.purchased_from_others,"	//14
					+ " G.show_order_id,"			//15
					+ " G.meid, "					//16
					+ " GS.streaming,"				//17
					+ " GS.night_vision,"			//18
					+ " GS.quality,"				//19
					+ " GS.device_angle,"			//20
					+ " GS.speaker_volume, "		//21
					+ " GS.mic_status,"				//22
					+ " AM.min_rotation,"			//23
					+ " AM.max_rotation,"			//24
					+ " U.country,"					//25
					+ " G.auto_update,"				//26
					+ " IF (FA.fota_status IS NULL,'NA', FA.fota_status) AS fota_status," //27
					+ " G.firmware_ver, "			//28
					+ " GS.auto_night_vision, "		//29
					+ " PS.speciesname, "			//30
					+ " G.user_time_zone, "			//31
					+ " GS.motion_detection, "		//32
					+ " AM.motion_detection AS md,"	//33
					+ " AM.stored_video, "			//34
					+ " GS.is_online,"    			//35
					+ " AM.debug AS show_debug, "	//36
					+ " GS.debug AS is_debug,"		//37
					+ " GS.livetracking_status,"	//38
					+ " GS.livetracking_person,"		//39
					+ " GS.livetracking_boundingbox,"	//40
					+ " GS.livetracking_rotation,"		//41
					+ " AM.live_tracking,"				//42
					+ " AM.barking_alert AS show_barking_alert,"	//43
					+ " GS.barking_alert, "							//44
					+ " GS.motion_detection_person, "				//45
					+ " GS.notification, "							//46
					+ " GS.screen_flip, "							//47
					+ " GS.night_vision_mode, "						//48
					+ " GS.noise_detection, "						//49
					+ " GS.detection_sensitivity, "					//50
					+ " GS.serial_number, "							//51
					+ " GS.continuous_playback, "					//52
					+ " PL.id AS plan_id, "							//53
					+ " PL.is_freeplan, "							//54
					+ " GF.sub_id, "								//55
					+ " GF.period_id AS periodid, "					//56
					+ " GS.recording_time, "						//57
					+ " GS.alarm_interval, "						//58
					+ " GS.noise_sensitivity, "						//59
					+ " GS.event_playback, "						//60
					+ " GS.pet_detection, "							//61
					+ " AM.temp_alert, "							//62
					+ " IF (ACW.email_ids IS NULL,'NA', ACW.email_ids) AS alert_email_ids, "     //63
					+ " IF (ACW.mobile_nos IS NULL,'NA', ACW.mobile_nos) AS alert_mobile_nos, "  //64
					+ " GS.throwing_beep, "							//65
					+ " AM.ispowermode, "							//66
					+ " AM.ishumidity, "							//67
					+ " AM.is_web_rtc, "							//68
					+ " GS.running_on_battery, "					//69
					+ " AM.show_sd, "								//70
					+ " GS.vehicle_detection, "						//71
					+ " GS.device_unique_id, "						//72
					+ " GS.stream_url, "								//73
					+ "	GS.aitreat_onoff, "								//74
					+ "	GS.treat_count, "								//75
					+ "	GS.aitreat_interval, "							//76
					+ "	GS.aitreat_maxcount, "							//77
					+ "	GS.auto_tracking, "								//78
					+ "	GS.show_vehicle_detection, "					//79
					+ "	GS.operation_mode, "							//80
					+ "	GS.light_time_id,"								//81
					+ " GS.is_activated, "								//82
					+ " GS.videoencoding_format, "						//83
					+ " GS.flicker_level, "								//84
					+ " GS.smart_light_detection, "						//85
					+ " GS.last_device_reset, "						    //86
					+ " U.chargebeeid, "  								//87
					+ " GS.is_microphone_enabled, "						//88
					+ " GS.is_recordvideo_enabled, "					//89
					+ " GS.is_speaker_enabled, "						//90
					+ " GS.human_detection_day, "						//91
					+ " GS.human_detection_night, "						//92
					+ " GS.is_auto_sensitive_enabled, "					//93
					+ " G.meari_update_popup "					        //94
					+ " FROM gateway G "
					+ " JOIN gateway_status GS on GS.gateway_id=G.id "
					+ " JOIN usergateway UG ON UG.gatewayID = G.id "
					+ " JOIN user U ON U.id=UG.userId "
					+ " JOIN assetmodel AM ON G.model_id = AM.id "
					+ " LEFT JOIN gateway_profile GP on GP.gateway_id = UG.gatewayId "
					+ " LEFT JOIN fota_to_asset FA ON G.meid = FA.meid "
					+ " LEFT JOIN pet_profile PP ON PP.id = GP.petprofile_id "
					+ " LEFT JOIN petspecies PS ON PS.id = PP.speciesid "
					+ " LEFT JOIN gateway_feature GF ON GF.gateway_id = G.id AND GF.enable=1 "
					+ " LEFT JOIN plan PL ON PL.id = GF.plan_id "
					+ " LEFT JOIN alert_cfg_wc ACW ON ACW.gateway_id = UG.gatewayId "
					+ " WHERE UG.userId= :userid ";
			if (gateway_id != 0) {
				sqlQry += " AND UG.gatewayId= :gatewayid ";
			}
			
			if (monitor_type_id != 0) {
				sqlQry += " AND AM.monitor_type_id= :monitor_type_id ";
			}

			sqlQry += " order by G.id ";

			SQLQuery gatewayQry = ses.createSQLQuery(sqlQry);
			gatewayQry.setParameter("userid", userID);
			
			if (gateway_id != 0)
				gatewayQry.setParameter("gatewayid", gateway_id);
			
			if (monitor_type_id != 0)
				gatewayQry.setParameter("monitor_type_id", monitor_type_id);
			
			List<Object[]> gatewayRes = gatewayQry.list();
			int total_ssid_count = 0;

			if (gatewayRes.size() > 0) {

				String country = (String) gatewayRes.get(0)[25];
				if (country == null || country.equalsIgnoreCase("NA") || country.equalsIgnoreCase("IN"))
					country = "US";
				String supportM = supportContactEmail.get(country) != null ? supportContactEmail.get(country) : supportContactEmail.get("US");
				String supportP = supportContactNumber.get(country) != null ? supportContactNumber.get(country) : supportContactNumber.get("US");

				for (Object[] result : gatewayRes) {

					WCDeviceList wcDevice = new WCDeviceList();

					long gatewayId = ((BigInteger) result[0]).longValue();
					if (result[16] != null) {
						wcDevice.setMeid((String) result[16]);
					}
					if (result[1] != null) {
						wcDevice.setMacid((String) result[1]);
					}

					if (result[2] != null)
						wcDevice.setGatewayname((String) result[2]);
					if (result[3] != null)
						wcDevice.setMonitortype( ((BigInteger) result[3]).longValue() );
					if (result[4] != null)
						wcDevice.setQrcode((String) result[4]);
					if (result[5] != null)
						wcDevice.setGatewayid(((BigInteger) result[5]).longValue());
					if (result[6] != null)
						wcDevice.setUserid(((BigInteger) result[6]).longValue());

					if (result[7] != null)
						wcDevice.setImageurl((String) result[7]);

					if (result[8] != null)
						wcDevice.setIs_wifidelete((boolean) result[8]);

					long assetModelId = ((BigInteger) result[10]).longValue();

					if (result[11] != null) {
						wcDevice.setIs_wifi_connection((boolean) result[11]);
						if ((boolean) result[11]) {
							wcDevice.setWifi_ssid("WC_" + (String) result[1]);
							wcDevice.setWifi_password(wc_wifi_password);
						}
					}

					if (result[12] != null) {
						total_ssid_count = (int) result[12];
						wcDevice.setTotalwificount(total_ssid_count);
						wcDevice.setRemainingwificount(total_ssid_count);
					}

					if (result[13] != null) {
						wcDevice.setIs_manual_connection((boolean) result[13]);
					}

					String suffixImgtype = "assetmodelid_" + assetModelId;

//					if (!type.trim().isEmpty()) {
//						log.info("Mobile platform : " + type + ",  Img Name :" + suffixImgtype + "_wifi_pairing_img");
//						AppImage appimage = appImageService.getAppImages(type, suffixImgtype + "_wifi_pairing_img");
//						if (appimage != null)
//							wcDevice.setWifi_pairing_img(appimage.getImg_path());
//					}

					List<WifiInfo> wifi_info_list = wifiServiceV4.getWiFiList(gatewayId);

					if( wifi_info_list.size() > 0 ) {
						if( wc_show_hd_all_freq ) {
							wcDevice.setShow_hd(true);
						} else {
							WifiInfo wifiinfo = wifi_info_list.get(0);
							if( wifiinfo.getBandwidth().contains("5") ) {
								wcDevice.setShow_hd(true);
							}
						}
					}
					
					int remainingWifiCount = total_ssid_count - wifi_info_list.size();
					if (remainingWifiCount < 0)
						remainingWifiCount = 0;

					wcDevice.setRemainingwificount(remainingWifiCount);
					wcDevice.setWifiinfolist(wifi_info_list);
					if (result[14] != null && result[15] != null) {
						boolean purchased_from_others = (boolean) result[14];
						boolean show_orderid = (boolean) result[15];
						if (show_orderid) {
							wcDevice.setWarranty_claimed(true);
							wcDevice.setWarranty_claim_msg("Your Warranty is already registered");
						}

						if (purchased_from_others) {
							wcDevice.setPurchased_from_others(true);
							String eRes = RegisterUserError.contectMsgNormalTxt;
							String msg = eRes.replace("#SP#", supportP).replace("#SM#", supportM)
									+ " to register your warranty.";
							wcDevice.setWarranty_claim_msg(msg);
						}
					}
					if (result[17] != null)
						wcDevice.setStreaming((boolean) result[17]);
					if (result[18] != null)
						wcDevice.setNight_vision((boolean) result[18]);
					if (result[19] != null)
						wcDevice.setQuality((String) result[19]);
					if (result[20] != null)
						wcDevice.setDevice_angle(((BigInteger) result[20]).intValue());
					if (result[21] != null)
						wcDevice.setSpeaker_volume((int) result[21]);
					if (result[22] != null)
						wcDevice.setMic_status((boolean) result[22]);
					if (result[23] != null)
						wcDevice.setMin_rotation((int) result[23]);
					if (result[24] != null)
						wcDevice.setMax_rotation((int) result[24]);
					if (result[26] != null)
						wcDevice.setAuto_update((boolean) result[26]);
					if (result[27] != null)
						wcDevice.setFota_status((String) result[27]);
					if (result[28] != null) {
						wcDevice.setCurrent_fota_version((String) result[28]);
						
						if( wcDevice.getCurrent_fota_version().equalsIgnoreCase("NA") || wcDevice.getCurrent_fota_version().trim().equalsIgnoreCase("")) {
							wcDevice.setCurrent_fota_version("-");	
						}else {
							if(monitor_type_id == 5) {
								try {
									String withoutDots = wcDevice.getCurrent_fota_version().replace(".", "");
									long val = Long.parseLong(withoutDots);
									if(String.valueOf(val).length() < 11) {
										val = Long.parseLong(String.valueOf(val) + "0");
									}
									if(56620240520l < val) {
										wcDevice.setShowwatermark(false);
									}
								}catch(Exception e) {
									log.error("Error while fota vertion in mini cam :: Error : "+ e.getLocalizedMessage());
									
								}
							}
							if(monitor_type_id == 6) {
								try {
									String withoutDots = wcDevice.getCurrent_fota_version().replace(".", "");
									long val = Long.parseLong(withoutDots);
									if(String.valueOf(val).length() < 11) {
										val = Long.parseLong(String.valueOf(val) + "0");
									}
									if(56520240517l < val) {
										wcDevice.setShowwatermark(false);
									}
								}catch(Exception e) {
									log.error("Error while fota vertion in waggle cam pro :: Error : "+ e.getLocalizedMessage());

								}
							}
							if(monitor_type_id == 8) {
								try {
									String withoutDots = wcDevice.getCurrent_fota_version().replace(".", "");
									long val = Long.parseLong(withoutDots);
									if(String.valueOf(val).length() < 13) {
										val = Long.parseLong(String.valueOf(val) + "0");
									}
									if(5411520241203l < val) {
										wcDevice.setShowwatermark(false);
									}
								}catch(Exception e) {
									log.error("Error while fota vertion in waggle cam pro :: Error : "+ e.getLocalizedMessage());

								}
							}
							if(monitor_type_id == 12) {
								try {
									String withoutDots = wcDevice.getCurrent_fota_version().replace(".", "");
									long val = Long.parseLong(withoutDots);
									if(String.valueOf(val).length() < 11) {
										val = Long.parseLong(String.valueOf(val) + "0");
									}
									if(35520250320l < val) {
										wcDevice.setShowwatermark(false);
									}
								}catch(Exception e) {
									log.error("Error while fota vertion in waggle cam pro :: Error : "+ e.getLocalizedMessage());

								}
							}
						}
					}
					if (result[29] != null)
						wcDevice.setAuto_night_vision((boolean) result[29]);
					if (result[30] != null)
						wcDevice.setDevice_for((String) result[30]);
					if (result[31] != null)
						wcDevice.setUser_time_zone((boolean) result[31]);
					if (result[32] != null) {
						wcDevice.setIs_motion_detection((boolean) result[32]);
						wcDevice.setMotion_detection_dog( wcDevice.isIs_motion_detection() );
					}
					if (result[33] != null)
						wcDevice.setShow_motion_detection((boolean) result[33]);
					if (result[34] != null)
						wcDevice.setShow_stored_video((boolean) result[34]);
					if (result[35] != null)
						wcDevice.setis_online((boolean) result[35]);
					if (result[36] != null)
						wcDevice.setShow_debug((boolean) result[36]);
					if (result[37] != null)
						wcDevice.setIs_debug((boolean) result[37]);
					if (result[38] != null)
						wcDevice.setLivetracking_status((boolean) result[38]);
					if (result[39] != null)
						wcDevice.setLivetracking_person((boolean) result[39]);
					if (result[40] != null)
						wcDevice.setLivetracking_boundingbox((boolean) result[40]);
					if (result[41] != null)
						wcDevice.setLivetracking_rotation((boolean) result[41]);
					if (result[42] != null)
						wcDevice.setShow_live_tracking((boolean) result[42]);
					if (result[43] != null)
						wcDevice.setShow_barking_alert((boolean) result[43]);
					if (result[44] != null)
						wcDevice.setBarking_alert((boolean) result[44]);
					if (result[45] != null)
						wcDevice.setMotion_detection_person((boolean) result[45]);
					if (result[46] != null)
						wcDevice.setNotification((boolean) result[46]);
					if (result[47] != null)
						wcDevice.setScreen_flip((boolean) result[47]);
					if (result[48] != null)
						wcDevice.setNight_vision_mode((int) result[48]);
					if (result[49] != null)
						wcDevice.setNoise_detection((boolean) result[49]);
					if (result[50] != null)
						wcDevice.setDetection_sensitivity((String) result[50]);
					if (result[51] != null)
						wcDevice.setSerial_number((String) result[51]);
					if (result[52] != null)
						wcDevice.setContinuous_playback((boolean) result[52]);
									
					if( wcDevice.getMonitortype() == 5 || wcDevice.getMonitortype() == 6 || wcDevice.getMonitortype() == 8 || wcDevice.getMonitortype() == 12) {
						wcDevice.setShow_motion_detection_person(true);
						wcDevice.setShow_noise_detection(true);
						wcDevice.setShow_continuous_playback(true);
						wcDevice.setShow_screen_flip(true);
						wcDevice.setShow_live_tracking(wcDevice.getMonitortype() != 12);
						wcDevice.setShow_motion_detection(true);
						wcDevice.setShow_barking_alert(true);
						
						if( wcDevice.getMonitortype() == 5 ) {
							wcDevice.setShow_motion_detection(false);;
							wcDevice.setShow_barking_alert(false);
						} else if ( wcDevice.getMonitortype() == 6 ) {
							wcDevice.setShow_continuous_playback(false);	
						}
						
						if( !wc_continuous_playback )
							wcDevice.setShow_continuous_playback(false);

						if (result[86] != null) {
							Date resetDate = (Date) result[86];
							long reset_milliseconds = reset_minits * 60 * 1000; // 5 minutes

							Date dateDnr = new Date(resetDate.getTime() + reset_milliseconds);
							Date currentDate = new Date();

							if (currentDate.after(dateDnr)) {
								wcDevice.setIs_device_reset(false);
							} else {
								wcDevice.setIs_device_reset(true);
							}

						}
					}
					if( wcDevice.getMonitortype() == 8 || wcDevice.getMonitortype() == 12) {
						wcDevice.setShow_lighting_settings(true);
						wcDevice.setShow_operation_mode(true);
						if (result[80] != null)
							wcDevice.setOperation_mode((int) result[80]);
						if (result[81] != null)
							wcDevice.setLight_setting((int) result[81]);
						if (result[82] != null)
							wcDevice.setIs_configured((boolean) result[82]);
						if (result[83] != null)
							wcDevice.setVideoencoding_format((String) result[83]);
						if (result[84] != null)
							wcDevice.setFlicker_level((int) result[84]);
						if (result[85] != null)
							wcDevice.setSmart_light_detection((boolean) result[85]);

						wcDevice.setContinuous_playback(wcDevice.getMonitortype() != 12);
						
						if(wcDevice.getNight_vision_mode() == 1) {
							wcDevice.setNight_vision_mode(6);
						}
					}
					
					if (result[53] != null) {
						wcDevice.setPlanId(((BigInteger) result[53]).longValue());
					}
					if (result[62] != null && (boolean) result[62] ) {
						wcDevice.setTemperature_model( true );
						wcDevice.setProduct_model_name( IrisservicesConstants.WAGGLE_CAM_ULTRA );
					}
					
					if (result[56] != null) {
						wcDevice.setPeriodId(((BigInteger) result[56]).longValue());
					}

					AllProductSubscription allProdSub = null;
					if((String) result[87] != null) {
						allProdSub = cbService.getProductSubscriptionByGateway(gatewayId, (String) result[87]);
					}

					if (result[54] == null) {
						
						if(wcDevice.getMonitortype() == 4 && !wcDevice.isTemperature_model())
							wcDevice.setShow_activate(false);
						else		
							wcDevice.setShow_activate(true);
						
						wcDevice.setIs_freePlan(false);
					} else {
						
						wcDevice.setIs_freePlan((boolean) result[54]);
						if(wcDevice.getMonitortype() == 8 || wcDevice.getMonitortype() == 12) {
							boolean isActive = iCreditSystemService.getMeariPlanExpired(userID, gatewayId, (int) wcDevice.getPeriodId());
							if(!isActive) {
								wcDevice.setShow_activate(true);
								wcDevice.setIs_freePlan(false);
							}else {
								wcDevice.setShow_activate(false);
							}
							
							if(wcDevice.isShow_activate() && (String) result[87] != null) {
								if (allProdSub != null && allProdSub.getSubscriptionId() != null) {
									wcDevice.setShow_activate(false);
								}
							}
						}else {
							wcDevice.setShow_activate(false);
						}
						
					}
					
					if (result[55] != null) {
						wcDevice.setSub_id((String) result[55]);
					}
					
					if (result[57] != null && ((int) result[57]) != 0) {
						wcDevice.setRecording_time((int) result[57]);
					}
					
					if (result[58] != null && ((int) result[58]) != 0) {
						wcDevice.setAlarm_interval((int) result[58]);
					}
					
					if (result[59] != null && !((String) result[59]).isEmpty()) {
						wcDevice.setNoise_sensitivity((String) result[59]);
					}
					
					if (result[60] != null) {
						wcDevice.setEvent_recording((boolean) result[60]);
					}
					
					if (result[61] != null) {
						wcDevice.setPet_detection((boolean) result[61]);
					}
					
					if (result[63] != null) {
						String[] emailIds = ((String) result[63]).split("\\,");
						ArrayList<String> email_id_list = new ArrayList<>();
						for( String email : emailIds ) {
							if( email.equalsIgnoreCase("NA") )
								continue;
							email_id_list.add(email);
						}
						wcDevice.setAlert_email_id( email_id_list );
					}
					
					if (result[64] != null) {
						log.info("parsing wc_alert mobile no :: gateway_id : "+ wcDevice.getGatewayid());
						String[] mobileNos = ((String) result[64]).split("\\,");
						ArrayList<JMobile> mobileNoList = new ArrayList<>(); 
						for( String no : mobileNos ) {
							try {
								if( no.equalsIgnoreCase("NA") )
									continue;
								String mobcountry = no.split("-")[0];
								mobcountry = mobcountry.contains("+") ? mobcountry : "+"+mobcountry;
								JMobile mobile = new JMobile(mobcountry, no.split("-")[1]);
								mobileNoList.add(mobile);	
							} catch (Exception e) {
								log.error("Error while parsing mobile no : "+ no +" :: Error : "+ e.getLocalizedMessage());
							}
						}
						wcDevice.setAlert_mobile_no(mobileNoList);
					}
					
					if (result[65] != null) {
						wcDevice.setThrowing_beep((boolean) result[65]);
					}
					
					if (result[66] != null) {
						wcDevice.setIs_battery_mode((boolean) result[66]);
						wcDevice.setBattery_available( (boolean) result[66] );
					}
					
					if (result[67] != null)
						wcDevice.setIs_humidity((boolean) result[67]);
					
					if (result[68] != null)
						wcDevice.setWebRTC((boolean) result[68]);
					
					if( wcDevice.isBattery_available() ) {
						if (result[69] != null)
							wcDevice.setRunning_on_battery((boolean) result[69]);	
					}
					
					if (result[70] != null)
						wcDevice.setShow_sd((boolean) result[70]);
					
					if (result[71] != null)
						wcDevice.setVehicle_detection((boolean) result[71]);
					
					if (result[72] != null)
						wcDevice.setDevice_unique_id((String) result[72]);
					
					if (result[73] != null)
						wcDevice.setStream_url(((String) result[73]) + path_postfix_device );
					if (result[74] != null)
						wcDevice.setAitreat_onoff((boolean) result[74]);
					if (result[75] != null)
						wcDevice.setTreat_count((int) result[75]);
					if (result[76] != null)
						wcDevice.setAitreat_interval((int) result[76]);
					if (result[77] != null)
						wcDevice.setAitreat_maxcount((int) result[77]);
					if (result[78] != null)
						wcDevice.setAuto_tracking((boolean) result[78]);
					if (result[79] != null)
						wcDevice.setShow_vehicle_detection((boolean) result[79]);

					if (result[88] != null)
						wcDevice.setIs_microphone_enabled((boolean) result[88]);

					if (result[89] != null)
						wcDevice.setIs_recordvideo_enabled((boolean) result[89]);

					if (result[90] != null)
						wcDevice.setIs_speaker_enabled((boolean) result[90]);

					if (result[91] != null)
						wcDevice.setHuman_detection_day((boolean) result[91]);

					if (result[92] != null)
						wcDevice.setHuman_detection_night((boolean) result[92]);

					if(result[93] != null)
						wcDevice.setAuto_sensitive_enabled((boolean) result[93]);

					if(result[94] != null)
						wcDevice.setMeari_update_popup((boolean) result[94]);

					if (wcDevice.getMonitortype() == 5) {
						wcDevice.setTitle("");
						wcDevice.setDesc(minicam_plan_content);
						wcDevice.setBtn(minicam_btn_content);
						wcDevice.setPaid_title("");
						wcDevice.setPaid_desc(minicam_plan_content);
						wcDevice.setPaid_btn(minicam_btn_content);
					} else if (wcDevice.getMonitortype() == 6) {
						wcDevice.setTitle("");
						wcDevice.setDesc(wcpro_plan_content);
						wcDevice.setBtn(wcpro_btn_content);
						wcDevice.setPaid_title("");
						wcDevice.setPaid_desc(wcpro_plan_content);
						wcDevice.setPaid_btn(wcpro_btn_content);
					} else if (wcDevice.getMonitortype() == 4) {
						wcDevice.setTitle("");
						wcDevice.setDesc(wcultra_plan_content);
						wcDevice.setBtn(wcultra_btn_content);
						wcDevice.setPaid_title("");
						wcDevice.setPaid_desc(wcultra_plan_content);
						wcDevice.setPaid_btn(wcultra_btn_content);
					} else if (wcDevice.getMonitortype() == 8) {
						wcDevice.setTitle("");
						wcDevice.setDesc(solarcam_plan_content);
						wcDevice.setBtn(solarcam_btn_content);
						wcDevice.setPaid_title("");
						wcDevice.setPaid_desc(solarcam_plan_content);
						wcDevice.setPaid_btn(solarcam_btn_content);
					} else if (wcDevice.getMonitortype() == 12) {
					wcDevice.setTitle("");
					wcDevice.setDesc(solarminicam_plan_content);
					wcDevice.setBtn(solarcam_btn_content);
					wcDevice.setPaid_title("");
					wcDevice.setPaid_desc(solarminicam_plan_content);
					wcDevice.setPaid_btn(solarcam_btn_content);
				}
					
					wcDevice.setWeb_rtc_application( path_prefix + wcDevice.getMeid() );
					wcDevice.setWeb_rtc_stream_name( path_postfix_device + _helper.generateWowzaHash( wcDevice.getMeid() , path_postfix_device) );
					if( wcDevice.isTemperature_model() && wcDevice.getMonitortype() != 5 && wcDevice.getMonitortype() != 6) {
						JAlertsWC alertsWC = alertcfgServiceV4.getWCAlertStatus( wcDevice.getGatewayid(), userID);
						wcDevice.setAlerts( alertsWC );
					}
					
					ArrayList<AlertWC> alertWcList = alertcfgServiceV4.getCurrentDateWCAlerts(wcDevice.getGatewayid()); 
					if( alertWcList != null ) {
						
						int motion_detection_count = 0 ;
						int bark_alert_count = 0;
						int naughtiness = 60;
						int motion_detection_pet_count = 0;
						
						for( AlertWC alertWC : alertWcList ) {
							switch ( alertWC.getAlert_type_id()+"" ) {
							case IrisservicesConstants.ALERT_MOTION_DETECTION_HUMAN:
								motion_detection_count++;
								break;
							case IrisservicesConstants.ALERT_MOTION_DETECTION_DOG:
								motion_detection_count++;
								motion_detection_pet_count++;
								break;
							case IrisservicesConstants.ALERT_MOTION_DETECTION_CAT:
								motion_detection_count++;
								motion_detection_pet_count++;
								break;
							case IrisservicesConstants.ALERT_SOUND_DOG:
								bark_alert_count++;
								break;
							case IrisservicesConstants.ALERT_SOUND_CAT:
								bark_alert_count++;
								break;
							}
						}
						
						int real_value_naughtiness = motion_detection_pet_count / 3;
						
						naughtiness = naughtiness + real_value_naughtiness;
						
						if( naughtiness > 100 ) naughtiness = 100;
						
//						if( motion_detection_count >= 80 )  naughtiness = 70;
//						else if( motion_detection_count >= 90 )  naughtiness = 80;
//						else if( motion_detection_count >= 100 )  naughtiness = 100;
						
						wcDevice.setMotion_detected_count(motion_detection_count);
						wcDevice.setBark_alert_count(bark_alert_count);
						wcDevice.setNaughtiness(naughtiness);
						
					}
					
					JPetprofileFlutter pet_profile = petSpeciesServices.getJPetprofiles( wcDevice.getGatewayid() );
					wcDevice.setPet_profile(pet_profile);
					
					String meariSubkey = iCreditSystemService.getMeariKey(userID, gatewayId, (int) wcDevice.getPeriodId());
					wcDevice.setMeariSubKey(meariSubkey);

					if (wcDevice.getMeariSubKey() != null && wcDevice.getMeariSubKey().equalsIgnoreCase("NA") && allProdSub != null && allProdSub.getSubscriptionStatus() != null && allProdSub.getSubscriptionStatus().equalsIgnoreCase("in_trial")) {
						wcDevice.setPeriodId(1);
					}
					
					String start_video_recording_url = start_video_recording_wowza_post_m;
					String start_video_recording_url_body = start_video_recording_wowza_post_m_body;
					String stop_video_recording_url = stop_video_recording_wowza_put_m;
					
					start_video_recording_url = start_video_recording_url.replace("passcode", wowza_video_record_passcode);
					stop_video_recording_url = stop_video_recording_url.replace("passcode", wowza_video_record_passcode);
					
					start_video_recording_url = start_video_recording_url.replace("wowza_dns", wowza_api_admin_dns);
					start_video_recording_url_body = start_video_recording_url_body.replace("wowza_dns", wowza_api_admin_dns);
					stop_video_recording_url = stop_video_recording_url.replace("wowza_dns", wowza_api_admin_dns);
					
					start_video_recording_url = start_video_recording_url.replace("meid", path_prefix + wcDevice.getMeid());
					start_video_recording_url_body = start_video_recording_url_body.replace("pathprefix", path_postfix_device);
					stop_video_recording_url = stop_video_recording_url.replace("meid", path_prefix + wcDevice.getMeid());
					
					wcDevice.setStart_video_recording_url( start_video_recording_url );
					wcDevice.setStart_video_recording_url_body( start_video_recording_url_body );
					wcDevice.setStop_video_recording_url( stop_video_recording_url );
					wcDevice.setHangup_battery_percentage( hangup_battery_percentage );
					wcDevice.setShow_smart_light_setting(show_smart_light_setting);
					wcDeviceList.add(wcDevice);
				}
			} else {
				log.info("User don't have device");
				return wcDeviceList;
			}
		} catch (Exception e) {
			log.error("Exception occured while getting DeviceList :  " + e.getLocalizedMessage());
			return wcDeviceList;
		}
		return wcDeviceList;
	}

	@Override
	public int saveOrUpdateGatewayProfile(long gatewayId, long profileId) {
		log.info("Entered saveOrUpdateGatewayProfile ::");
		Session ses = sessionFactory.getCurrentSession();
		try {
			String qry = "insert into gateway_profile(gateway_id,petprofile_id) value("+gatewayId+", "+profileId+");";
			SQLQuery qry1 = ses.createSQLQuery(qry);
			int status = qry1.executeUpdate();

			if (status > 0) {
				log.info("updated gateway_profile");
				return status;
			}
			return status;
		} catch (ConstraintViolationException e) {
			log.error("Handling gateway profile creation failure :");
			
//			String qry = "update gateway_profile set gateway_id="+gatewayId+" where petprofile_id="+profileId+ " and "
//					+ "gateway_id="+gatewayId;
//			SQLQuery qry1 = ses.createSQLQuery(qry);
//			int status = qry1.executeUpdate();
//
//			if (status > 0) {
//				log.info("updated gateway_profile");
//				return status;
//			}			
		}catch (Exception e) {
			log.error("Error on saveOrUpdateGatewayProfile : "+e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public long getGatewayProfile(long gatewayId) {
		log.info("Entered getGatewayProfile : gateway id : " + gatewayId);
		try {
			String qry = "SELECT petprofile_id FROM gateway_profile WHERE gateway_id = '" + gatewayId + "';";
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);

			if (!query.list().isEmpty()) {
				long id = ((BigInteger) query.list().get(0)).longValue();
				return id;
			}
		} catch (Exception e) {
			log.error("Exception in getGatewayProfile : " + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public boolean updateGatewayProfile(long gateway_id, long profile_id) {
		log.info("Entered saveOrUpdateGatewayProfile ::");
		Session ses = sessionFactory.getCurrentSession();
		try {
			String qry = "INSERT INTO `gateway_profile` (`gateway_id`, `petprofile_id`) VALUES ("+ gateway_id +", "+ profile_id +"); ";
			
			SQLQuery qry1 = ses.createSQLQuery(qry);
			int status = qry1.executeUpdate();

			if (status > 0) {
				log.info("updated gateway_profile");
				return true;
			}
		} catch (Exception e) {
			log.error("Error on updateGatewayProfile : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public List<WCDeviceList> getSubDeviceList( String userEmail) {

		log.info("Entered :: getSubDeviceList : userEmail : "+ userEmail );

		List<WCDeviceList> wcDeviceList = new ArrayList<WCDeviceList>();

		try {
			Session ses = sessionFactory.getCurrentSession();

			String sqlQry = "SELECT DISTINCT "
					+ " G.id, "						//0
					+ " G.macid,"					//1
					+ " G.name,"					//2
					+ " AM.monitor_type_id," 		//3
					+ " G.qrcode,"					//4
					+ " SUG.gateway_id,"			//5
					+ " SUG.sub_user_id,"			//6
					+ " G.imageurl,"				//7
					+ " AM.is_wifidelete,"			//8
					+ " AM.model,"					//9
					+ " AM.id as assetmodelid,"		//10
					+ " AM.is_wifi_connection,"		//11
					+ " AM.total_ssid_count,"		//12
					+ " AM.is_manual_connection,"	//13
					+ " G.purchased_from_others,"	//14
					+ " G.show_order_id,"			//15
					+ " G.meid, "					//16
					+ " GS.streaming,"				//17
					+ " GS.night_vision,"			//18
					+ " GS.quality,"				//19
					+ " GS.device_angle,"			//20
					+ " GS.speaker_volume, "		//21
					+ " GS.mic_status,"				//22
					+ " AM.min_rotation,"			//23
					+ " AM.max_rotation,"			//24
					+ " U.country,"					//25
					+ " G.auto_update,"				//26
					+ " IF (FA.fota_status IS NULL,'NA', FA.fota_status) AS fota_status," //27
					+ " G.firmware_ver, "			//28
					+ " GS.auto_night_vision, "		//29
					+ " PS.speciesname, "			//30
					+ " G.user_time_zone, "			//31
					+ " GS.motion_detection, "		//32
					+ "	GS.aitreat_onoff, "			//33
					+ "	GS.treat_count, "			//34
					+ "	GS.aitreat_interval "		//35		
					+ " FROM gateway G JOIN gateway_status GS on "
					+ "GS.gateway_id=G.id JOIN sub_user_gateway SUG ON SUG.gateway_id = G.id JOIN user U ON "
					+ "U.username=SUG.sub_user_email JOIN assetmodel AM ON G.model_id = AM.id"
					+ " LEFT JOIN fota_to_asset FA ON G.meid = FA.meid "
					+ " LEFT JOIN pet_profile PP ON PP.gateway_id = G.id "
					+ " LEFT JOIN petspecies PS ON PS.id = PP.speciesid "
					+ "WHERE SUG.sub_user_email= :user_email AND SUG.enable=1 AND invitation_status=1";
//			if (gatewayID != 0) {
//				sqlQry += "AND UG.gatewayId= :gatewayid";
//			}

			sqlQry += " order by G.id ";

			SQLQuery gatewayQry = ses.createSQLQuery(sqlQry);
			gatewayQry.setParameter("user_email", userEmail);
//			if (gatewayID != 0)
//				gatewayQry.setParameter("gatewayid", gatewayID);
			List<Object[]> gatewayRes = gatewayQry.list();
			int total_ssid_count = 0;

			if (gatewayRes.size() > 0) {

				String country = (String) gatewayRes.get(0)[25];
				if (country == null || country.equalsIgnoreCase("NA") || country.equalsIgnoreCase("IN"))
					country = "US";
				String supportM = supportContactEmail.get(country) != null ? supportContactEmail.get(country) : supportContactEmail.get("US");
				String supportP = supportContactNumber.get(country) != null ? supportContactNumber.get(country) : supportContactNumber.get("US");

				for (Object[] result : gatewayRes) {

					WCDeviceList wcDevice = new WCDeviceList();

					long gatewayId = ((BigInteger) result[0]).longValue();
					if (result[16] != null) {
						wcDevice.setMeid((String) result[16]);
					}
					if (result[1] != null) {
						wcDevice.setMacid((String) result[1]);
					}

					if (result[2] != null)
						wcDevice.setGatewayname((String) result[2]);
					if (result[4] != null)
						wcDevice.setQrcode((String) result[4]);
					if (result[5] != null)
						wcDevice.setGatewayid(((BigInteger) result[5]).longValue());
					if (result[6] != null)
						wcDevice.setUserid(((BigInteger) result[6]).longValue());

					if (result[7] != null)
						wcDevice.setImageurl((String) result[7]);

					if (result[8] != null)
						wcDevice.setIs_wifidelete((boolean) result[8]);

					long assetModelId = ((BigInteger) result[10]).longValue();

					if (result[11] != null) {
						wcDevice.setIs_wifi_connection((boolean) result[11]);
						if ((boolean) result[11]) {
							wcDevice.setWifi_ssid("WC_" + (String) result[1]);
							wcDevice.setWifi_password(wc_wifi_password);
						}
					}

					if (result[12] != null) {
						total_ssid_count = (int) result[12];
						wcDevice.setTotalwificount(total_ssid_count);
						wcDevice.setRemainingwificount(total_ssid_count);
					}

					if (result[13] != null) {
						wcDevice.setIs_manual_connection((boolean) result[13]);
					}

					String suffixImgtype = "assetmodelid_" + assetModelId;

//					if (!type.trim().isEmpty()) {
//						log.info("Mobile platform : " + type + ",  Img Name :" + suffixImgtype + "_wifi_pairing_img");
//						AppImage appimage = appImageService.getAppImages(type, suffixImgtype + "_wifi_pairing_img");
//						if (appimage != null)
//							wcDevice.setWifi_pairing_img(appimage.getImg_path());
//					}

					List<WifiInfo> wifi_info_list = wifiServiceV4.getWiFiList(gatewayId);

					if( wifi_info_list.size() > 0 ) {
						WifiInfo wifiinfo = wifi_info_list.get(0);
						if( wifiinfo.getBandwidth().contains("5") ) {
							wcDevice.setShow_hd(true);
						}
					}
					
					int remainingWifiCount = total_ssid_count - wifi_info_list.size();
					if (remainingWifiCount < 0)
						remainingWifiCount = 0;

					wcDevice.setRemainingwificount(remainingWifiCount);
					wcDevice.setWifiinfolist(wifi_info_list);
					if (result[14] != null && result[15] != null) {
						boolean purchased_from_others = (boolean) result[14];
						boolean show_orderid = (boolean) result[15];
						if (show_orderid) {
							wcDevice.setWarranty_claimed(true);
							wcDevice.setWarranty_claim_msg("Your Warranty is already registered");
						}

						if (purchased_from_others) {
							wcDevice.setPurchased_from_others(true);
							String eRes = RegisterUserError.contectMsgNormalTxt;
							String msg = eRes.replace("#SP#", supportP).replace("#SM#", supportM)
									+ " to register your warranty.";
							wcDevice.setWarranty_claim_msg(msg);
						}
					}
					if (result[17] != null)
						wcDevice.setStreaming((boolean) result[17]);
					if (result[18] != null)
						wcDevice.setNight_vision((boolean) result[18]);
					if (result[19] != null)
						wcDevice.setQuality((String) result[19]);
					if (result[20] != null)
						wcDevice.setDevice_angle(((BigInteger) result[20]).intValue());
					if (result[21] != null)
						wcDevice.setSpeaker_volume((int) result[21]);
					if (result[22] != null)
						wcDevice.setMic_status((boolean) result[22]);
					if (result[23] != null)
						wcDevice.setMin_rotation((int) result[23]);
					if (result[24] != null)
						wcDevice.setMax_rotation((int) result[24]);
					if (result[26] != null)
						wcDevice.setAuto_update((boolean) result[26]);
					if (result[27] != null)
						wcDevice.setFota_status((String) result[27]);
					if (result[28] != null)
						wcDevice.setCurrent_fota_version((String) result[28]);
					if (result[29] != null)
						wcDevice.setAuto_night_vision((boolean) result[29]);
					if (result[30] != null)
						wcDevice.setDevice_for((String) result[30]);
					if (result[31] != null)
						wcDevice.setUser_time_zone((boolean) result[31]);
					if (result[32] != null)
						wcDevice.setIs_motion_detection((boolean) result[32]);
					if (result[33] != null)
						wcDevice.setAitreat_onoff((boolean) result[33]);
					if (result[34] != null)
						wcDevice.setAitreat_interval((int) result[34]);
					if (result[35] != null)
						wcDevice.setTreat_count((int) result[35]);
					
					
					wcDevice.setIs_sub_user_gateway( true );
					wcDeviceList.add(wcDevice);
				}
			} else {
				log.info("User don't have device");
				return wcDeviceList;
			}
		} catch (Exception e) {
			log.error("Exception occured while getting getSubDeviceList :  " + e.getLocalizedMessage());
			return null;
		}
		return wcDeviceList;
	}

	@Override
	public int getSubUserCount(long userId) {
		log.info("Entered into getSubUserCount :: user_id : "+ userId);
		try {
			
			String qry = "SELECT COUNT(*) FROM `sub_user_gateway` WHERE user_id = :user_id";
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userId);
			
			List<Object> obj = query.list();
		
			return ( (BigInteger) obj.get(0) ).intValue();
			

		} catch (Exception e) {
			log.error("Error in getSubUserCount :: Error : "+ e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public GatewayToAlexa getGatewayToAlexaByUserId(long user_id) {
		log.info("Entered into getGatewayToAlexaByUserId :: user_id : "+ user_id);
		try {
			List gatewayToAlexaList = sessionFactory.getCurrentSession().createCriteria(GatewayToAlexa.class).add(Restrictions.eq("user_id", user_id)).list();
			
			if( gatewayToAlexaList.isEmpty() ) {
				log.info("gateway_to_alexa not found for user_id : "+ user_id);
				return null;
			}
			
			return (GatewayToAlexa) gatewayToAlexaList.get(0);
		} catch (Exception e) {
			log.error("Error in getGatewayToAlexaByUserId :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public GatewayToAlexa saveOrUpdateGatewayToAlexa(GatewayToAlexa gatewayToAlexa) {
		log.info("Entered into saveOrUpdateGatewayToAlexa :: user_id : "+ gatewayToAlexa.getUser_id() + " :: gateway_id : "+ gatewayToAlexa.getGateway_id() );
		try {
			return (GatewayToAlexa) sessionFactory.getCurrentSession().merge(gatewayToAlexa);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateGatewayToAlexa :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public List<WCDeviceListWeb> getWCamDeviceList( long userID) {

		log.info("Entered :: getDeviceList :  userId : " + userID);

		List<WCDeviceListWeb> wcDeviceList = new ArrayList<WCDeviceListWeb>();

		try {
			Session ses = sessionFactory.getCurrentSession();

			String sqlQry = "SELECT DISTINCT "
					+ " G.id, "						//0
					+ " G.macid,"					//1
					+ " G.name,"					//2
					+ " AM.monitor_type_id," 		//3
					+ " G.qrcode,"					//4
					+ " UG.gatewayId,"				//5
					+ " UG.userId,"					//6
					+ " G.imageurl,"				//7
					+ " AM.is_wifidelete,"			//8
					+ " G.installed_date,"			//9
					+ " AM.id as assetmodelid,"		//10
					+ " AM.is_wifi_connection,"		//11
					+ " AM.total_ssid_count,"		//12
					+ " AM.is_manual_connection,"	//13
					+ " G.purchased_from_others,"	//14
					+ " G.show_order_id,"			//15
					+ " G.meid, "					//16
					+ " GS.streaming,"				//17
					+ " GS.night_vision,"			//18
					+ " GS.quality,"				//19
					+ " GS.device_angle,"			//20
					+ " GS.speaker_volume, "		//21
					+ " GS.mic_status,"				//22
					+ " AM.min_rotation,"			//23
					+ " AM.max_rotation,"			//24
					+ " U.country,"					//25
					+ " G.auto_update,"				//26
					+ " IF (FA.fota_status IS NULL,'NA', FA.fota_status) AS fota_status," //27
					+ " G.firmware_ver, "			//28
					+ " GS.auto_night_vision, "		//29
					+ " PS.speciesname, "			//30
					+ " G.user_time_zone, "			//31
					+ " GS.motion_detection, "		//32
					+ " AM.motion_detection AS md,"	//33
					+ " AM.stored_video, "			//34
					+ " GS.is_online,"    			//35
					+ " AM.debug AS show_debug, "	//36
					+ " GS.debug AS is_debug,"		//37
					+ " GS.livetracking_status,"	//38
					+ " GS.livetracking_person,"		//39
					+ " GS.livetracking_boundingbox,"	//40
					+ " GS.livetracking_rotation,"		//41
					+ " AM.live_tracking,"				//42
					+ " OM.orderdate, "					//43
					+ " AM.inventorymodelname "			//44
					+ " FROM gateway G "   
					+ " JOIN gateway_status GS on GS.gateway_id=G.id "
					+ " JOIN usergateway UG ON UG.gatewayID = G.id "
					+ " JOIN user U ON U.id=UG.userId "
					+ " JOIN assetmodel AM ON G.model_id = AM.id "
					+ " LEFT JOIN gateway_profile GP on GP.gateway_id = UG.gatewayId "
					+ " LEFT JOIN fota_to_asset FA ON G.meid = FA.meid "
					+ " LEFT JOIN pet_profile PP ON PP.id = GP.petprofile_id "
					+ " LEFT JOIN petspecies PS ON PS.id = PP.speciesid "
					+ " LEFT JOIN  ordermappingdetails OM ON OM.user_id=U.id "
					+ " WHERE UG.userId= :userid ";
//			if (gatewayID != 0) {
//				sqlQry += "AND UG.gatewayId= :gatewayid";
//			}

			sqlQry += " GROUP BY G.id order by G.id ";

			SQLQuery gatewayQry = ses.createSQLQuery(sqlQry);
			gatewayQry.setParameter("userid", userID);
//			if (gatewayID != 0)
//				gatewayQry.setParameter("gatewayid", gatewayID);
			List<Object[]> gatewayRes = gatewayQry.list();
			int total_ssid_count = 0;

			if (gatewayRes.size() > 0) {

				String country = (String) gatewayRes.get(0)[25];
				if (country == null || country.equalsIgnoreCase("NA") || country.equalsIgnoreCase("IN"))
					country = "US";
				String supportM = supportContactEmail.get(country) != null ? supportContactEmail.get(country) : supportContactEmail.get("US");
				String supportP = supportContactNumber.get(country) != null ? supportContactNumber.get(country) : supportContactNumber.get("US");

				for (Object[] result : gatewayRes) {

					WCDeviceListWeb wcDevice = new WCDeviceListWeb();

					long gatewayId = ((BigInteger) result[0]).longValue();
					if (result[16] != null) {
						wcDevice.setMeid((String) result[16]);
					}
					if (result[1] != null) {
						wcDevice.setMacid((String) result[1]);
					}

					if (result[2] != null)
						wcDevice.setGatewayname((String) result[2]);
					if (result[3] != null)
						wcDevice.setMonitortype( ((BigInteger) result[3]).longValue() );
					if (result[4] != null)
						wcDevice.setQrcode((String) result[4]);
					if (result[5] != null)
						wcDevice.setGatewayid(((BigInteger) result[5]).longValue());
					if (result[6] != null)
						wcDevice.setUserid(((BigInteger) result[6]).longValue());

					if (result[7] != null)
						wcDevice.setImageurl((String) result[7]);

					if (result[8] != null)
						wcDevice.setIs_wifidelete((boolean) result[8]);

					if (result[9] != null)
						wcDevice.setInstalled_date(((Timestamp)result[9]).toString());
					
					long assetModelId = ((BigInteger) result[10]).longValue();

					if (result[11] != null) {
						wcDevice.setIs_wifi_connection((boolean) result[11]);
						if ((boolean) result[11]) {
							wcDevice.setWifi_ssid("WC_" + (String) result[1]);
							wcDevice.setWifi_password(wc_wifi_password);
						}
					}

					if (result[12] != null) {
						total_ssid_count = (int) result[12];
						wcDevice.setTotalwificount(total_ssid_count);
						wcDevice.setRemainingwificount(total_ssid_count);
					}

					if (result[13] != null) {
						wcDevice.setIs_manual_connection((boolean) result[13]);
					}

					String suffixImgtype = "assetmodelid_" + assetModelId;

					List<WifiInfo> wifi_info_list = wifiServiceV4.getWiFiList(gatewayId);

					if( wifi_info_list.size() > 0 ) {
						if( wc_show_hd_all_freq ) {
							wcDevice.setShow_hd(true);
						} else {
							WifiInfo wifiinfo = wifi_info_list.get(0);
							if( wifiinfo.getBandwidth().contains("5") ) {
								wcDevice.setShow_hd(true);
							}	
						}
					}
					
					int remainingWifiCount = total_ssid_count - wifi_info_list.size();
					if (remainingWifiCount < 0)
						remainingWifiCount = 0;

					wcDevice.setRemainingwificount(remainingWifiCount);
					wcDevice.setWifiinfolist(wifi_info_list);
					if (result[14] != null && result[15] != null) {
						boolean purchased_from_others = (boolean) result[14];
						boolean show_orderid = (boolean) result[15];
						if (show_orderid) {
							wcDevice.setWarranty_claimed(true);
							wcDevice.setWarranty_claim_msg("Your Warranty is already registered");
						}

						if (purchased_from_others) {
							wcDevice.setPurchased_from_others(true);
							String eRes = RegisterUserError.contectMsgNormalTxt;
							String msg = eRes.replace("#SP#", supportP).replace("#SM#", supportM)
									+ " to register your warranty.";
							wcDevice.setWarranty_claim_msg(msg);
						}
					}
					if (result[17] != null)
						wcDevice.setStreaming((boolean) result[17]);
					if (result[18] != null)
						wcDevice.setNight_vision((boolean) result[18]);
					if (result[19] != null)
						wcDevice.setQuality((String) result[19]);
					if (result[20] != null)
						wcDevice.setDevice_angle(((BigInteger) result[20]).intValue());
					if (result[21] != null)
						wcDevice.setSpeaker_volume((int) result[21]);
					if (result[22] != null)
						wcDevice.setMic_status((boolean) result[22]);
					if (result[23] != null)
						wcDevice.setMin_rotation((int) result[23]);
					if (result[24] != null)
						wcDevice.setMax_rotation((int) result[24]);
					if (result[26] != null)
						wcDevice.setAuto_update((boolean) result[26]);
					if (result[27] != null)
						wcDevice.setFota_status((String) result[27]);
					if (result[28] != null)
						wcDevice.setCurrent_fota_version((String) result[28]);
					if (result[29] != null)
						wcDevice.setAuto_night_vision((boolean) result[29]);
					if (result[30] != null)
						wcDevice.setDevice_for((String) result[30]);
					if (result[31] != null)
						wcDevice.setUser_time_zone((boolean) result[31]);
					if (result[32] != null)
						wcDevice.setIs_motion_detection((boolean) result[32]);
					if (result[33] != null)
						wcDevice.setShow_motion_detection((boolean) result[33]);
					if (result[34] != null)
						wcDevice.setShow_stored_video((boolean) result[34]);
					if (result[35] != null)
						wcDevice.setis_online((boolean) result[35]);
					if (result[36] != null)
						wcDevice.setShow_debug((boolean) result[36]);
					if (result[37] != null)
						wcDevice.setIs_debug((boolean) result[37]);
					if (result[38] != null)
						wcDevice.setLivetracking_status((boolean) result[38]);
					if (result[39] != null)
						wcDevice.setLivetracking_person((boolean) result[39]);
					if (result[40] != null)
						wcDevice.setLivetracking_boundingbox((boolean) result[40]);
					if (result[41] != null)
						wcDevice.setLivetracking_rotation((boolean) result[41]);
					if (result[42] != null)
						wcDevice.setShow_live_tracking((boolean) result[42]);
					if (result[43] != null)
						wcDevice.setOrder_date(((Timestamp)result[43]).toString());
					if (result[44] != null)
						wcDevice.setModel_name((String)result[44]);
					wcDevice.setModel_id(assetModelId);

					JPetprofileFlutter pet_profile = petSpeciesServices.getJPetprofiles( wcDevice.getGatewayid() );
					wcDevice.setPet_profile(pet_profile);
					
					wcDeviceList.add(wcDevice);
				}
			} else {
				log.info("User don't have device");
				return wcDeviceList;
			}
		} catch (Exception e) {
			log.error("Exception occured while getting DeviceList :  " + e.getLocalizedMessage());
			return null;
		}
		return wcDeviceList;
	}

	@Override
	public GatewayStatus getGatewayStatus(long gatewayId) {
		log.info("Entered into getGatewayStatus :: gateway_id : "+ gatewayId);
		try {
			List gatewayStatusList = sessionFactory.getCurrentSession().createCriteria(GatewayStatus.class).add( Restrictions.eq("gateway_id", gatewayId) ).list();
			
			if( gatewayStatusList.isEmpty() ) {
				log.info("gateway_status not found for gateway_id : "+ gatewayId);
				return null;
			}
			
			return (GatewayStatus) gatewayStatusList.get(0);
		} catch (Exception e) {
			log.error("Error in getGatewayStatus :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public GatewayStatus saveOrUpdateGatewayStatus(GatewayStatus gatewayStatus) {
		log.info("Entered into saveOrUpdateGatewayStatus :: meid : "+ gatewayStatus.getMeid());
		try {
			return (GatewayStatus) sessionFactory.getCurrentSession().merge(gatewayStatus);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateGatewayStatus :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public List<NightVisionMode> getNightVisionMode(long monitortype) {
		log.info("Entered into getNightVisionMode");
		List<NightVisionMode> nightVisionModeList = new ArrayList<>();
		try {
			nightVisionModeList = sessionFactory.getCurrentSession().createCriteria(NightVisionMode.class).add( Restrictions.eq("monitor_type", monitortype) ).list();
		} catch (Exception e) {
			log.error("Error in getNightVisionMode :: Error : "+e.getLocalizedMessage());
		}
		return nightVisionModeList;
	}

	@Override
	public UpgradeDeviceHistory saveOrUpdateUpgradeDeviceHistory(UpgradeDeviceHistory upgradeDeviceHistory) {
		log.info("Entered into saveOrUpdateUpgradeDeviceHistory :: user_id : "+ upgradeDeviceHistory.getUser_id()+" :: old_meid : "+ upgradeDeviceHistory.getOld_meid()+" :: new_meid : "+ upgradeDeviceHistory.getNew_meid());
		try {
			upgradeDeviceHistory = (UpgradeDeviceHistory) sessionFactory.getCurrentSession().merge(upgradeDeviceHistory);
		} catch (Exception e) {
			log.error("Error in getNightVisionMode :: Error : "+e.getLocalizedMessage());
		}
		return upgradeDeviceHistory;
	}

	@Override
	public boolean deleteRemoveGatewayRequest(long user_id, long gateway_id) {
		log.info("Entered into deleteRemoveGatewayRequest :: user_id : "+user_id+" :: gateway_id : "+ gateway_id);
		try {
			String qry = "DELETE FROM `remove_gateway_request` WHERE `user_id`=:user_id AND `gateway_id`=:gateway_id";
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", user_id);
			query.setParameter("gateway_id", gateway_id);
			
			int status = query.executeUpdate();
			
			return (status > 0);
		} catch (Exception e) {
			log.error("Error in deleteRemoveGatewayRequest :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateOldGatewayProfile(long old_gateway_id, long gateway_id) {
		log.info("Entered into updateOldGatewayProfile :: old_gateway_id : "+ old_gateway_id+" :: gateway_id : "+ gateway_id);
		try {
			Session ses = sessionFactory.getCurrentSession();
				String qry = "UPDATE gateway_profile SET gateway_id = :gateway_id WHERE gateway_id = :old_gateway_id";
				SQLQuery query = ses.createSQLQuery(qry);
				query.setParameter("old_gateway_id", old_gateway_id);
				query.setParameter("gateway_id", gateway_id);
				int status = query.executeUpdate();
				if (status > 0) {
					log.info("updated gateway_profile status : true");
					return true;
				}
				log.info("updated gateway_profile status : false");
		} catch (Exception e) {
			log.error("Error in updateOldGatewayProfile :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updatePetProfileGatewayId(long old_gateway_id, long gateway_id) {
		log.info("Entered into updatePetProfileGatewayId :: old_gateway_id : "+ old_gateway_id+" :: gateway_id : "+ gateway_id);
		try {
			Session ses = sessionFactory.getCurrentSession();
				String qry = "UPDATE pet_profile SET gateway_id = :gateway_id WHERE gateway_id = :old_gateway_id";
				SQLQuery query = ses.createSQLQuery(qry);
				query.setParameter("old_gateway_id", old_gateway_id);
				query.setParameter("gateway_id", gateway_id);
				int status = query.executeUpdate();
				if (status > 0) {
					log.info("updated pet_profile status : true");
					return true;
				}
				log.info("updated gateway_profile status : false");
		} catch (Exception e) {
			log.error("Error in updatePetProfileGatewayId :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateGatewayNameByPetProfile(long gatewayId) {
		log.info("Entered into updateGatewayNameByPetProfile :: gateway_id : "+ gatewayId);
		try {
			Session ses = sessionFactory.getCurrentSession();
				String qry = "UPDATE gateway G "
						+ "JOIN gateway_profile GP on GP.gateway_id = G.id "
						+ "JOIN pet_profile PP on PP.id = GP.petprofile_id "
						+ "SET G.name = PP.name WHERE GP.gateway_id = '"+ gatewayId +"'";
				SQLQuery query = ses.createSQLQuery(qry);
				int status = query.executeUpdate();
				if (status > 0) {
					log.info("updated gateway name status : true");
					return true;
				}
				log.info("updated gateway name status : false");
		} catch (Exception e) {
			log.error("Error in updateGatewayNameByPetProfile :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateTimeZoneLastGatewayReport(long gateway_id, String time_zone) {
		log.info("Entered into updateTimeZoneLastGatewayReport :: gateway_id : "+ gateway_id+" :: time_zone : "+time_zone);
		try {
			
			Session ses = sessionFactory.getCurrentSession();
			String qry = "UPDATE lastgatewayreport LGR "
					+ "SET LGR.timezone =:time_zone  WHERE GP.gateway_id =:gateway_id";
			Query query = ses.createSQLQuery(qry)
					.setParameter("time_zone", time_zone)
					.setParameter("gateway_id", gateway_id);
			
			int status = query.executeUpdate();
			if (status > 0) {
				log.info("updated time zone : true");
				return true;
			}
			log.info("updated time zone : false");

		} catch (Exception e) {
			log.error("Error in updateTimeZoneLastGatewayReport :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public GatewayPendingEvent getGatewayPendingEvent(long gateway_id) {
		log.info("Entered into getGatewayPendingEvents :: gateway_id : "+ gateway_id);
		try {
			List<GatewayPendingEvent> gatewayPendingEvent = (List<GatewayPendingEvent>) sessionFactory.getCurrentSession().createCriteria(GatewayPendingEvent.class).add( Restrictions.eq("gateway_id", gateway_id) ).list();
			if( gatewayPendingEvent.isEmpty() ) {
				log.info("No gateway_pending_event for gateway_id : "+ gateway_id);
				return null;
			}
			return gatewayPendingEvent.get(0);
		} catch (Exception e) {
			log.error("Error in getGatewayPendingEvents :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public GatewayPendingEvent saveOrUpdateGatewayPendingEvent(GatewayPendingEvent gatewayPendingEvent) {
		log.info("Entered into saveOrUpdateGatewayPendingEvents :: gateway_id : "+ gatewayPendingEvent.getGateway_id());
		try {
			return (GatewayPendingEvent) sessionFactory.getCurrentSession().merge(gatewayPendingEvent);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateGatewayPendingEvents :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean delGatewayFeature(long id) {
		log.info("Entered delGatewayFeature : "+id);
		try {
			String qry = "DELETE FROM gateway_feature WHERE gateway_id="+id;
			int status = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return status > 0;
		} catch (Exception e) {
			log.error("Error in delGatewayFeature : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public ArrayList<JCategory> getProductCategory() {
		log.info("Entered into getProductCategory");
		ArrayList<JCategory> categorie_list = new ArrayList<>();
		try {
			
			String qry = "SELECT "
					+ "P.product_name, " 		//0
					+ "P.pimg_url, "			//1
					+ "P.pinfo_url, "			//2
					+ "PS.display_name, "		//3
					+ "PS.psimg_url, "			//4
					+ "PS.psinfo_url, "  		//5
					+ "PS.mtype_id, "			//6
					+ "PS.product_id,"			//7
					+ "PS.description,"         //8
					+ "PS.display_order AS `order`,"		//9
					+ "PS.is_plan_available "		//10
					+ "FROM `product_sub_cat` PS  "
					+ "JOIN product P ON P.id = PS.product_id ORDER BY PS.display_order;";
			
			List<Object[]> categoryObject = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			HashMap<String, JCategory> categoryMap = new HashMap<>();
			for (Object[] object : categoryObject) {
				
				String product_name = (String) object[0];
				
				JCategory category = new JCategory();
				
				if( categoryMap.containsKey( product_name ) ) {
					// if more than one product available in category with different mtype , monitor type is 0
					category = categoryMap.get(product_name);
					int curMtype = ((BigInteger) object[6]).intValue();
					if(category.getMonitortype() != curMtype)
						categoryMap.get(product_name).setMonitortype(0); 
					ArrayList<JProduct> product_list = category.getProduct_list();
					JProduct product = new JProduct();
					product.setDisplay_name( (String) object[3] );
					product.setImg_url( (String) object[4] );
					product.setMonitor_id( curMtype );
					product.setDescription( (String) object[8] );
					product.setMonitor_order((int) object[9]);
					product.setInfo_img_url( (String) object[5]);
					product.setIs_plan_available( (boolean) object[10]);
					product_list.add(product);
					
				} else {
					
					ArrayList<JProduct> product_list = category.getProduct_list();
					JProduct product = new JProduct();
					product.setDisplay_name( (String) object[3] );
					product.setImg_url( (String) object[4] );
					product.setMonitor_id( ((BigInteger) object[6]).intValue() );
					product.setDescription( (String) object[8] );
					product.setMonitor_order((int) object[9]);
					product.setInfo_img_url( (String) object[5]);
					product.setIs_plan_available( (boolean) object[10]);
					product_list.add(product);
					
					category.setCategory_name(product_name);
					category.setImg_url( (String) object[1] );
					category.setProduct_list( product_list );
					// if only one product available in category ,actual monitor type is available in category
					category.setMonitortype(((BigInteger) object[6]).intValue());
					
					categoryMap.put( product_name, category );
				}
				
			}
			
			categoryMap.values().stream()
		    .sorted(Comparator.comparing(cat -> cat.getProduct_list().get(0).getMonitor_order()))
		    .forEach(categorie_list::add);
			
		} catch (Exception e) {
			log.error("Error in getProductCategory :: Error : "+ e.getLocalizedMessage());
		}
		return categorie_list;
	}

	@Override
	public ArrayList<JSensorType> getSensorType() {
		log.info("Entered into getSensorType");
		ArrayList<JSensorType> sensortype_list = new ArrayList<>();
		try {
			
			String qry = "SELECT "
					+ "st.id AS sensortypeid,"				//0
					+ "sensortype,"							//1
					+ "sl.id AS locationid,"				//2
					+ "slocation, "							//3
					+ "st.imageurl "						//4
					+ "FROM sensortype st JOIN"
					+ " `sensorlocation` sl ON st.id=sl.sensor_id  WHERE st.enable=1 AND sl.enable=1 ORDER BY st.id,sl.id;";
			
			List<Object[]> sensortypeObject = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			ArrayList<JSensorLocation> sensorlocation_list = new ArrayList<JSensorLocation>();
			HashMap<String, JSensorType> stMap = new HashMap<>();

			for (Object[] object : sensortypeObject) {
				
				String st_name = (String) object[1];
				long st_id =((BigInteger) object[0]).intValue();
				String sensorImage = (String) object[4];
				
				JSensorLocation location = new JSensorLocation();
				location.setSlocation( (String) object[3] );
				location.setId( ((BigInteger) object[2]).intValue());

				if( stMap.containsKey( st_name ) ) {
					JSensorType stype = stMap.get(st_name);

					ArrayList<JSensorLocation> product_list = stype.getLocationList();
					product_list.add(location);

				} else {
					
					ArrayList<JSensorLocation> product_list  = new ArrayList<JSensorLocation>();				
					product_list.add(location);
					
					JSensorType stype = new JSensorType();
					
					stype.setSensor_image( sensorImage );
					stype.setLocationList(product_list);
					stype.setSensortype(st_name);
					stype.setId(st_id);
					stMap.put( st_name, stype );
				}
				
			}			
			stMap.values().forEach( senType -> sensortype_list.add(senType) );
			sensortype_list.sort((p1, p2) -> Long.compare(p1.getId(), p2.getId()));			
		} catch (Exception e) {
			log.error("Error in getSensorType :: Error : "+ e.getLocalizedMessage());
		}
		return sensortype_list;
	}
	
	@Override
	public List<Object> getSensorList( long userid, long gatewayId, long monitor_type_id, String reqVer) {
		log.info("Entered :: OptimizedDoaImpl :: getSensorList:: ");

		List<Object> smDeviceList = new ArrayList<Object>();
		List<JLastNodeSensorReport> smDeviceList1 = new ArrayList<>();

		Query query = null;
		
		String qry = "SELECT "
				+ "G.id, "													//0
				+ "G.name, " 												//1
				+ "LGR.datetime, "											//2
				+ "ST.id AS stId, "									//3
				+ "ST.sensortype, "											//4
				+ "LGR.eventid, "											//5
				+ "EM.event_name, "											//6
				+ "LGR.battery, "											//7
				+ "LGR.rawrssi, "											//8
				+ "MT.name AS devicetype, "									//9
				+ "G.qrcode, "												//10
				+ "MT.id AS monitore_type, "								//11
				+ "PL.id AS plan_id, "										//12
				+ "PL.is_freeplan, "										//13
				+ "GF.sub_id, "												//14
				+ "GF.period_id AS periodid, "								//15
				+ "UT.timezone, "											//16
				+ "ST.imageurl, "											//17
				+ "SL.slocation,"											//18
				+ "LGR.current_status,"									//19
				+ "LGR.status_datetime "									//20
				+ "FROM gateway G "
				+ "LEFT JOIN lastsensorreport LGR ON G.id = LGR.gateway_id "
				+ "LEFT JOIN usergateway UG ON G.id = UG.gatewayid "
				+ "LEFT JOIN assetmodel AM ON G.model_id = AM.id "
				+ "LEFT JOIN monitortype MT ON MT.id = AM.monitor_type_id "
				+ "LEFT JOIN gateway_feature GF ON GF.gateway_id = G.id AND GF.enable=1 "
				+ "LEFT JOIN plan PL ON PL.id = GF.plan_id "
				+ "LEFT JOIN sensortype ST ON ST.id = G.sensor_type_id "
				+ "LEFT JOIN eventid_mapping EM ON EM.event_id = LGR.eventid "
				+ "LEFT JOIN user_timezone UT ON UT.user_id =:userid "
				+ "LEFT JOIN sensorlocation SL ON SL.id = G.sensor_location_type_id "
				+ "WHERE MT.id=9  AND MT.enable=1 AND UG.userid =:userid ";

		if (gatewayId != 0) {
			qry += " AND UG.gatewayId=:gatewayid";
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry);
			query.setParameter("userid", userid);
			query.setParameter("gatewayid", gatewayId);
		} else {
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry);
			query.setParameter("userid", userid);
		}
		
		List<Object[]> greports = query.list();
		
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		if (greports.size() > 0) {
			
			for (Object[] result : greports) {
			JLastNodeSensorReport lastSenRpt = new JLastNodeSensorReport();
			
			 long gatewayid = ((BigInteger) result[0]).longValue();
			 lastSenRpt.setGatewayid(gatewayid);
			 lastSenRpt.setGatewayname((String) result[1]);
			 lastSenRpt.setBattery((Integer) result[7] != null ? (Integer) result[7] : 0);
			 lastSenRpt.setWifiRange((Integer) result[8] != null ? (Integer) result[8] : 0);
			 lastSenRpt.setQrcode((String) result[10]);
			 lastSenRpt.setMonitortype(((BigInteger) result[11]).longValue());
			 lastSenRpt.setSensor_type((String) result[4]);
			 long sencode = ((BigInteger) result[3]).longValue();
			 lastSenRpt.setSensor_type_code(sencode);
			 lastSenRpt.setImgUrl((String) result[17]);
				if ((Integer) result[19] == null || (Integer) result[19] == 0) {
					if (lastSenRpt.getSensor_type_code() == 1) {
						lastSenRpt.setEvent_name("Open");
						lastSenRpt.setCurr_status(1);
					} else if (lastSenRpt.getSensor_type_code() == 2) {
						lastSenRpt.setEvent_name("Normal");
						lastSenRpt.setCurr_status(2);
					} else if (lastSenRpt.getSensor_type_code() == 3) {
						lastSenRpt.setEvent_name("Normal");
					}

				} else if ((Integer) result[19] != null && (Integer) result[19] > 0) {
					if (lastSenRpt.getSensor_type_code() == 1 && (Integer) result[19] == 1) {
						lastSenRpt.setEvent_name("Open");
					} else if (lastSenRpt.getSensor_type_code() == 1 && (Integer) result[19] == 2) {
						lastSenRpt.setEvent_name("Close");
					} else if (lastSenRpt.getSensor_type_code() == 2 && (Integer) result[19] == 1) {
						lastSenRpt.setEvent_name("Leak Detected");
					} else if (lastSenRpt.getSensor_type_code() == 2 && (Integer) result[19] == 2) {
						lastSenRpt.setEvent_name("Normal");
					} else if (lastSenRpt.getSensor_type_code() == 3) {
						lastSenRpt.setEvent_name("Normal");
					}
					lastSenRpt.setCurr_status((Integer) result[19]);
				}
			 lastSenRpt.setSenLocation((String) result[18]);
			 String timeZone = (String) result[16] != null ? (String) result[16] : "+00:00";
			 SimpleDateFormat dateFormatto = new SimpleDateFormat("MMM dd, yyyy hh:mm aa");
			 if (!reqVer.equals("v5.0")) {
				 dateFormatto = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			}
			 dateFormatto.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone)); 
			 if((Integer) result[19] != null && (Date) result[20] != null) {
				 if((String) result[5] != null && !((String) result[5]).equals("00000001") && !((String) result[5]).equals("00000100")) {
					 Date reportDatetime = _helper.timeZoneConverterfromutc( "yyyy-MM-dd HH:mm:ss","+00:00", timeZone, ((Date) result[20]).toString());
					 lastSenRpt.setDatetime(dateFormatto.format(reportDatetime));
				 }else {
					 lastSenRpt.setDatetime(dateFormat.format((Date) result[20]));
				 }
			 }
			 boolean paired = wifiInfoServiceV4.isWifiConPaired(gatewayid);
			 lastSenRpt.setPaired(paired);
			 
			 smDeviceList1.add(lastSenRpt);
			}
			
		}
		smDeviceList.addAll(smDeviceList1);
		return smDeviceList;
	}
	
	public List<Object> getSensorListByCode(long userid, long gatewayId, String sensorCode, String os) {
		log.info("Entered :: OptimizedDoaImpl :: getSensorList:: ");

		List<Object> smDeviceList = new ArrayList<Object>();
		List<Object> smDeviceList1 = new ArrayList<>();

		Query query = null;
		
		String qry = "SELECT "
				+ "G.id, "													//0
				+ "G.name, " 												//1
				+ "LGR.datetime, "											//2
				+ "ST.id AS stId, "									//3
				+ "ST.sensortype, "											//4
				+ "LGR.eventid, "											//5
				+ "EM.event_name, "											//6
				+ "LGR.battery, "											//7
				+ "LGR.rawrssi, "											//8
				+ "MT.name AS devicetype, "									//9
				+ "G.qrcode, "												//10
				+ "MT.id AS monitore_type, "								//11
				+ "PL.id AS plan_id, "										//12
				+ "PL.is_freeplan, "										//13
				+ "GF.sub_id, "												//14
				+ "GF.period_id AS periodid, "								//15
				+ "UT.timezone, "											//16
				+ "ST.imageurl, "											//17
				+ "SL.slocation, "											//18
				+ "G.meid,"												//19
				+ "G.macid, "											//20
				+ "G.sensor_location_type_id, "							//21
				+ "ST.battery_max, "									//22
				+ "ST.battery_min, "									//23
				+ "LGR.rssi, "											//24
				+ "G.installed_date,"									//25
				+ "LGR.current_status,"									//26
				+ "LGR.status_datetime, "								//27
				+ "LGR.pkttime_utc_datetime, "							//28
				+ "G.dnr_interval "							            //29
				+ "FROM gateway G "
				+ "JOIN sensorlocation SL ON SL.id = G.sensor_location_type_id "
				+ "JOIN usergateway UG ON G.id = UG.gatewayid "
				+ "JOIN assetmodel AM ON G.model_id = AM.id "
				+ "JOIN monitortype MT ON MT.id = AM.monitor_type_id "
				+ "JOIN sensortype ST ON ST.id = G.sensor_type_id AND ST.sensorcode=:sensorCode  "
				+ "LEFT JOIN user_timezone UT ON UT.user_id = UG.userid "
				+ "LEFT JOIN lastsensorreport LGR ON G.id = LGR.gateway_id "
				+ "LEFT JOIN gateway_feature GF ON GF.gateway_id = G.id AND GF.enable=1 "
				+ "LEFT JOIN plan PL ON PL.id = GF.plan_id "
				+ "LEFT JOIN eventid_mapping EM ON EM.event_id = LGR.eventid "
				+ "WHERE MT.id=9  AND MT.enable=1 AND UG.userid =:userid ";

		if (gatewayId != 0) {
			qry += " AND UG.gatewayId=:gatewayid";
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry);
			query.setParameter("userid", userid);
			query.setParameter("gatewayid", gatewayId);
			query.setParameter("sensorCode",sensorCode);
		} else {
			query = this.slave4SessionFactory.getCurrentSession()
					.createSQLQuery(qry);
			query.setParameter("userid", userid);
			query.setParameter("sensorCode",sensorCode);
		}
		
		List<Object[]> greports = query.list();

		if (greports.size() > 0) {

			for (Object[] result : greports) {

				long sencode = ((BigInteger) result[3]).longValue();
				boolean dnrVal = true;
				int dnrInt = (Integer) result[29] != null ? (Integer) result[29] : 540;

				long dnr_milliseconds = (60 * dnrInt * 1000);
				if ((Timestamp) result[28] != null) {
					Date dateDnr = new Date(((Timestamp) result[28]).getTime() + dnr_milliseconds);
					Date currentDate = new Date();

					if (currentDate.after(dateDnr)) {
						dnrVal = false;
					}
				} else {
					dnrVal = false;
				}

				if (sencode == 1) {
					JDoorSensor lastSenRpt = new JDoorSensor();

					long gatewayid = ((BigInteger) result[0]).longValue();
					lastSenRpt.setGatewayid(gatewayid);
					lastSenRpt.setGatewayname((String) result[1]);
					lastSenRpt.setBattery((Integer) result[7] != null ? (Integer) result[7] : 0);
					lastSenRpt.setWifiRange((Integer) result[8] != null ? (Integer) result[8] : 0);
					lastSenRpt.setQrcode((String) result[10]);
					lastSenRpt.setMonitortype(((BigInteger) result[11]).longValue());
					lastSenRpt.setSensor_type((String) result[4]);
					lastSenRpt.setMeid((String) result[19]);
					lastSenRpt.setSensor_type_code(sencode);
					lastSenRpt.setSenlocationtypeid((Integer) result[21] != null ? (Integer) result[21] : 0);
					lastSenRpt.setBattery_max((Integer) result[22] != null ? (Integer) result[22] : 0);
					lastSenRpt.setBattery_min((Integer) result[23] != null ? (Integer) result[23] : 0);
					
					if((Integer) result[26] != null && (Integer) result[26] > 0) {
						lastSenRpt.setCurr_status((Integer) result[26]);
					}
					
					if(lastSenRpt.getCurr_status() == 2) {
						 lastSenRpt.setEvent_name("Close");
					}
					
					lastSenRpt.setImgUrl((String) result[17]);
					lastSenRpt.setSenLocation((String) result[18]);
					String timeZone = (String) result[16] != null ? (String) result[16] : "+00:00";
					List<Object> ble_device_list = wifiInfoServiceV4.getSensorWifiList(lastSenRpt.getGatewayid());
					lastSenRpt
							.setWifiinfolist(ble_device_list.isEmpty() ? new ArrayList<Object>() : ble_device_list);
					SimpleDateFormat dateFormatto = new SimpleDateFormat("MMM dd, yyyy, hh:mm a");
					dateFormatto.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
					if ((Integer) result[26] !=null && (Integer) result[26] > 0 && (Date) result[27] != null) {
						Date reportDatetime = _helper.timeZoneConverterfromutc("yyyy-MM-dd HH:mm:ss", "+00:00",
								timeZone, ((Date) result[27]).toString());
						
						lastSenRpt.setDatetime(dateFormatto.format(reportDatetime));
					}
					
					JAlertSensor alertsWC = alertcfgServiceV4.getSensorAlertStatus(lastSenRpt.getGatewayid(), userid);
					lastSenRpt.setAlertSen(alertsWC);
					if((String) result[24] == null && !ble_device_list.isEmpty()) {
						lastSenRpt.setWifistatus(true);
					}else {
						lastSenRpt.setWifistatus(dnrVal);
					}
					if (result[20] != null) {
						lastSenRpt.setBle_id("M10_" + (String) result[20]);
					}
					if ((Date) result[25] != null) {
						lastSenRpt.setRegistered_date(((Date) result[25]).toString());
					}
					
					smDeviceList1.add(lastSenRpt);
				}

				if (sencode == 2) {
					JWaterLeakSensor lastSenRpt = new JWaterLeakSensor();

					long gatewayid = ((BigInteger) result[0]).longValue();
					lastSenRpt.setGatewayid(gatewayid);
					lastSenRpt.setGatewayname((String) result[1]);
					lastSenRpt.setBattery((Integer) result[7] != null ? (Integer) result[7] : 0);
					lastSenRpt.setWifiRange((Integer) result[8] != null ? (Integer) result[8] : 0);
					lastSenRpt.setQrcode((String) result[10]);
					lastSenRpt.setMonitortype(((BigInteger) result[11]).longValue());
					lastSenRpt.setSensor_type((String) result[4]);
					lastSenRpt.setSensor_type_code(sencode);
					
					if((Integer) result[26] != null && (Integer) result[26] > 0) {
						lastSenRpt.setCurr_status((Integer) result[26]);
					}
					
					if(lastSenRpt.getCurr_status() == 1) {
						 lastSenRpt.setEvent_name("Leak Detected");
					}
					
					lastSenRpt.setImgUrl((String) result[17]);
					lastSenRpt.setSenLocation((String) result[18]);
					lastSenRpt.setMeid((String) result[19]);
					lastSenRpt.setSenlocationtypeid((Integer) result[21] != null ? (Integer) result[21] : 0);
					lastSenRpt.setBattery_max((Integer) result[22] != null ? (Integer) result[22] : 0);
					lastSenRpt.setBattery_min((Integer) result[23] != null ? (Integer) result[23] : 0);
					
					String timeZone = (String) result[16] != null ? (String) result[16] : "+00:00";
					List<Object> ble_device_list = wifiInfoServiceV4.getSensorWifiList(lastSenRpt.getGatewayid());
					lastSenRpt
					.setWifiinfolist(ble_device_list.isEmpty() ? new ArrayList<Object>() : ble_device_list);
					SimpleDateFormat dateFormatto = new SimpleDateFormat("MMM dd, yyyy, hh:mm a");
					dateFormatto.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
					if ((Integer) result[26] !=null && (Integer) result[26] > 0 && (Date) result[27] != null) {
						Date reportDatetime = _helper.timeZoneConverterfromutc("yyyy-MM-dd HH:mm:ss", "+00:00",
								timeZone, ((Date) result[27]).toString());
						
						lastSenRpt.setDatetime(dateFormatto.format(reportDatetime));
					}
					JAlertSensor alertsWC = alertcfgServiceV4.getSensorAlertStatus(lastSenRpt.getGatewayid(), userid);
					lastSenRpt.setAlertSen(alertsWC);
					if((String) result[24] == null && !ble_device_list.isEmpty()) {
						lastSenRpt.setWifistatus(true);
					}else {
						lastSenRpt.setWifistatus(dnrVal);
					}
					if (result[20] != null) {
						lastSenRpt.setBle_id("M10_" + (String) result[20]);
					}
					if ((Date) result[25] != null) {
						lastSenRpt.setRegistered_date(((Date) result[25]).toString());
					}
					smDeviceList1.add(lastSenRpt);
				}

				if (sencode == 3) {
					JWaterLevelSensor lastSenRpt = new JWaterLevelSensor();

					long gatewayid = ((BigInteger) result[0]).longValue();
					lastSenRpt.setGatewayid(gatewayid);
					lastSenRpt.setGatewayname((String) result[1]);
					lastSenRpt.setBattery((Integer) result[7] != null ? (Integer) result[7] : 0);
					lastSenRpt.setWifiRange((Integer) result[8] != null ? (Integer) result[8] : 0);
					lastSenRpt.setQrcode((String) result[10]);
					lastSenRpt.setMonitortype(((BigInteger) result[11]).longValue());
					lastSenRpt.setSensor_type((String) result[4]);
					lastSenRpt.setSensor_type_code(sencode);
					if((String) result[6] == null) {
						 lastSenRpt.setEvent_name("Normal");
					 }else {
						 lastSenRpt.setEvent_name((String) result[6]);
					 }
					lastSenRpt.setImgUrl((String) result[17]);
					lastSenRpt.setMeid((String) result[19]);
					lastSenRpt.setSenLocation((String) result[18]);
					if((String) result[5] != null && ((String) result[5]).equals("4000")) {
						lastSenRpt.setCurr_status(2);
					}else if((Integer) result[26] !=null &&  (String) result[5] != null && ((String) result[5]).equals("8000")) {
						lastSenRpt.setCurr_status(3);
					}
					lastSenRpt.setSenlocationtypeid((Integer) result[21] != null ? (Integer) result[21] : 0);
					lastSenRpt.setBattery_max((Integer) result[22] != null ? (Integer) result[22] : 0);
					lastSenRpt.setBattery_min((Integer) result[23] != null ? (Integer) result[23] : 0);
					String timeZone = (String) result[16] != null ? (String) result[16] : "+00:00";
					List<Object> ble_device_list = wifiInfoServiceV4.getSensorWifiList(lastSenRpt.getGatewayid());
					lastSenRpt
							.setWifiinfolist(ble_device_list.isEmpty() ? new ArrayList<Object>() : ble_device_list);
					SimpleDateFormat dateFormatto = new SimpleDateFormat("MMM dd, yyyy, hh:mm a");
					dateFormatto.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
					if ((Date) result[2] != null) {
						Date reportDatetime = _helper.timeZoneConverterfromutc("yyyy-MM-dd HH:mm:ss", "+00:00",
								timeZone, ((Date) result[2]).toString());
						
						lastSenRpt.setDatetime(dateFormatto.format(reportDatetime));
					}
					
					JAlertSensor alertsWC = alertcfgServiceV4.getSensorAlertStatus(lastSenRpt.getGatewayid(), userid);
					lastSenRpt.setAlertSen(alertsWC);
					if((String) result[24] == null && !ble_device_list.isEmpty()) {
						lastSenRpt.setWifistatus(true);
					}else {
						lastSenRpt.setWifistatus(dnrVal);
					}
					if (result[20] != null) {
						lastSenRpt.setBle_id("M10_" + (String) result[20]);
					}
					if ((Date) result[25] != null) {
						lastSenRpt.setRegistered_date(((Date) result[25]).toString());
					}
					smDeviceList1.add(lastSenRpt);
				}

			}

		}
		smDeviceList.addAll(smDeviceList1);
		return smDeviceList;
	}
	
	@Override
	public ArrayList<JSensorTypeCode> getSensorTypeCode() {
		log.info("Entered into getSensorTypeCode");
		ArrayList<JSensorTypeCode> sensortype_list = new ArrayList<>();
		try {
			
			String qry = "SELECT st.id AS sensortypeid,sensortype,st.sensorcode FROM sensortype st WHERE st.enable=1 ORDER BY st.id;";
			
			List<Object[]> sensortypeObject = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			
			for (Object[] object : sensortypeObject) {
				
				long st_id =((BigInteger) object[0]).intValue();
				String st_name = (String) object[1];
				JSensorTypeCode sensCode = new JSensorTypeCode();
				sensCode.setId(st_id);
				sensCode.setSensortype(st_name);
				sensCode.setSensorcode((String) object[2]);

				sensortype_list.add(sensCode);
				
			}			
				
		} catch (Exception e) {
			log.error("Error in getSensorTypeCode :: Error : "+ e.getLocalizedMessage());
		}
		return sensortype_list;
	}

	@Override
	public boolean checkOrderWithinRange(long gatewayId,String from, String to) {
		log.info("Entered into checkOrderWithinRange :: gatewayId : "+ gatewayId);
		try {
			
			String qry = "SELECT purchase_date FROM `device_subscription` WHERE ( purchase_date BETWEEN "
					+ "DATE(:from) AND DATE(:to)) AND gateway_id=:gatewayId AND DATEDIFF(purchase_date, instal_date)<=15";
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayId", gatewayId);
			query.setParameter("from", from);
			query.setParameter("to", to);

			List<Object> obj = query.list();
			if(!obj.isEmpty())
				return true;
			else
				return false;
			

		} catch (Exception e) {
			log.error("Error in checkOrderWithinRange :: Error : "+ e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean insertDeviceSub(long userid,long gatewayid,String instal_date,long monitortypeid,long sales_channel, boolean isgps) {
		String qry = "INSERT INTO device_subscription(`user_id`, `gateway_id`, `instal_date`,mtype_id,sales_channel,isgps) "
				+ " VALUES('"+userid+"','"+gatewayid+"','"+instal_date+"',"+monitortypeid+", COALESCE((SELECT shortdescription FROM order_channel WHERE id = "+sales_channel+"), 'NA'),"+isgps+")";
		SQLQuery sqlQry = sessionFactory.getCurrentSession().createSQLQuery(qry);
		log.info("insertDeviceSub:" + qry);
		try {
			if(sqlQry.executeUpdate() > 0) {
				log.info("Updated successfully!");
				return true;

			}else
				return false;
		}catch (Exception e) {
			log.error(" insertDeviceSub : " + e.getLocalizedMessage());
			return false;
		}

	}
		
	@Override
	public boolean getGnameExist(String gname,long gateway_id,long cmp_id) {
		log.info("Entered into getGnameExist :gateway_id : "+ gateway_id);
		try {
			String qry = "  SELECT id FROM gateway WHERE cmp_id =:cmp_id AND id!=:gateway_id AND `name`=:gname "
					+ "AND model_id IN( SELECT model_id FROM gateway WHERE id=:gateway_id); ";
			
			List gname_list = sessionFactory.getCurrentSession().createSQLQuery(qry)
							.setParameter("cmp_id", cmp_id)
							.setParameter("gname", gname)
							.setParameter("gateway_id", gateway_id)
							.list();
								
			if( gname_list.isEmpty() ) {
				return false;
			}else			
				return true;
		} catch (Exception e) {
			log.error("Error in getMonitorTypeByCBPlan :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateMeidForGateway(long gateway_id) {
		log.info("Entered updateMeidForGateway :: "+gateway_id);
		String qry = "UPDATE gateway "
				+ "SET meid=CONCAT(meid, '-',id),`macid`=CONCAT(macid, '-' ,id),qrcode=CONCAT(qrcode, '-',`name`),"
				+ "`name`=CONCAT(id, '-' ,`name`) " + 
				"WHERE id=:gatewayid";
		
		String qry1 = "UPDATE asset "
				+ "SET assetaddr=CONCAT(assetaddr, '-',`id`) " + 
				"WHERE id=:gatewayid";
		try {
			SQLQuery sqlQry = sessionFactory.getCurrentSession().createSQLQuery(qry);
			sqlQry.setParameter("gatewayid", gateway_id);
			
			SQLQuery sqlQry1 = sessionFactory.getCurrentSession().createSQLQuery(qry1);
			sqlQry1.setParameter("gatewayid", gateway_id);
			
			if(sqlQry.executeUpdate() > 0) {
				sqlQry1.executeUpdate();
				log.info("Updated successfully!");
				return true;
			}else
				return false;
		}catch (Exception e) {
			log.error(" updateMeidForGateway : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public List<JPetmonitorHistory> getPetMonitorHistory(long gatewayId, String tempunit, String timezone) {
		log.info("Entered into getPetMonitorHistory :gateway_id : "+ gatewayId);
		List<JPetmonitorHistory> historyList = new ArrayList<>();
		try {
			String qry = " SELECT g.id, CONVERT_TZ(g.pkttime_utc, '+00:00', :timezone ) AS `datetime`,g.humidity,g.temperature,g.eventid,g.nmeventid,g.version FROM gatewayreport g "
					+ " WHERE g.gateway_id =:gatewayId AND g.pkttime_utc  BETWEEN DATE_ADD(NOW(), INTERVAL -14 DAY) AND NOW() ORDER BY g.pkttime_utc DESC";
			
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("gatewayId", gatewayId);
			query.setParameter("timezone", timezone);
			
			List<Object[]> gname_list = query.list();
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			DecimalFormat df = new DecimalFormat("0.00");
			for (Object[] object : gname_list) {
				
				JPetmonitorHistory sensCode = new JPetmonitorHistory();
				sensCode.setDatetime(dateFormat.format((Date) object[1]));
				float tempval = (float) object[3];
				if (tempunit.equalsIgnoreCase("F") && tempval > -200)
					tempval = CelsiusToFahrenheit(tempval);
				tempval = Float.parseFloat(df.format(tempval));
				float humidity = Float.parseFloat(df.format((float) object[2]));
				
				sensCode.setHumidity(humidity);
				sensCode.setTemperature(tempval);

				historyList.add(sensCode);
				
			}	
			return historyList;
		} catch (Exception e) {
			log.error("Error in getPetMonitorHistory: "+e.getLocalizedMessage());
		}
		return null;
	}
	
	private float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {

		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}

	private float FahrenheitToCelsius(float tempmvalIndegreeFahrenheit) {
		double degreeCelcius = (Double.valueOf(tempmvalIndegreeFahrenheit).floatValue() - 32) * 5 / 9;
		double roundvalues = Math.round(degreeCelcius * 100.0) / 100.0;
		return (float) roundvalues;
	}

	@Override
	public boolean isGpsDevice(long gateway_id) {
		log.info("Entered isGpsDevice ::");
		try {
			String qry = "SELECT G.id "
					+ "FROM gateway G "
					+ "JOIN assetmodel AM ON AM.id=G.model_id AND AM.isgps=1 "
					+ "WHERE G.id=:gatewayid";
			List gateway_list = sessionFactory.getCurrentSession().createSQLQuery(qry)
							.setParameter("gatewayid", gateway_id)
							.list();
			if(gateway_list.isEmpty()) {
				return false;
			} else
				return true;
		} catch (Exception e) {
			log.error("Error in isGpsDevice :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public boolean isDeviceConfigured(long userId) {
		log.info("Entered into isDeviceConfigured :: user_id : "+ userId);
		try {
			
			String qry = "SELECT COUNT(userid) FROM usergateway UG JOIN gateway G ON G.id = UG.gatewayid "
					+ " JOIN assetmodel AM  ON AM.id = G.model_id WHERE AM.`monitor_type_id` !=9 AND UG.userid= :user_id";
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userId);
			
			List<Object> obj = query.list();
		
			int cnt =  ( (BigInteger) obj.get(0) ).intValue();
			
			if(cnt>0)
				return true;
			else
				return false;			

		} catch (Exception e) {
			log.error("Error in isDeviceConfigured :: Error : "+ e.getLocalizedMessage());
			return false;
		}		
	}
	
	@Override
	public List<JGatewayInfo> getJGatewayInfo(long user_id, long monitorType) {
		log.info("Entered into getJGatewayInfo :: user_id : "+ user_id);
		List<JGatewayInfo> gatewayInfoList = new ArrayList<>();
		try {
			
			String qry = "SELECT G.id AS gateway_id,G.name,G.qrcode AS qr_code,P.imageurl AS image_url"
					   + " FROM gateway G "
					   + " JOIN usergateway UG ON UG.gatewayId = G.id"
					   + " JOIN assetmodel AM ON AM.id = G.model_id AND AM.monitor_type_id =:monitorType  "
					   + " LEFT JOIN pet_profile P ON P.gateway_id = UG.gatewayId "
					   + " WHERE UG.userId =:userid";
			
			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("userid", user_id);
			query.setParameter("monitorType", monitorType);
			
			query.setResultTransformer(new AliasToBeanResultTransformer(JGatewayInfo.class));
			query
			.addScalar("gateway_id", new LongType())
			.addScalar("name", new StringType())
			.addScalar("qr_code", new StringType())
			.addScalar("image_url", new StringType());
			
			gatewayInfoList = query.list();
			
			if( gatewayInfoList.isEmpty() ) {
				log.info("No gateway found");
				return null;
			}
			
			return gatewayInfoList;
		} catch (Exception e) {
			log.error("Error in getJGatewayInfo :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	public boolean updateDevicebasedSubGatewayId(long old_gateway_id, long gatewayId) {
		log.info("Entered into updateDevicebasedSubGatewayId :: old_gateway_id : "+ old_gateway_id+" :: gateway_id : "+ gatewayId);
		try {
			Session ses = sessionFactory.getCurrentSession();
				String qry = "UPDATE all_product_subscription SET gateway_id = :gateway_id WHERE gateway_id = :old_gateway_id";
				SQLQuery query = ses.createSQLQuery(qry);
				query.setParameter("old_gateway_id", old_gateway_id);
				query.setParameter("gateway_id", gatewayId);
				int status = query.executeUpdate();
				if (status > 0) {
					log.info("updated all_product_subscription status : true");
					
					Session ses1 = sessionFactory.getCurrentSession();
					String qry1 = "UPDATE gateway_feature SET gateway_id = :gateway_id WHERE gateway_id = :old_gateway_id";
					SQLQuery query1 = ses1.createSQLQuery(qry1);
					query1.setParameter("old_gateway_id", old_gateway_id);
					query1.setParameter("gateway_id", gatewayId);
					int status1 = query1.executeUpdate();
					if (status1 > 0) {
						log.info("updated gateway_feature status : true");

						Session ses2 = sessionFactory.getCurrentSession();
						String qry2 = "UPDATE flexi_plan_history SET gateway_id = :gateway_id WHERE gateway_id = :old_gateway_id";
						SQLQuery query2 = ses2.createSQLQuery(qry2);
						query2.setParameter("old_gateway_id", old_gateway_id);
						query2.setParameter("gateway_id", gatewayId);
						query2.executeUpdate();

						return true;
					}
					log.info("updated gateway_feature status : false");
				}
				log.info("updated all_product_subscription status : false");
				
				
				
		} catch (Exception e) {
			log.error("Error in updateDevicebasedSubGatewayId :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	public List<JPetprofileFlutter> getPetprofileList(long userId) {

		log.info("Entered into getPetprofileList :: userId : {}", userId);
		List<JPetprofileFlutter> res = new ArrayList<>();

		try {
			Session session = sessionFactory.getCurrentSession();

			String hql = "SELECT p.id, p.name, p.birth_date, p.sex, p.breed, p.height, p.weight, p.imageurl, " +
					"p.speciesid, p.user_id, p.intact, p.structure, p.find_now, p.activitylevel, p.enable, p.gateway_id " +
					"FROM pet_profile p WHERE user_id = :user_id AND gateway_id IS NULL";

			Query query = session.createSQLQuery(hql);
			query.setParameter("user_id", userId);
			List<Object[]> results = query.list();

			for(Object[] row : results) {
				JPetprofileFlutter petProfile = new JPetprofileFlutter();

				if (row[0] != null) petProfile.setId(((BigInteger) row[0]).longValue());
				if (row[1] != null) petProfile.setName((String) row[1]);
				if (row[2] != null) {
					petProfile.setBirth_date(row[2].toString());
					long tempAge = IrisservicesUtil.getAge((Date) row[2], "MONTH");
					Period period = Period.ofMonths((int) tempAge).normalized();
					NumberFormat formatter = new DecimalFormat("00");

					String years = formatter.format(period.getYears());
					String months = formatter.format(period.getMonths());

					petProfile.setAgeMonth(months);
					petProfile.setAgeYr(years);
				}
				if (row[3] != null) petProfile.setSex((String) row[3]);
				if (row[4] != null) petProfile.setBreed((String) row[4]);
				if (row[5] != null) petProfile.setHeight(Float.parseFloat((String) row[5]));
				if (row[6] != null) petProfile.setWeight(Float.parseFloat((String) row[6]));
				if (row[7] != null) petProfile.setImageurl((String) row[7]);
				if (row[8] != null) petProfile.setSpeciesid(((BigInteger) row[8]).longValue());
				if (row[9] != null) petProfile.setUser_id(((BigInteger) row[9]).longValue());
				if (row[10] != null) petProfile.setIntact((Boolean) row[10]);
				if (row[11] != null) petProfile.setStructure((String) row[11]);
				if (row[12] != null) petProfile.setFind_now((Boolean) row[12]);
				if (row[13] != null) {
					long activityLevel = petSpeciesServicesv4.getFindActivitylevelVal(row[13].toString());
					petProfile.setActivitylevel(activityLevel);
				}
				if (row[14] != null) petProfile.setEnable((Boolean) row[14]);
				if (row[15] != null) petProfile.setGatewayId(((BigInteger) row[15]).longValue());

				res.add(petProfile);
			}
		} catch (Exception e) {
			log.error("Error in getPetprofileList :: Error : {}", e.getLocalizedMessage());
		}

		return res;
	}

	@Override
	public boolean saveUserDeviceSpot(long userId, String gatewayId, String devicePlaceType, String devicePlace) {

		log.info("Entered into saveUserDeviceSpot :: userId : {} :: gatewayId : {}", userId,gatewayId);
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "INSERT INTO user_device_spot_status(user_id, gateway_id, device_place_type, device_place) " +
					"values(:userId, :gatewayId, :devicePlaceType, :devicePlace)";
			Query query = ses.createSQLQuery(qry)
								.setParameter("userId", userId)
								.setParameter("gatewayId", gatewayId)
								.setParameter("devicePlaceType", devicePlaceType)
								.setParameter("devicePlace", devicePlace);

			query.executeUpdate();
			log.info("updated user device spot status : false");

			return true;
		} catch (Exception e) {
			log.error("Error in saveUserDeviceSpot :: Error : {}", e.getLocalizedMessage());
		}

		return false;
	}

	@Override
	public List<Object[]> getUserDeviceSpot() {

		log.info("Entered into getUserDeviceSpot");
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "SELECT c.id, c.`device_spot_type`, t.id AS spotId, t.`device_spot` " +
					" FROM `user_device_spot_type` c " +
					" LEFT JOIN `user_device_spot` t ON c.id = t.`device_spot_type_id` ORDER BY spotid";

			Query query = ses.createSQLQuery(qry);

			List<Object[]> result = query.list();

			return result;
		} catch (Exception e) {
			log.error("Error in getUserDeviceSpot :: Error : {}", e.getLocalizedMessage());
		}

		return new ArrayList<>();
	}

	@Override
	public boolean getUserDeviceSpotByUserId(long userId,long gatewayId) {
		log.info("Entered into getUserDeviceSpotByUserId :: user_id : "+ userId);
		try {

			String qry = "SELECT user_id FROM user_device_spot_status "
					+ " WHERE gateway_id = :gateway_id AND user_id= :user_id and device_place_type != 'NA' and device_place != 'NA'; ";
			SQLQuery query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userId);
			query.setParameter("gateway_id", gatewayId);

			List<Object> obj = query.list();
			if(obj.size() > 0) {
				int cnt = ((BigInteger) obj.get(0)).intValue();

				if (cnt > 0)
					return true;
				else
					return false;
			}else{
				return false;
			}

		} catch (Exception e) {
			log.error("Error in getUserDeviceSpotByUserId :: Error : "+ e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public FotaUpdate getFotaUpdate(String gatewayId) {

		log.info("Entered into getFotaUpdate :: gatewayid : {}", gatewayId);
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "SELECT G.id, AM.model_alias_name, " +
					"CASE WHEN LGR.fota_ver = 'NA' THEN NI.curr_fota_version ELSE LGR.fota_ver END AS fota_ver, " +
					"FVM.fota_version, FVM.filename, G.update_passcode, G.is_bt_update, G.meid " +
					"FROM gateway G " +
					"LEFT JOIN lastgatewayreport LGR ON G.id = LGR.gateway_id " +
					"LEFT JOIN niom.inventory NI ON G.meid = NI.meid " +
					"LEFT JOIN fota_version_mapping FVM ON " +
					"(CASE WHEN LGR.fota_ver = 'NA' THEN NI.curr_fota_version ELSE LGR.fota_ver END) = FVM.curr_version " +
					"AND FVM.is_bt_fota = 1 " +
					"JOIN assetmodel AM ON AM.id = G.model_id " +
					"WHERE G.id = :gatewayId " +
					"ORDER BY FVM.id DESC " +
					"LIMIT 1";

			Query query = ses.createSQLQuery(qry)
								.setParameter("gatewayId", gatewayId);

			List<Object[]> result = query.list();
			Object[] obj = result.get(0);

			return populateFotaUpdate(obj);
		} catch (Exception e) {
			log.error("Error in getFotaUpdate :: Error : {}", e.getLocalizedMessage());
		}

		return new FotaUpdate();
	}

	private FotaUpdate populateFotaUpdate(Object[] obj) {

		FotaUpdate fotaUpdate = new FotaUpdate();

		fotaUpdate.setGatewayId(((BigInteger) obj[0]).longValue());
		fotaUpdate.setDeviceModel((String) obj[1]);
		fotaUpdate.setCurrentFotaVersion((String) obj[2]);
		if(obj[3] != null) fotaUpdate.setUpgradeFotaVersion((String) obj[3]);
		if(obj[4] != null) fotaUpdate.setFileName((String) obj[4]);
		fotaUpdate.setPasscode((String) obj[5]);
		fotaUpdate.setBleUpdateStatus((boolean) obj[6]);
		fotaUpdate.setMeid((String) obj[7]);

		return fotaUpdate;
	}


	@Override
	public boolean updateLatestFotaVersion(String gatewayId, String updatedFotaVersionNumber) {
		log.info("Entered into updateLastestFotaVersion :: gatewayid : {}", gatewayId);
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry1 = "UPDATE lastgatewayreport  SET fota_ver= :updatedFotaVersionNumber WHERE gateway_id= :gatewayId";

			Query query1 = ses.createSQLQuery(qry1)
					.setParameter("gatewayId", gatewayId)
					.setParameter("updatedFotaVersionNumber",updatedFotaVersionNumber);

			query1.executeUpdate();

			String qry2="UPDATE gateway  SET is_bt_update=0 WHERE id= :gatewayId";

			Query query2 = ses.createSQLQuery(qry2)
					.setParameter("gatewayId", gatewayId);

			query2.executeUpdate();

			return true;

		} catch (Exception e) {
			log.error("Error in updateLastestFotaVersion :: Error : {}", e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean saveMinicamshippingaddres(Minicamshipping jminicamshipping) {
		log.info("Entered into saveMinicamshippingaddres :: userId : {} ", jminicamshipping.getUser_id());
		try {
			Session ses = sessionFactory.getCurrentSession();
			LocalDateTime now = LocalDateTime.now();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			long gatewayId=jminicamshipping.getGatewayId();
			long monitortypeId= jminicamshipping.getMonitortypeId();
			String formattedDateTime = now.format(formatter);
			String qry = "INSERT INTO minicam_shipping_addr(user_id, gateway_id, monitortype_id, address1, address2, zipcode, city, state, created_date) " +
					"values(:userId, :gateway_id, :monitortype_id, :address1, :address2, :zipcode, :city, :state, :created_date )";
			Query query = ses.createSQLQuery(qry)
					.setParameter("userId", jminicamshipping.getUser_id())
					.setParameter("gateway_id",jminicamshipping.getGatewayId())
					.setParameter("monitortype_id",jminicamshipping.getMonitortypeId())
					.setParameter("address1", jminicamshipping.getAddress1())
					.setParameter("address2", jminicamshipping.getAddress2())
					.setParameter("zipcode", jminicamshipping.getZipcode())
					.setParameter("city", jminicamshipping.getCity())
					.setParameter("state", jminicamshipping.getState())
					.setParameter("created_date", Timestamp.valueOf(formattedDateTime));

			query.executeUpdate();
			log.info("updated mini cam address status : false");

			return true;
		} catch (Exception e) {
			log.error("Error in saveMinicamshippingaddres :: Error : {}", e.getLocalizedMessage());
		}

		return false;
	}
    @Override
    public boolean checkN12fotaupdateAvailable(String fotaUpdateVersion, Long gatewayId) {
		log.info("Entered into checkN12fotaupdateAvailable :: gatewayId : {} ", gatewayId);
		Boolean result = null;
		try {
			Session ses = sessionFactory.getCurrentSession();
			String qry = "SELECT IF(fota_ver <> :fotaUpdateVersion && battery >= 30,TRUE,FALSE ) AS 'Result' FROM lastgatewayreport WHERE gateway_id= :gatewayId";
			Query query = ses.createSQLQuery(qry);
			query.setParameter("fotaUpdateVersion", fotaUpdateVersion);
			query.setParameter("gatewayId", gatewayId);

			List<Object> obj = query.list();

			if (!obj.isEmpty()) {
				Object first = obj.get(0);
				if (first instanceof Boolean) {
					result = (Boolean) first;
				} else if (first instanceof Number) {
					// MySQL might return 0 or 1 as numeric result for BOOLEAN
					result = ((Number) first).intValue() == 1;
				}
			}
		}
		catch (Exception e) {
			log.error("Error in checkN12fotaupdateAvailable :: Error : {}", e.getLocalizedMessage());
		}
		return result ;

    }






	@Override
	public  List<JGatewayInfo> getJGatewayInfoV1(long userId, long monitorTypeId, long gateWayId) {
		log.info("Entered into getJGatewayInfo :: user_id : "+ userId);
		List<JGatewayInfo> gatewayInfoList = new ArrayList<>();
		try {

			String qry = "SELECT G.id AS gateway_id,G.name,G.qrcode AS qr_code,M.imageurl AS image_url,monitor_type_id AS monitorTypeId "
					+ " FROM gateway G "
					+ " JOIN usergateway UG ON UG.gatewayId = G.id "
					+ " JOIN assetmodel AM ON AM.id = G.model_id   "
					+ " LEFT JOIN monitortype M ON M.id = AM.monitor_type_id  "
					+ " WHERE UG.userId =:userid " +
					"AND (:monitortypeid = 0 OR AM.monitor_type_id =:monitortypeid) " +
					"AND (:gatewayid = 0 OR UG.gatewayId = :gatewayid) AND AM.monitor_type_id IN (1, 5, 6, 8, 11, 12)";

			SQLQuery query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("userid", userId);
			query.setParameter("monitortypeid", monitorTypeId);
			query.setParameter("gatewayid", gateWayId);
			//query.setParameter("validmonitorids", validPlanMonitorIds);


			query.setResultTransformer(new AliasToBeanResultTransformer(JGatewayInfo.class));
			query
					.addScalar("gateway_id", new LongType())
					.addScalar("name", new StringType())
					.addScalar("qr_code", new StringType())
					.addScalar("image_url", new StringType())
					.addScalar("monitorTypeId", new LongType());

			gatewayInfoList = query.list();

			if( gatewayInfoList.isEmpty() ) {
				log.info("No gateway found");
				return null;
			}

			return gatewayInfoList;
		} catch (Exception e) {
			log.error("Error in getJGatewayInfo :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}


	public ArrayList<JProductWithSubCategory> getProductList() {
		log.info("Entered into getProductList");
		ArrayList<JProductWithSubCategory> product_list = new ArrayList<>();
		try {

			String qry = "SELECT " +
					"    PS.mtype_id, " +
					"    PS.display_name AS ps_display_name, " +
					"    PS.psimg_url AS ps_img_url," +
					"    P.product_name AS product_display_name, " +
					"    P.pimg_url AS product_img_url " +
					"FROM " +
					"    product_sub_cat PS " +
					"JOIN " +
					"    product P ON P.id = PS.product_id " +
					"WHERE " +
					"  PS.mtype_id NOT IN (3, 4, 9, 10, 11);";
			List<Object[]> productListObject = sessionFactory.getCurrentSession().createSQLQuery(qry).list();

			Map<Integer, List<Object[]>> products = productListObject.stream()
					.collect(Collectors.groupingBy(record -> ((BigInteger) record[0]).intValue()));

			List<JProductWithSubCategory> finalResults = new ArrayList<>();

			for (Map.Entry<Integer, List<Object[]>> entry : products.entrySet()) {
				Integer mtypeId = entry.getKey();
				List<Object[]> groupProducts = entry.getValue();

				if(groupProducts.size() > 1) {

					Object[] first = groupProducts.get(0);

					product_list.add(new JProductWithSubCategory(mtypeId, (String) first[3], (String) first[4]));
				} else {

					Object[] only = groupProducts.get(0);
					product_list.add(new JProductWithSubCategory(mtypeId, (String) only[1], (String) only[2]));
				}

			}

		} catch (Exception e) {
			log.error("Error in getProductCategory :: Error : "+ e.getLocalizedMessage());
		}
		return product_list;
	}
}
