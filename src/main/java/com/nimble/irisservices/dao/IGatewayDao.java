package com.nimble.irisservices.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.InvalidPacket;
import com.nimble.irisservices.dto.JAlertRangeType;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JGatewaySensorType;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JMeariNotification;
import com.nimble.irisservices.dto.JPacketReport;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FurbitLastGatewayReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayCredits;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.exception.InvalidSubgroupIdException;

public interface IGatewayDao {

	List<JGateway> getJGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid, long cmpid,
			String meid);

	public List<JGatewayConfig> getJGatewayConfig(String gatewayid, long userid, String tempunit);

	public List<Gateway> getGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid);

	JGatewayOverview getGatewayCount(String groupid, String subgroupid, long userid, String levelid);

	Gateway saveORupdateGateway(JGateway gateway, long cmpid) throws ConstraintViolationException,
			InvalidAssetGroupIdException, InvalidAssetGroupIdException, InvalidModelIdException, InvalidAsseIdException,
			InvalidGroupIdException, DataIntegrityViolationException, InvalidSubgroupIdException;

	// Save Or update Gateway to register QRC user
	Gateway saveORupdateQRCGateway(JGateway gateway, long cmpid) throws ConstraintViolationException,
			InvalidAssetGroupIdException, InvalidAssetGroupIdException, InvalidModelIdException, InvalidAsseIdException,
			InvalidGroupIdException, DataIntegrityViolationException, InvalidSubgroupIdException;

	boolean updateGatewayName(String gatewayName, String gatewayId);

	public Gateway convetJGatewaytoGateway(JGateway jg, long cmpid) throws InvalidAssetGroupIdException,
			InvalidSubgroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException;

	GatewayCredits updateGatewayCredit(long gateway_id, long company_id);

	boolean updateGatewayCreditPoints(ThrottlingSettings throtSettings);

	public AssetModel getAssetModel(long id);

	public void updateAsset(Asset asset) throws InvalidAsseIdException;

	public void saveAsset(Asset asset) throws InvalidAsseIdException;

	public Asset getAssetById(long id);

	public Asset getAssetByAddr(String addr);

	List<Asset> getAssets(Long[] assetids) throws InvalidAsseIdException;

	List<Gateway> getGatewayByIds(Set<String> gatIds);

	JSreiGatewayOverview getSreiGatewayCount(String groupid, String subgroupid, long userid, int rptStatus,
			String levelid, int IsInbuilt);

	public List<Gateway> getGatewayCmpBased(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long cmpid);

	Gateway saveORupdateGateway(Gateway gateway);

	JGateway gatewayExitsinDB(JGateway jgateway, long cmpid);

	boolean delGateway(User user, String assetid);

	Gateway getGateway(long id);

	List<JSGateway> getJSGateways(String cmptype_id);
	/* List<JSGateway> getJSGateways(); */

	int saveORupdatePetprofile(List<JPetprofile> jpetprofiles, long userid);

	List<JPetprofile> getJPetprofiles(long userid, long id, long gatewayid, String name, String age, String sex,
			String breed);

	JGatewayUserDetails getGateway(String meid);

	boolean updatepetprofile(String gatewayId);

	public ProbeCategory getProbeCategory(long model_id);

	public boolean gatewayOnOff(String gatewayId, boolean isEnable, String message);

	public boolean enableOrDisableGateway(String gatewayId, String userId, boolean isEnable);

	public int updateGoalSetting(String gatewayId, String goal);

	public int updateCaloriesGoalSetting(String gatewayId, String calories_goal);

	public JResponse getJPetprofilesByUser(long userid, long gatewayid, int mnitortype);

	public List<Gateway> getGatewayByMonitorType(String monitortype, long userid);

	public PetProfile getPetProfile(long gateway_id);

	public JGatewayUserDetails getGatewayAndUserDetails(String meid);

	public List<FurbitLastGatewayReport> getFLastGatewayReportByUser(long userid);

	public ArrayList<JGatewayConfig> getGatewayConfig(long userid);

	public boolean updatePetProfile(PetProfile petProfile);

	public AssetModel getAssetModelByName(String inventorymodelname);

	public Gateway getGatewayInMeid(String meid);

	public boolean checkQrcExist(long id, String qrcode);

	public boolean checkQrcRegistered(String qrcode);

	public int updateGateway(Gateway gateway);

	public Gateway getGatewayByid(long id);

	public JGateway getJGateway(String columnname, String value);

	public List<JGateway> getJGatewayByUser(long userid, String monitor_type);

	public Date getBirthDate(String calendarValue, String age);

	public List<JGatewayForPet> getNotMappedGateway(long userid);

	public long getOrderchannelid(String orderchannel);

	// public int saveOrderchannelDetail(long user_id,long gateway_id,long
	// orderchannel);

	public JGatewayUserDetails getGatewayByMAC(String macid, long user_id);

	public boolean updateProfileImgPath(long ppid, String imgpath);

	public int getDeviceCount(long userid);

	public boolean enableOrDisablePetProfile(long userId, List<EnableOrDisablePetProfile> enableOrDisablePetPrfList);

	public boolean updateGatewayName(String gatewayid);

	public boolean changeGatewayOrderidStatus(long id, boolean pFromStatus);

	public Gateway saveORupdateRecallGateway(JGateway jgateway, long id) throws InvalidAssetGroupIdException,
			InvalidSubgroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException;

	public boolean updateAssetMeid(long id, String meid);

	public boolean saveRecallGateway(Gateway gateway);

	public boolean changeDefalutRptInLastGateway(long gatewayId, String currentTimeUTC, int defaultRpt);

	public Gateway saveORupdateReturnGateway(JGateway jgateway, long id) throws ConstraintViolationException,
			InvalidAssetGroupIdException, InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException,
			DataIntegrityViolationException, InvalidSubgroupIdException;

	public Gateway saveReturnGateway(Gateway gateway);

	public List<String> getMeidsList(Gateway exGatewayInDB);

	public List<String> getOldAssertList(Asset asset);

	public boolean changeOldAssertDetailsByAddr(String meid, String changedMeid);

	public AssetModel getAssetModelByMeid(String imei);

	List<JGateway> checkDeviceExist(String meid);

	JQrcDetails getQrcDetails(String qrc);

	boolean assignGatewayToLogin(String gid, long user_id);

	long getGatewayId(String meid);

	public boolean isUserRegistered(String qrc);
		
	JQrcDetails  getOldModelNameandModelId(String deviceModelNo);
	
	public  long getOtpByEmail(String email);

	public boolean createOrResetGatewayStatus(long gateway_id, String meid, String serial_number,long mtyp,int isRecall);

	public List<String> getDistinctProductCategory();

	public JGatewayToAlexa getJGatewayToAlexa(String user_id);

	public GatewayStatus getGatewayStatusByMeid(String meid);

	public PetProfile getPetprofile(long id, long gatewayid, String name, String age, String sex, String breed);

	public boolean updateGatewayNameByPetId(String name, long profileId);

	public double getPetFoodCaloriesByProfileId(long profileId);

	List<JAlertRangeType> getJAlertRangeType(String userid);
	
	public boolean delGatewayStatus(long gateway_id);
	
	public boolean delGatewayProfile(long gateway_id);

	public boolean delGateway(long gateway_id);

	public List<JMeariNotification> getMeariNotification();
	
	public boolean delAsset(String meid);

	JResponse getInvalidPacket(String otype, long offset, long limit, String from_date, String to_date,String type,
			JResponse response);

	ArrayList<InvalidPacket> getInvalidPacketExport(String from_date, String to_date, String type);

	Map getInvalidFieldMeidCount(String from_date, String to_date);

	boolean getSkipOtaStatus(String meid);

	boolean updateskipotastatus(String Meid, boolean status);

	AssetModel getAssetModelByGatewayId(long gateway_id);

	boolean updateuseremaildetails(String from_email, String to_email);

	JGatewaySensorType getSensorDetailByGatewayId(long gateway_id);
	
	public void updateRecallAsset(Asset asset) throws InvalidAsseIdException;

	public boolean updateGatewayFotoversion(String verVal, String gatewayId);

	double[] getDeviceLatLon(long gateway_id);

	boolean createSolarcamSubscription(long gateway_id, long userid, long mType);
	JGateway getJGatewayByGateway(long gatewaid);

	Gateway getActiveGatewayByid(long id);

	long getUserIdByGatewayId(long gateway_id);

	PlanToPeriod getPlanAndPeriodId(String planName);

	List<JPacketReport> getPacketReport(String meid, String startTime, int seconds);
	boolean delGatewayAllProdSub(long gatewayId);

	List<JGateway> getJGatewayV1(String assetgroupid, String groupid, String subgroupid, String gatewayid, long userid, String meid, String chargebeeId);

	boolean enableOrDisableBtUpdate(String gatewayId, int bleEnable);

	boolean updatePasscode(String gatewayId);

	boolean enableOrDisableFotaVersionMapping(String fotaVersionId, int enable, String currVersion);

	boolean minicamaddresavilable(long userId);

	String getUsernameByQrc(String qrcode);

	boolean updateGatewaySkip(boolean isSkip, long gatewayId);

	boolean saveWarrantyClaimType(String claimType, long gatewayId);

	boolean checkFotaVersionAvailable(long gatewayId, String meid);

	boolean checkFreeplanavilinmearidevice(long gatewayId, long userId);

	boolean enableOrDisableMeariUpdate(String gatewayId, int btnEnable);

    void updatePurchaseMonthYear(long userId, long gatewayId, String purchasedMonthYear);

	String getNewQrc(String oldQrc);

}
