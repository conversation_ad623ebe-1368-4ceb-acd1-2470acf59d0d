package com.nimble.irisservices.dao;

import java.util.List;

import com.nimble.irisservices.dto.ComboPlanInfo;
import com.nimble.irisservices.dto.JCancelSubDetail;
import com.nimble.irisservices.dto.JPlan;
import com.nimble.irisservices.entity.*;

public interface IChargebeeDao {

	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeid);

	public JPlan getPlanDesc(String planId);

	public Credits getChargebeeUserCredits(String chargebeeId);

	public ProductSubscription getProductSubscription(long user_id);

	public List<UnpaidInvoices> getUnpaidInvoice(String type);

	public boolean saveCurrentPlanDetails(CbCurrentPlanDetails cbCurrentPlanDetails);

	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String order_date, String order_id, int is_bundle);

	public boolean saveCancelSubscription(JCancelSubDetail csDetail,long userid, String cancel_type, String cancelled_at, int refund_amount, double exchange_rate, String curr_code);

	public AllProductSubscription getProductSubscriptionByGatewayId(long gateway_id, String chargebee_id, long monitor_type);

	public boolean checkDueInvoice(String cbSubId);	
	public String getMeariSubCode(long userid,long gatewayid,long period_id, boolean isSolar);
	
	public boolean updateMeariSubStatus(long userid, long gatewayid, long period_id,String type,String keyname);
	
	public boolean saveTemp_sub_purchase( long userid,long gatewayid, long monitortype_id, String  hp_id);
	
	public ProductSubscription getProductSubscriptionByOrderId(long order_id);

	public ProductSubscription saveOrUpdateProductSubscription(ProductSubscription productSubscription);
	
	public boolean saveReturnCancelSubscription(String cbsubid, String reasonType, String reasonDesc, long userid,
			String cancel_type, String cancelled_at, int refund_amount, double exchange_rate, String curr_code);

	public AllSubscription getSubscriptionByGatewayId(String subscrp_id);

	public AllProductSubscription getProductSubscriptionBySubId(String subscrp_id);

	public boolean saveCancelSubHistory(CancelsubHistory canSub);

	public String getSubscriptionByChargebeeIdNotinProductsub(String chargebeeid);

	public String getProductSubscriptionStatus(long gateway_id, String chargebee_id);

	public String getPlanVersionbyplanname(String planid);

	public String getProductSubscriptionByChargebee(String chargebee_id);

	public AllProductSubscription getProductSubscriptionByGateway(long id, String chargebeeid);
	
	public boolean updategatewaybysubcriptionId(long gatewayId, String subId);

	public boolean isVetPlanAvailable(String chargebeeid,long monitortype);
	
	public String getVetPlanSub(String chargebeeid,long monitortype);
	
	public long getCurrentVetPlanid(String chargebeeid,long monitortype);

    List<AllProductSubscription> getproductSubscriptions(String chargebeeid, long gateway_id);

    public ComboPlanInfo checkActiveComboPlan(String chargebeeId);

	String getUnmappedPlaninprodSubscription(String chargebeeid, long monitorType);

	long getGatewayFeatureBySubId(String subId);

	int getCancelSub(long id, String chargebeeid);

	List<ManageList> getManageList();
}
