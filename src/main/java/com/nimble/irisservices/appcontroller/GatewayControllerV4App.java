package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.Subscription;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.service.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.impl.GatewayDaoImplV4;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.impl.VetChatServiceImpl;


@Controller
@RequestMapping("/app")
public class GatewayControllerV4App {

	private static final Logger log = LogManager.getLogger(GatewayControllerV4App.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	IDynamicCmdService dynamiccmdService;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	GatewayDaoImplV4 GatewayDaoimplv4;

	@Autowired
	@Lazy
	INiomDataBaseService niomServiceV4;
	
	@Autowired
	IDynamicCmdService dynamicCmdService;

	Helper _helper = new Helper();
	
	@Autowired
	@Lazy
	ICommonService commonService;
	
	@Autowired
	private IAsyncService async;
	
	@Autowired
	@Lazy
	IChargebeeService chargebeeService;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IMessagingService messagingService;
	
	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${remove_gateway_upgrade_msg}")
	private String remove_gateway_upgrade_msg;

	@Value("${remove_gateway_title_msg}")
	private String remove_gateway_title_msg;

	@Value("${freeplanactivation_contentmc}")
	private String freeplanactivation_contentmc;

	@Value("${trialplanactivation_contentmc}")
	private String trialplanactivation_contentmc;

	@Value("${paidplanactivation_contentmc}")
	private String paidplanactivation_contentmc;

	@Value("${remove_gateway_common_msg}")
	private String remove_gateway_common_msg;

	@Value("${remove_gateway_confirmation_title}")
	private String remove_gateway_confirmation_title;

	@Value("${remove_gateway_confirmation_body_without_subscription}")
	private String remove_gateway_confirmation_body_without_subscription;

	@Value("${remove_gateway_confirmation_body_with_subscription}")
	private String remove_gateway_confirmation_body_with_subscription;

	@Value("${show_remove_gateway_greater_than_device_cnt}")
	private int show_remove_gateway_greater_than_device_cnt;

	@Value("${remove_gateway_valid_hours_to_show}")
	private int remove_gateway_valid_hours_to_show;
	
	@Autowired
	IRegisterDeviceService registerDeviceService;
	
	@Value("${wc_maximum_device_play_count}")
	private int wc_maximum_device_play_count;

	@Value("${wc_maximum_sub_user_count}")
	private int wc_maximum_sub_user_count;

	@Value("${show_lan_permission}")
	private boolean show_lan_permission;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;
	
	@Autowired
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	IAlertCfgService alertcfgService;
	@Autowired
	IAsyncService asyncService;

	@Autowired
	private IMeariNotificationService meariNotificationService;
	
	@Value("${web_rtc_url}")
	private String web_rtc_url;

	@Value("${remove_gateway_without_subscription_content}")
	private String remove_gateway_without_subscription_content;

	@Value("${freeplan}")
	private String freeplan;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	private VetChatServiceImpl vetChatServiceImpl;

	@Value("${remove_gateway_confirmation_note}")
	private String remove_gateway_confirmation_note;

	@Value("${fota_update_file_url}")
	private String fotaUpdateFileUrl;

	@Value("${n12_5_fotacommand}")
	private String fotacommand ;

	@Value("${isN12_5_fotacommandUpdateNeeded}")
	private boolean isN12_5_fotacommandUpdateNeeded ;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@PostMapping(value = "v5.0/updatecamconfig", headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateCamConfig(
			@RequestBody JCamConfig camConfig,
			@RequestParam("os") String os,
			@RequestParam(value = "type", defaultValue = "iphone", required = false) String type,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entered into updateCamConfig :: gateway_id : "+ camConfig.getGateway_id()+ " :: cam_config : "+ camConfig.toString());
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			boolean updated_status = false;
			
			GatewayStatus gatewayStatus = gatewayServiceV4.getGatewayStatus( camConfig.getGateway_id() );
			
			List<AlertCfgWC> alertCfgList = null;
			
			if( gatewayStatus == null ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				return response;
			}
			
			switch ( camConfig.getUpdated_for() ) {
			case "notification": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setNotification( value.equals("1") ? true : false );
				break;
			}

			case "livetracking_rotation": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setLivetracking_rotation( value.equals("1") ? true : false );
				break;
			}

			case "screen_flip" : {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setScreen_flip( value.equals("1") ? true : false );
				break;
			}
			case "night_vision_mode": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setNight_vision_mode( Integer.parseInt(value) );
				break;
			}
			case "noise_detection": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setNoise_detection( value.equals("1") ? true : false );
				break;
			}
			case "detection_sensitivity": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setDetection_sensitivity( value );
				break;
			}
			case "continuous_playback": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setContinuous_playback( value.equals("1") ? true : false );
				break;
			}
			case "noise_sensitivity": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setNoise_sensitivity( value );
				break;
			}
			case "recording_time": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setRecording_time( Integer.parseInt(value) );
				break;
			}
			case "alarm_interval": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAlarm_interval( Integer.parseInt(value) );
				break;
			}
			case "event_recording": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setEvent_playback( value.equals("1") ? true : false );
				break;
			}
			case "pet_detection": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setPet_detection( value.equals("1") ? true : false );
				break;
			}
			case "is_vehicle_detection": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setVehicle_detection( value.equals("1") ? true : false );
				break;
			}
			case "device_unique_id": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setDevice_unique_id( value );
				break;
			}
			case "motion_detection": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "motion" );
				String value = camConfig.getUpdated_value();
				gatewayStatus.setMotion_detection( value.equals("1") ? true : false );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION);
				break;
			}
			case "motion_detection_pet": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "motion" );
				String value = camConfig.getUpdated_value();
				gatewayStatus.setMotion_detection( value.equals("1") ? true : false );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION_DOG +","+IrisservicesConstants.ALERT_MOTION_DETECTION_CAT);
				break;
			}
			case "motion_detection_persion": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "motion" );
				String value = camConfig.getUpdated_value();
				gatewayStatus.setMotion_detection_person( value.equals("1") ? true : false );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION_HUMAN);
				break;
			}			
			case "motion_detection_dog": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "motion" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION_DOG);
				break;
			}
			case "motion_detection_cat": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "motion" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION_CAT);
				break;
			}
			case "sound_alert": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "sound" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_SOUND);
				break;
			}
			case "sound_alert_pet": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "sound" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_SOUND+","+IrisservicesConstants.ALERT_SOUND_DOG+","+IrisservicesConstants.ALERT_SOUND_CAT);
				break;
			}
			case "sound_alert_dog": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "sound" );
				String value = camConfig.getUpdated_value();
				gatewayStatus.setBarking_alert( value.equals("1") ? true : false );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_SOUND_DOG);
				break;
			}
			case "sound_alert_cat": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "sound" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_SOUND_CAT);
				break;
			}
			case "temperature": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "temp" );
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_TEMPERATURE);
				break;
			}
			case "temperature_range": {
				updategatewayPendingEvent( gatewayStatus.getGateway_id(), "temp" );
				String[] value = camConfig.getUpdated_value().split(",");
				if( !gatewayStatus.isTemp_unit_celsius() ) {
					value[0] = String.valueOf(  alertCfgServiceV4.FahrenheitToCelsius( Float.parseFloat( value[0] ) ));
					value[1] = String.valueOf(  alertCfgServiceV4.FahrenheitToCelsius( Float.parseFloat( value[1] ) ));
				}
				updated_status = alertCfgServiceV4.updateAlertCfgRange( value[0], value[1], camConfig.getGateway_id(), IrisservicesConstants.ALERT_TEMPERATURE);
				break;
			}
			case "humidity": {
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_HUMIDITY);
				break;
			}
			case "humidity_range": {
				String[] value = camConfig.getUpdated_value().split(",");
				updated_status = alertCfgServiceV4.updateAlertCfgRange( value[0], value[1], camConfig.getGateway_id(), IrisservicesConstants.ALERT_HUMIDITY );
				break;
			}
			case "battery": {
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_BATTERY);
				break;
			}
			case "battery_range": {
				String[] value = camConfig.getUpdated_value().split(",");
				updated_status = alertCfgServiceV4.updateAlertCfgRange( value[0], IrisservicesConstants.POWER_MAX, camConfig.getGateway_id(), IrisservicesConstants.ALERT_BATTERY);
				break;
			}
			case "on_battery": {
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_ON_BATTERY);
				break;
			}
			case "power_recovery": {
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_POWER_RECOVERY);
				break;
			}
			case "dnr": {
				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_DNR);
				break;
			}
			case "sms": {
				String value = camConfig.getUpdated_value();
				updated_status = alertCfgServiceV4.updateAlertCfgWCEmailOrSmsOrPushNotification( camConfig.getGateway_id(), value, IrisservicesConstants.SMS );
				break;
			}
			case "email": {
				String value = camConfig.getUpdated_value();
				updated_status = alertCfgServiceV4.updateAlertCfgWCEmailOrSmsOrPushNotification( camConfig.getGateway_id(), value, IrisservicesConstants.EMAIL );
				break;
			}
			case "throwing_beep": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setThrowing_beep( value.equals("1") ? true : false );
				break;
			}
			case "gateway_name": {
				String value = camConfig.getUpdated_value();
				long gateway_id = camConfig.getGateway_id();
				boolean status = gatewayService.updateGatewayName(value, gateway_id + "");
				
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to update device name");
					return response;
				}
			}
			case "update_email_id": {
				String email_ids = ""; 
				if( !camConfig.getAlert_email_id().isEmpty() ) {
					for( String email : camConfig.getAlert_email_id() )
						email_ids = email_ids + email +",";
					email_ids =  email_ids.substring( 0, email_ids.length()-1 );
				}
				updated_status = alertCfgServiceV4.updateAlertCfgWCEmailOrSmsOrPushNotification( camConfig.getGateway_id(), email_ids, IrisservicesConstants.EMAIL_IDS );
				break;
			}
			case "update_mobile_no": {
				String mobile_nos = "";
				if( !camConfig.getAlert_mobile_no().isEmpty() ) {
					for( JMobile mobile : camConfig.getAlert_mobile_no() ) {
						mobile_nos = mobile_nos + mobile.getCountry()+"-"+mobile.getMobile_no()+",";
					}
					mobile_nos = mobile_nos.substring( 0, mobile_nos.length()-1 );
				}
				updated_status = alertCfgServiceV4.updateAlertCfgWCEmailOrSmsOrPushNotification( camConfig.getGateway_id(), mobile_nos, IrisservicesConstants.MOBILE_NOS );
				break;
			}
			case "is_celsius": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setTemp_unit_celsius( value.equals("1") ? true : false );
//				alertCfgList = alertCfgServiceV4.getAlertCfgWC(camConfig.getGateway_id(), IrisservicesConstants.ALERT_TEMPERATURE);
//				
//				if( alertCfgList != null ) {
//					
//					AlertCfgWC alertCfgWC = alertCfgList.get(0);
//					if( gatewayStatus.isTemp_unit_celsius() ) {
//						alertCfgWC.setMin( alertCfgServiceV4.FahrenheitToCelsius( alertCfgWC.getMin() ) );
//						alertCfgWC.setMax( alertCfgServiceV4.FahrenheitToCelsius( alertCfgWC.getMax() ) );
//					} else {
//						alertCfgWC.setMin( alertCfgServiceV4.CelsiusToFahrenheit( alertCfgWC.getMin() ) );
//						alertCfgWC.setMax( alertCfgServiceV4.CelsiusToFahrenheit( alertCfgWC.getMax() ) );
//					}
//					alertCfgWC = alertCfgServiceV4.saveOrUpdateAlertCfgWC(alertCfgWC);
//					if( alertCfgWC == null) 
//						log.info("temp alert min max C to F save failed");
//					else 
//						log.info("temp alert min max C to F save success");
//				}
				alertCfgList = null;
				break;
			}
			case "auto_night_vision": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAuto_night_vision( value.equals("1") ? true : false );
				break;
			}
			case "stream_quality": {
				gatewayStatus.setQuality( camConfig.getUpdated_value() );
				break;
			}
			case "live_tracking_person": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setLivetracking_person( value.equals("1") ? true : false );
				gatewayStatus.setLivetracking_status(true);
				gatewayStatus.setLivetracking_rotation(true);
				gatewayStatus.setLivetracking_boundingbox(true);
				break;
			}
			case "live_tracking_status": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setLivetracking_person( false );
				gatewayStatus.setLivetracking_status( value.equals("1") ? true : false );
				break;
			}
			case "time_zone": { 
				userServiceV4.updateUserTimezone(user.getId(), camConfig.getUpdated_value());
				updated_status = true;
				break;
			}
			case "temp_frequency_value": {
				updated_status = alertCfgServiceV4.updateNotifyFrequency( Long.parseLong( camConfig.getUpdated_value() ), camConfig.getGateway_id(), IrisservicesConstants.ALERT_TEMPERATURE );
				break;
			}
			case "power_loss_frequency_value": {
				updated_status = alertCfgServiceV4.updateNotifyFrequency( Long.parseLong( camConfig.getUpdated_value() ), camConfig.getGateway_id(), IrisservicesConstants.ALERT_POWER_RECOVERY+","+ IrisservicesConstants.ALERT_BATTERY +"," + IrisservicesConstants.ALERT_ON_BATTERY);
				break;
			}
			case "motion_detection_frequency_value": {
				updated_status = alertCfgServiceV4.updateNotifyFrequency( Long.parseLong( camConfig.getUpdated_value() ), camConfig.getGateway_id(), IrisservicesConstants.ALERT_MOTION_DETECTION+","+ IrisservicesConstants.ALERT_MOTION_DETECTION_CAT +"," + IrisservicesConstants.ALERT_MOTION_DETECTION_DOG+","+IrisservicesConstants.ALERT_MOTION_DETECTION_HUMAN);
				break;
			}
			case "sound_alert_frequency_value": {
				updated_status = alertCfgServiceV4.updateNotifyFrequency( Long.parseLong( camConfig.getUpdated_value() ), camConfig.getGateway_id(), IrisservicesConstants.ALERT_SOUND+","+ IrisservicesConstants.ALERT_SOUND_CAT +"," + IrisservicesConstants.ALERT_SOUND_DOG);
				break;
			}
			case "aitreat_onoff": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAitreat_onoff(value.equals("1") ? true : false);
				break;
			}
			case "treat_count": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setTreat_count(Integer.valueOf(value));
				break;
			}
			case "aitreat_interval": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAitreat_interval(Integer.valueOf(value));
				break;
			}
			case "aitreat_maxcount": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAitreat_maxcount(Integer.valueOf(value));
				break;
			}
			case "schedule_enable": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setSchedule_enable(value.equals("1") ? true : false);
				break;
			}
			case "auto_tracking": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setAuto_tracking(value.equals("1") ? true : false);
				if (value.equals("1")) {
					gatewayStatus.setLivetracking_rotation(true);
				}
				break;
			}
			case "updatefotoversion": {
				String value = camConfig.getUpdated_value();
				long gateway_id = camConfig.getGateway_id();
				boolean status = gatewayService.updateGatewayFotoversion(value, gateway_id + "");
				
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to update foto version");
					return response;
				}
			}
			case "operation_mode": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setOperation_mode(Integer.valueOf(value));
				break;
			}
			case "light_setting": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setLight_time_id(Integer.valueOf(value));
				break;
			}
			case "is_configured": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setIs_activated(value.equals("1") ? true : false);
				if(gatewayStatus.isIs_activated()) {
					Gateway gid = gatewayService.getGatewayByid(gatewayStatus.getGateway_id());
					boolean isFreeplan = gatewayService.checkFreeplanavilinmearidevice(gatewayStatus.getGateway_id(),user.getId());
					if(!isFreeplan)
						gatewayService
						.createSolarcamSubscription(gatewayStatus.getGateway_id(), user.getId(), gid.getModel().getMonitor_type().getId());
				}
				break;
			}
			case "videoencoding_format": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setVideoencoding_format(value);
				break;
			}
			case "flicker_level": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setFlicker_level(Integer.valueOf(value));
				break;
			}
			case "smart_light_detection": {
				String value = camConfig.getUpdated_value();
				gatewayStatus.setSmart_light_detection(value.equals("1") ? true : false);
				break;
			}
			case "is_device_reset": {
				String value = camConfig.getUpdated_value();
				if(value != null && value.equalsIgnoreCase("1")){
					gatewayStatus.setLast_device_reset( _helper.getCurrentTimeinUTC());
				}
				break;
			}
				case "human_detection_day": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setHuman_detection_day(value.equals("1")?true:false);
					break;
				}

				case "human_detection_night": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setHuman_detection_night(value.equals("1")?true:false);
					break;
				}

				case "is_microphone_enabled": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setIs_microphone_enabled(value.equals("1")?true:false);
					if(!gatewayStatus.getIs_microphone_enabled()){
						gatewayStatus.setIs_recordvideo_enabled(false);
					}
					break;
				}

				case "is_speaker_enabled": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setIs_speaker_enabled(value.equals("1")?true:false);
					break;
				}

				case "is_recordvideo_enabled": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setIs_recordvideo_enabled(value.equals("1")?true:false);
					break;
				}

				case "speaker_volume": {
					String value = camConfig.getUpdated_value();
					gatewayStatus.setSpeaker_volume( Integer.parseInt(value) );
					break;
				}
			}
			
			if( updated_status ) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				return response;
			}
			
			gatewayStatus = gatewayServiceV4.saveOrUpdateGatewayStatus( gatewayStatus );
			
			if( alertCfgList != null ) {
				boolean enable = camConfig.getUpdated_value().equals("1") ? true : false;
				
				if( alertCfgList.size() > 1 ) {
					
					for( AlertCfgWC alertCfg : alertCfgList ) {
						alertCfg.setEnable(enable);
						AlertCfgWC alertCfgNew = alertCfgServiceV4.saveOrUpdateAlertCfgWC( alertCfg );	
						if( alertCfgNew != null )
							log.info("Alert CFG WC successfully updated :: alert_cfg_id : "+alertCfg.getId()+" :: gateway_id : "+ alertCfg.getGateway_id());
					}
						
				} else {
					alertCfgList.get(0).setEnable(enable);
					AlertCfgWC alertCfg = alertCfgServiceV4.saveOrUpdateAlertCfgWC( alertCfgList.get(0) );	
				}
				
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			
		} catch (Exception e) {
			log.error("Error in updateCamConfig :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
			return response;
		}
		return response;
	}
	
	public void updategatewayPendingEvent( long gateway_id, String alert_type ) {
		log.info("Entered into updategatewayPendingEvent :: gateway_id : "+ gateway_id+" alert_type : "+ alert_type);
		try {
			GatewayPendingEvent gatewayPendingEvent = gatewayServiceV4.getGatewayPendingEvent( gateway_id );
			if( gatewayPendingEvent == null ) {
				gatewayPendingEvent = new GatewayPendingEvent();
				gatewayPendingEvent.setGateway_id( gateway_id );
			}
			
			if( alert_type.equalsIgnoreCase("temp") ) {
				gatewayPendingEvent.setTemp_alert(true);
			} else if( alert_type.equalsIgnoreCase("motion") ) {
				gatewayPendingEvent.setMotion_alert(true);
			} else if( alert_type.equalsIgnoreCase("sound") ) {
				gatewayPendingEvent.setSound_alert(true);
			} 
			
			gatewayPendingEvent.setUpdated_on( _helper.getCurrentTimeinUTC() );
			gatewayPendingEvent = gatewayServiceV4.saveOrUpdateGatewayPendingEvent( gatewayPendingEvent );	
			if( gatewayPendingEvent != null )
				log.info("gateway_pending_event saved successfully");
			else 
				log.info("gateway_pending_event saved failed");
		} catch (Exception e) {
			log.error("Error in updategatewayPendingEvent :: Error : "+ e.getLocalizedMessage());
		}
	}
	
	@GetMapping(value = "v5.0/devicelist", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getDeviceList(
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) String gatewayId,
			@RequestParam(value = "userid", defaultValue = "NA") String userId, @RequestParam("os") String os,
			@RequestParam(value = "type", defaultValue = "iphone", required = false) String type,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(defaultValue = "5", required = false) long monitor_type_id,
			@RequestHeader HttpHeaders header) {
		
		String auth = header.getFirst("auth");
		log.info("Entered :: getdevicelist : " + auth);
		log.info("user id : " + header.getFirst("userid"));
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			long userID = Long.valueOf(userId);
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				if (user.getId() != userID) {
					response.put("Status", 0);
					response.put("Msg", "User id mismatch");
					response.put("user_deleted", user.isDelete_user());
					log.info("User id for authey and user id parameter mismatch! ");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			List<Object> wc_device_list = gatewayServiceV4.getWCDeviceList(userID, Long.parseLong( gatewayId ), monitor_type_id);
			List<WCDeviceList> wc_sub_device_list = gatewayServiceV4.getSubDeviceList( user.getUsername());
			
			int subSubUserCount = gatewayServiceV4.getSubUserCount( user.getId() );
			monitor_type_id = monitor_type_id == 8 || monitor_type_id == 12 ? 8 : 5;
			List<NightVisionMode> nightVisionModeList = gatewayServiceV4.getNightVisionMode( monitor_type_id);
			
			List<JTimeInterval> alertInterval = getIntervalTimes(true);
			List<JTimeInterval> recordingTime = getIntervalTimes(false);
			
			response.put("freeplan_content", freeplanactivation_contentmc);
			response.put("paidplan_content", paidplanactivation_contentmc);
			response.put("trialplan_content", trialplanactivation_contentmc);
			
			if (wc_device_list == null && wc_sub_device_list == null) {
				response.put("Status", 0);
				response.put("Msg", "Session timeout, Please try again");
				response.put("wcdevicelist", wc_device_list);
				response.put("user_deleted", user.isDelete_user());
				response.put("alert_interval", alertInterval);
				response.put("recording_time", recordingTime);
				log.info("Error while getting device list for the user_id : " + userId);
			} else if (wc_device_list.isEmpty() && wc_sub_device_list.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "user don't have device!");
				response.put("wcdevicelist", wc_device_list);
				response.put("wcsubdevicelist", wc_sub_device_list);
				response.put("user_deleted", user.isDelete_user());
				response.put("alert_interval", alertInterval);
				response.put("recording_time", recordingTime);				
				log.info("No device found for user id : " + userId);
			} else {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("wcdevicelist", wc_device_list);
				response.put("wcsubdevicelist", wc_sub_device_list);
				response.put("maximum_device_count", 0);
				response.put("maximum_device_play_count", wc_maximum_device_play_count);
				response.put("maximum_sub_user_count", wc_maximum_sub_user_count);
				response.put("show_lan_permission", show_lan_permission);
				response.put("user_deleted", user.isDelete_user());
				response.put("sub_user_count", subSubUserCount);
				response.put("night_vision_mode", nightVisionModeList);
				response.put("alert_interval", alertInterval);
				response.put("recording_time", recordingTime);
				response.put("web_rtc_url", web_rtc_url);
				log.info("device list found for the user id : " + userId);
			}
			log.info("Exit :: deviceList");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to get device list ");
			response.put("Error", e.getLocalizedMessage());
			response.put("user_deleted", false);
			log.error("Exception occured at devicelist  :", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	private List<JTimeInterval> getIntervalTimes(boolean isAlertInterval) {
		List<JTimeInterval> intervalTimes = new ArrayList<>();
		//intervalTimes.add(new JTimeInterval(0, "30 Seconds", 30));
		intervalTimes.add(new JTimeInterval(1, "1 Minute", 60));
		intervalTimes.add(new JTimeInterval(2, "2 Minutes", 120));
		intervalTimes.add(new JTimeInterval(3, "3 Minutes", 180));
		if (isAlertInterval) {
			intervalTimes.add(new JTimeInterval(4, "5 Minutes", 300));
			intervalTimes.add(new JTimeInterval(5, "10 Minutes", 600));
		}
		return intervalTimes;
	}

	@GetMapping("v5.0/removegatewaycontentlist")
	@ResponseBody
	public JResponse getRemoveGatewayContentListV5(
			@RequestParam String os,
			@RequestParam String app_ver,
			@RequestParam( value = "monitortype", required = false) String monitortype,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam( value = "gateway_id",defaultValue = "0", required = false) long gateway_id) {
		log.info("Entered into getRemoveGatewayContentList");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			
			UserV4 user = null;
			try {
				
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				log.info("user_id : "+ user.getId());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			long monitorType = monitortype != null && monitortype != "" ? Long.valueOf(monitortype) : 0;
			
			List<RemoveGatewayType> removeGatewayTypeList = gatewayServiceV4.getRemoveGatewayType();
			
			if( monitorType != 1 ) {
				removeGatewayTypeList = removeGatewayTypeList.stream()
				.filter( remove -> remove.getId() != 1)
				.collect( Collectors.toList() );
			}
			
			if( removeGatewayTypeList == null || removeGatewayTypeList.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}
			
			AssetModel assetmodel = gatewayService.getAssetModelByGatewayId(gateway_id);
			
			String remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_without_subscription_content;
			String remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription;
			
			if(monitorType == 1) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "Pet Monitor");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "Pet Monitor");
			}else if(monitorType == 3) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "Smart AI Bowl");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "Smart AI Bowl");
			}else if(monitorType == 4) {
				if (assetmodel != null && assetmodel.isTemp_alert()) {
					remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "WaggleCam Ultra");
					remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "WaggleCam Ultra");
				} else {
					remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "WaggleCam Pro+");
					remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "WaggleCam Pro+");
				}
			}else if(monitorType == 5) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV Cam AI Mini");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "RV Cam AI Mini");
			}else if(monitorType == 6) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "WaggleCam Pro");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "WaggleCam Pro");
			}else if(monitorType == 8) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV 4G Camera");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "RV 4G Camera");
			}else if(monitorType == 9) {
				
				JGatewaySensorType gateway = gatewayService.getSensorDetailByGatewayId(gateway_id);
				if(gateway != null) {
					remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", gateway.getSensorName().toLowerCase());
					remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", gateway.getSensorName().toLowerCase());
				}else {
					remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV Smart Sensor");
					remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "RV Smart Sensor");
				}
				
			}else if(monitorType == 12) {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV 4G Mini");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "RV 4G Mini");
			}else {
				remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "device");
				remove_gateway_confirmation_body_with_subscription_loc = remove_gateway_confirmation_body_with_subscription_loc.replace("$$", "device");
			}
			
			response.put("confirmation_body", remove_gateway_confirmation_body_without_subscription_loc);
			boolean is_sub_avail = false;
			if (monitorType == 1) {
				List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(user.getChargebeeid());
				if (subscription != null) {
					is_sub_avail = true;
				}else {
					AllProductSubscription allProdSub = chargebeeService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitorType);
					if (allProdSub != null) {
						is_sub_avail = true;
					}
				}
			} else {
				AllProductSubscription allProdSub = chargebeeService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitorType);
				if (allProdSub != null) {
					is_sub_avail = true;
				}
			}
			
			List<JGatewayInfo> gatewayInfoList = gatewayServiceV4.getJGatewayInfo(user.getId());
			RemoveGatewayRequest removeGatewayRequest = null;
			removeGatewayRequest = gatewayServiceV4.getRemoveGatewayRequest(user.getId());
			
			response.put("show_remove_gateway", false);
			if( removeGatewayRequest != null ) {
				String date = removeGatewayRequest.getUpdated_on();
				Calendar cal = Calendar.getInstance();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date curDate = cal.getTime();
				
				cal.setTime( sdf.parse( removeGatewayRequest.getUpdated_on() ) );
				cal.add(Calendar.HOUR, remove_gateway_valid_hours_to_show);
				Date updatedDate = cal.getTime();
				
				if (curDate.before(updatedDate) ) {
					response.put("show_remove_gateway", false);
				}
				
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("remove_gateway_type", removeGatewayTypeList);
			response.put("confirmation_title", remove_gateway_confirmation_title);
			if( is_sub_avail ) {
				response.put("confirmation_note", remove_gateway_confirmation_note);
			}else{
				response.put("confirmation_note", "");
			}
			response.put("gateway_list", gatewayInfoList);
			
			if( gatewayInfoList == null || gatewayInfoList.isEmpty() || gatewayInfoList.size() <= show_remove_gateway_greater_than_device_cnt ) {
				response.put("show_remove_gateway", false);	
			}
			
		} catch (Exception e) {
			log.error("Error in getRemoveGatewayContentList :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}
	
	@RequestMapping(value = "v5.0/savecalib/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveCalibV5(@RequestParam("gatewayid") long gatewayid, @RequestParam("update_for") String update_for,
			@RequestParam(value = "temp_unit", defaultValue = "", required = false) String temp_unit,
			@RequestParam("value") String value,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering saveCalib : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			String message = null;
			boolean calib = false, dynamic_cmd_status = false;
			DecimalFormat formatter = new DecimalFormat("#0.00");

			if (usr != null) {
				float updateVal = 0, temp_calib_val = 0, battery_val = 0, charging_val = 0, full_charge_val = 0;
				Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayid));

				if (update_for.equalsIgnoreCase("temp")) {
					log.info("Save Temp Calib:");

					if (temp_unit.equalsIgnoreCase("F")) {
						updateVal = (Float.parseFloat(value) / 2);
					} else
						updateVal = Float.parseFloat(value);

					// Compare temp calib already exist.
					float current_calib = gateway.getTemp_calib();
					TempCalibStatus calibStatus = new TempCalibStatus();

					if (current_calib == updateVal) {
						response.put("Status", 1);
						response.put("Msg", "The value is already set to " + value + "");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else {
						calibStatus.setCurrent_calib(current_calib);
						calibStatus.setNew_calib(updateVal);
						calibStatus.setGateway_id(gatewayid);
						calibStatus.setStatus("InProgress");
						calibStatus.setUpdated_on(_helper.getCurrentTimeinUTC());
						calibStatus.setBattery_offset_status("InProgress");
						calibStatus.setCharging_offset_status("InProgress");
						calibStatus.setFullcharge_offset_status("InProgress");
					}

					calib = gatewayServiceV4.saveTempCalib(calibStatus);

					if (calib) {
						temp_calib_val = updateVal + gateway.getDefault_temp_calib();
						battery_val = updateVal + gateway.getDefault_battery_offset();
						charging_val = updateVal + gateway.getDefault_charging_offset();
						full_charge_val = updateVal + gateway.getDefault_fullcharge_offset();

//						message = "fullchgoffset=" + formatter.format(full_charge_val) + ",chgoffset="
//								+ formatter.format(charging_val) + ",batoffset=" + formatter.format(battery_val)
//								+ ",tempoffset=" + formatter.format(temp_calib_val);
						message = "fullchgoffset=" + formatter.format(full_charge_val) + ",batoffset="
								+ formatter.format(battery_val) + ",tempoffset=" + formatter.format(temp_calib_val);
//						dynamic_cmd_status = dynamicCmdService.saveDynamicCmdCalib(gateway, message, 1, "notsent", 0L);
						dynamic_cmd_status = dynamicCmdService.saveDynamicCmdV2(gateway, message, 1, "notsent", 0L);

						if (dynamic_cmd_status)
							log.info("Saved tempCalib in dynamic command table");
						else
							log.info("Failed to save tempCalib in dynamic command table");

						response.put("Status", 1);
						response.put("Msg", "Temperature calibration successful.");
						response.put("Note", "Note: Changes will take effect on \nthe next temperature update!");
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
						response.put("Note", "NA");
					}
				} else if (update_for.equalsIgnoreCase("pl")) {
					log.info("Save Power Loss Threshold:");
					PlThresholdStatus PlThresholdStatus = new PlThresholdStatus();

					PlThresholdStatus.setGateway_id(gatewayid);
					PlThresholdStatus.setPl_threshold(Integer.parseInt(value));
					PlThresholdStatus.setStatus("InProgress");
					PlThresholdStatus.setUpdated_on(_helper.getCurrentTimeinUTC());

					calib = gatewayServiceV4.savePLDelayFreq(PlThresholdStatus);
					message = "chgeventmaskint=" + value;
					dynamic_cmd_status = dynamicCmdService.saveDynamicCmdV2(gateway, message, 1, "notsent", 0L);

					if (dynamic_cmd_status)
						log.info("Saved power loss threshold in dynamic command table");
					else
						log.info("Failed to save power loss threshold in dynamic command table");

					if (calib) {
						response.put("Status", 1);
						response.put("Msg", "RV Power Loss threshold saved successfully");
						response.put("Note", "NA");
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
						response.put("Note", "NA");
					}
				}
			}
		} catch (Exception e) {
			log.error("Exception : saveCalib : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
			response.put("Note", "NA");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/warrantyinfo/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getWarrantyInfoV5(@RequestParam("os") String os,
			@RequestParam("app_ver") String appVer, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String auth = header.getFirst("auth");
		log.info(" Entered into getWarrantyInfo :: auth : " + auth);
		JResponse jResponse = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid Auth");
				return jResponse;
			}

			String country = user.getCountry().toUpperCase();
			if (country == null || country.isEmpty() || country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")) {
				country = "US";
			}

			String supportM = supportContactEmail.get(country);
			String supportP = supportContactNumber.get(country);

			String gatewayImg = "NA";
			boolean allWarrantyClaimed = true;
			try {
				List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "");
				boolean purchased_from_others = false;

				if (jGatewayList.isEmpty()) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Register your device to claim the Warranty");
					jResponse.put("all_warranty_claimed", allWarrantyClaimed);
				} else {

					List<JWarrantyInfo> jWarrantyInfoList = new ArrayList<JWarrantyInfo>();

					boolean all_warranty_claimed = true;
					for (JGateway gateway : jGatewayList) {
						
						if ((gateway.getMonitorTypeId() == 6 && restrictWCPro(appVer)) || gateway.getMonitorTypeId() == 9) { //Ignore WC Pro less than 7.7.2 and m10 device
							continue;
						}
						
						JWarrantyInfo jWarrantyInfo = new JWarrantyInfo();

						gatewayImg = gatewayServiceV4.getGatewayImgv5(gateway.getId());

						jWarrantyInfo.setGateway_id(gateway.getId());
						jWarrantyInfo.setGateway_name(gateway.getName());
						jWarrantyInfo.setOrder_channel(gateway.getOrder_channel());
						
						jWarrantyInfo.setGateway_img(gatewayImg);
						if(gateway.isPurchased_from_others() || gateway.isShowOrderId() ) {
							jWarrantyInfo.setWarranty_claimed( true );
						}else {
							all_warranty_claimed = false;
						}
						jWarrantyInfo.setPurchased_from_others( gateway.isPurchased_from_others() );
						jWarrantyInfo.setMonitortype_id(gateway.getMonitorTypeId());
						if (gateway.getMonitorTypeId() == 4) {
							if (gateway.isTemp_alert()) {
								jWarrantyInfo.setProduct_name(IrisservicesConstants.WAGGLE_CAM_ULTRA);
							} else
								jWarrantyInfo.setProduct_name(IrisservicesConstants.WAGGLE_CAM_PRO_PLUS);
						}
						
						jWarrantyInfoList.add(jWarrantyInfo);
						
					}
					
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Success");
					jResponse.put("gateway_list", jWarrantyInfoList);

					if( all_warranty_claimed ) 
						jResponse.put("Msg", "All warranty claimed");	
				}

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Error");
				return jResponse;
			}
			jResponse.put("Return Time", System.currentTimeMillis());
			return jResponse;
		} catch (Exception e) {
			log.error(" Error in getWarrantyInfo : " + e.getLocalizedMessage());
			jResponse.put("Status", 0);
			jResponse.put("Msg", "Error");
			return jResponse;
		}
	}
	
	private boolean restrictWCPro(String appVer) {
		String appVerSplit[] = appVer.split("\\.");
		
		if (Integer.parseInt(appVerSplit[0]) > 7 || Integer.parseInt(appVerSplit[0]) < 7) {
			return false;
		} else if (Integer.parseInt(appVerSplit[0]) == 7) {
			 if (Integer.parseInt(appVerSplit[1]) > 7) {
				 return false;
			 } else if (Integer.parseInt(appVerSplit[1]) == 7 && Integer.parseInt(appVerSplit[2]) > 1) {
				 return false;
			 }
		}
		return true;
	}

	@RequestMapping(value = "v5.0/findcountry", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse findCountryV5(HttpServletRequest httpRequest, HttpServletResponse hhtpResponse,
			@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver,
			@RequestHeader HttpHeaders header,Authentication authentication) {
		log.info(" Entered into findcountry v4 app controller");
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			response = commonService.findCountry( httpRequest, header );
			return response;
			
		} catch (Exception e) {
			log.error(" Error in findCountryV5 " + e.getLocalizedMessage());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("country_code", "US");
		} finally {
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}
	
	@RequestMapping(value = "v5.0/updatetempunit", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateTempUnitV5(@RequestParam("temperatureunit") String temperatureunit,
			@RequestParam("gateway_id") String gateway_id, @RequestParam("cmpid") long cmpid,
			@RequestParam("userid") String userid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("updateTempUnit for user id : " + userid);
		String autho = header.getFirst("auth");
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (usr.getCmpId() != cmpid) {
				log.info("Company doesn't match with user company : user id - " + userid + ", company id - " + cmpid);
				response.put("Status", 0);
				response.put("Msg", "Failed to update temperature unit");
				return response;
			}

			boolean updateTempUnit = gatewayServiceV4.updateTempUnit(cmpid, temperatureunit);
			log.info("Update temperature unit status : " + updateTempUnit);

			if (updateTempUnit) {
				String message = "";
				

				List<Gateway> gateway = gatewayService.getGatewayByUser(null, null, null, null,
						usr.getId(), null);			

				for(Gateway gw : gateway)
				{
					if (gw.getModel().getModel().toLowerCase().contains("n13")) {
						if (temperatureunit.equalsIgnoreCase("F"))
							message = "LCDMODE=1";
						else
							message = "LCDMODE=2";
					} else {
						if (temperatureunit.equalsIgnoreCase("F"))
							message = "LCDMODE=2";
						else
							message = "LCDMODE=1";
					
						}
					if(gw.getModel().getModel().toLowerCase().contains("n13") || (gw.getModel().getIsgps().equalsIgnoreCase("true")
							|| gw.getModel().getIsgps().equalsIgnoreCase("1")))
						updateTempUnit = dynamicCmdService.saveDynamicCmdV2(gw, message, 1, "notsent", 0L);
					else
						log.info("No OTA sent for non gps model : gateway_id : " + gw.getId());
				}
				response.put("Status", 1);
				response.put("Msg", "Temperature unit updated successfully");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update temperature unit");
			}
		} catch (Exception e) {
			log.error("Exception : updatetempunit :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed to update temperature unit");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}	

	// ========save or update gateway================
	@RequestMapping(value = "v4.0/saveorupdatePetProfile/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorupdatePetProfileV4(@RequestBody List<JPetprofile> jpetprofiles,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering saveorupdatePetProfile : " + autho);
		try {
			if (jpetprofiles != null) {

				Map<String, String> map = new HashMap<String, String>();

				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);

					JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : " + autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				long userId = Long.valueOf(map.get("user_id"));
				long cmpType_id = Long.valueOf(map.get("cmpType_id"));

				if (cmpType_id == 3) {

					int status = 0;
					for (JPetprofile jpetprofile : jpetprofiles) {
						PetProfile petProfile = null;
						boolean GatewayAva = false;

						String gateway_name = jpetprofile.getName();
						Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
						Matcher hasSpecial = special.matcher(gateway_name);
						if (hasSpecial.find()) {
							log.info("Gateway name contains special chars");
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.ER009);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, userId, null);
						for (JGateway jGateway : gateways) {

							if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
								GatewayAva = true;
								if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName())) {
									response.put("Status", 0);
									response.put("Msg", RegisterUserError.petNameUserMessage);
									response.put("Return Time", System.currentTimeMillis());
									return response;

								}
							}
						}

						if (jpetprofile != null) {
							if (!jpetprofile.getGateway_id().isEmpty() && !jpetprofile.getAge().isEmpty()
									&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
									&& !jpetprofile.getSex().isEmpty()) {

								Date birth_date = gatewayService.getBirthDate("YEAR", jpetprofile.getAge());
								SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
								String date = sf.format(birth_date);

								PetSpecies species = petSpeciesServices
										.getPetSpeciesByName(jpetprofile.getSpecieName());

								if (species == null) {
									species = petSpeciesServices.getPetSpecies(1L);
								}

								status = gatewayServiceV4.saveorupdatePetprofileV4(jpetprofile, userId, GatewayAva,
										date, species.getId());

							} else {
								log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

								response.put("Status", 0);
								response.put("Msg",
										"Enter all the mandatory fields like gatewayid,name,age,sex and breed");
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

						} 
//						else {
//							log.info("saveORupdatePetprofile: Error: Input JPetprofile is null");
//							response.put("Status", 0);
//							response.put("Msg", "Enter all the mandatory fields like gatewayid,name,age,sex and breed");
//							response.put("Return Time", System.currentTimeMillis());
//							return response;
//						}
					}

					log.info("Enter Into Gateway");
					if (status == 1) {
						for (JPetprofile jpetprofile : jpetprofiles) {

							log.info("Enter Into Gateway");
							boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
									jpetprofile.getGateway_id());
							log.info("GatewayName updated with respect to petname");

							long profileId = jpetprofile.getId();
							if (profileId == 0) { 
								PetProfile pp = gatewayService.getPetProfile(Long.parseLong(jpetprofile.getGateway_id()));
								if( pp != null ) {
									profileId = pp.getId();
								}
							}
							
							if (profileId != 0)
								gatewayServiceV4.saveOrUpdateGatewayProfile(Long.parseLong(jpetprofile.getGateway_id()), profileId);

						}
						response.put("Status", 1);
						response.put("Msg", "success");
						response.put("Msg", "All profiles are saved Successfully");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Error, Not saved");
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid company type");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty input profile list");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("saveorupdatePetProfile::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "DataIntegrityViolationException");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
			response.put("Msg", "UnExcepted Error in pet profile");
			response.put("Error", e.getLocalizedMessage());

		}
		log.info("Exit saveorupdatePetProfile");
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// -Balaji
	@RequestMapping(value = "v4.0/warrantyinfo/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getWarrantyInfo(@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "app_ver", required = false) String appVer, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String auth = header.getFirst("auth");
		log.info(" Entered into getWarrantyInfo :: auth : " + auth);
		JResponse jResponse = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid Auth");
				return jResponse;
			}

			String country = user.getCountry().toUpperCase();
			if (country == null || country.isEmpty() || country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")) {
				country = "US";
			}

			String supportM = supportContactEmail.get(country);
			String supportP = supportContactNumber.get(country);

			String gatewayImg = "NA";
			boolean allWarrantyClaimed = true;
			try {
				List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "1");
				boolean purchased_from_others = false;

				if (jGatewayList.isEmpty()) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Register your device to claim the Warranty");
					jResponse.put("all_warranty_claimed", allWarrantyClaimed);
				} else {

					List<JWarrantyInfo> jWarrantyInfoList = new ArrayList<JWarrantyInfo>();

					for (JGateway gateway : jGatewayList) {

						JWarrantyInfo jWarrantyInfo = new JWarrantyInfo();

						if (gateway.isPurchased_from_others())
							purchased_from_others = true;
						else if (!gateway.isShowOrderId()) {
							gatewayImg = gatewayServiceV4.getGatewayImg(gateway.getId());

							jWarrantyInfo.setGateway_id(gateway.getId());
							jWarrantyInfo.setGateway_name(gateway.getName());
							jWarrantyInfo.setGateway_img(gatewayImg);
							jWarrantyInfoList.add(jWarrantyInfo);
						}
					}

					if (jWarrantyInfoList.isEmpty()) {
						jResponse.put("Status", 1);
						if (purchased_from_others) {
							String eRes = RegisterUserError.contectMsgNormalTxt;
							String msg = eRes.replace("#SP#", supportP).replace("#SM#", supportM)
									+ " to register your warranty.";
							jResponse.put("Msg", msg);
						} else
							jResponse.put("Msg", "Your Warranty is already registered");
						jResponse.put("all_warranty_claimed", allWarrantyClaimed);
					} else {
						jResponse.put("Status", 1);
						jResponse.put("Msg", "Success");
						jResponse.put("gateway_list", jWarrantyInfoList);
						jResponse.put("all_warranty_claimed", false);
					}

				}

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Error");
				return jResponse;
			}
			jResponse.put("Return Time", System.currentTimeMillis());
			return jResponse;
		} catch (Exception e) {
			log.error(" Error in getWarrantyInfo : " + e.getLocalizedMessage());
			jResponse.put("Status", 0);
			jResponse.put("Msg", "Error");
			return jResponse;
		}
	}
	
	//Save temperature calibration and PLPB delay - Savitha
	@RequestMapping(value = "v4.0/savecalib/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveCalib(@RequestParam("gatewayid") long gatewayid, @RequestParam("update_for") String update_for,
			@RequestParam(value = "temp_unit", defaultValue = "", required = false) String temp_unit,
			@RequestParam("value") String value,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering saveCalib : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			String message = null;
			boolean calib = false, dynamic_cmd_status = false;
			DecimalFormat formatter = new DecimalFormat("#0.00");

			if (usr != null) {
				float updateVal = 0, temp_calib_val = 0, battery_val = 0, charging_val = 0, full_charge_val = 0;
				Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayid));

				if (update_for.equalsIgnoreCase("temp")) {
					log.info("Save Temp Calib:");

					if (temp_unit.equalsIgnoreCase("F")) {
						updateVal = (Float.parseFloat(value) / 2);
					} else
						updateVal = Float.parseFloat(value);

					// Compare temp calib already exist.
					float current_calib = gateway.getTemp_calib();
					TempCalibStatus calibStatus = new TempCalibStatus();

					if (current_calib == updateVal) {
						response.put("Status", 1);
						response.put("Msg", "The value is already set to " + value + "");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else {
						calibStatus.setCurrent_calib(current_calib);
						calibStatus.setNew_calib(updateVal);
						calibStatus.setGateway_id(gatewayid);
						calibStatus.setStatus("InProgress");
						calibStatus.setUpdated_on(_helper.getCurrentTimeinUTC());
						calibStatus.setBattery_offset_status("InProgress");
						calibStatus.setCharging_offset_status("InProgress");
						calibStatus.setFullcharge_offset_status("InProgress");
					}

					calib = gatewayServiceV4.saveTempCalib(calibStatus);

					if (calib) {
						temp_calib_val = updateVal + gateway.getDefault_temp_calib();
						battery_val = updateVal + gateway.getDefault_battery_offset();
						charging_val = updateVal + gateway.getDefault_charging_offset();
						full_charge_val = updateVal + gateway.getDefault_fullcharge_offset();

//						message = "fullchgoffset=" + formatter.format(full_charge_val) + ",chgoffset="
//								+ formatter.format(charging_val) + ",batoffset=" + formatter.format(battery_val)
//								+ ",tempoffset=" + formatter.format(temp_calib_val);
						message = "fullchgoffset=" + formatter.format(full_charge_val) + ",batoffset="
								+ formatter.format(battery_val) + ",tempoffset=" + formatter.format(temp_calib_val);
//						dynamic_cmd_status = dynamicCmdService.saveDynamicCmdCalib(gateway, message, 1, "notsent", 0L);
						dynamic_cmd_status = dynamicCmdService.saveDynamicCmdV2(gateway, message, 1, "notsent", 0L);

						if (dynamic_cmd_status)
							log.info("Saved tempCalib in dynamic command table");
						else
							log.info("Failed to save tempCalib in dynamic command table");

						response.put("Status", 1);
						response.put("Msg", "Temperature calibration successful.");
						response.put("Note", "Note: Changes will take effect on \nthe next temperature update!");
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
						response.put("Note", "NA");
					}
				} else if (update_for.equalsIgnoreCase("pl")) {
					log.info("Save Power Loss Threshold:");
					PlThresholdStatus PlThresholdStatus = new PlThresholdStatus();

					PlThresholdStatus.setGateway_id(gatewayid);
					PlThresholdStatus.setPl_threshold(Integer.parseInt(value));
					PlThresholdStatus.setStatus("InProgress");
					PlThresholdStatus.setUpdated_on(_helper.getCurrentTimeinUTC());

					calib = gatewayServiceV4.savePLDelayFreq(PlThresholdStatus);
					message = "chgeventmaskint=" + value;
					dynamic_cmd_status = dynamicCmdService.saveDynamicCmdV2(gateway, message, 1, "notsent", 0L);

					if (dynamic_cmd_status)
						log.info("Saved power loss threshold in dynamic command table");
					else
						log.info("Failed to save power loss threshold in dynamic command table");

					if (calib) {
						response.put("Status", 1);
						response.put("Msg", "RV Power Loss threshold saved successfully");
						response.put("Note", "NA");
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
						response.put("Note", "NA");
					}
				}
			}
		} catch (Exception e) {
			log.error("Exception : saveCalib : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Uh-Oh!\nRequest cannot be processed at this moment. Please try again later.");
			response.put("Note", "NA");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	// Update temperature unit - Savitha
	@RequestMapping(value = "v4.0/updatetempunit", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateTempUnit(@RequestParam("temperatureunit") String temperatureunit,
			@RequestParam("gateway_id") String gateway_id, @RequestParam("cmpid") long cmpid,
			@RequestParam("userid") String userid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("updateTempUnit for user id : " + userid);
		String autho = header.getFirst("auth");
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (usr.getCmpId() != cmpid) {
				log.info("Company doesn't match with user company : user id - " + userid + ", company id - " + cmpid);
				response.put("Status", 0);
				response.put("Msg", "Failed to update temperature unit");
				return response;
			}

			boolean updateTempUnit = gatewayServiceV4.updateTempUnit(cmpid, temperatureunit);
			log.info("Update temperature unit status : " + updateTempUnit);

			if (updateTempUnit) {
				String message = "";
				

				List<Gateway> gateway = gatewayService.getGatewayByUser(null, null, null, null,
						usr.getId(), null);			

				for(Gateway gw : gateway)
				{
					if (gw.getModel().getModel().toLowerCase().contains("n13")) {
						if (temperatureunit.equalsIgnoreCase("F"))
							message = "LCDMODE=1";
						else
							message = "LCDMODE=2";
					} else {
						if (temperatureunit.equalsIgnoreCase("F"))
							message = "LCDMODE=2";
						else
							message = "LCDMODE=1";
					
						}
					if(gw.getModel().getModel().toLowerCase().contains("n13") || (gw.getModel().getIsgps().equalsIgnoreCase("true")
							|| gw.getModel().getIsgps().equalsIgnoreCase("1")))
						updateTempUnit = dynamicCmdService.saveDynamicCmdV2(gw, message, 1, "notsent", 0L);
					else
						log.info("No OTA sent for non gps model : gateway_id : " + gw.getId());
				}
				response.put("Status", 1);
				response.put("Msg", "Temperature unit updated successfully");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update temperature unit");
			}
		} catch (Exception e) {
			log.error("Exception : updatetempunit :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed to update temperature unit");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}	
	
	
	/**
	 * 
	 * @param httpRequest
	 * @param hhtpResponse
	 * @param os
	 * @param app_ver
	 * @param header
	 * @return
	 * 
	 * This API used to get user location based on request IP, 
	 * First we check X-Forwarded-For in header, if it have the X-Forwarded-For use the ip to get location
	 * If header not contain X-Forwarded-For use server ip to get location
	 * If any error occurs give location as US defaultly
	 * This API is used by android only !!
	 * 
	 * - BALAJI
	 * 
	 */
	
	@RequestMapping(value = "v4.0/findcountry", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse findCountry(HttpServletRequest httpRequest, HttpServletResponse hhtpResponse,
			@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver,
			@RequestHeader HttpHeaders header,Authentication authentication) {
		log.info(" Entered into findcountry v4 app controller");
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			response = commonService.findCountry( httpRequest, header );
			return response;
			
		} catch (Exception e) {
			log.error(" Error in findCountryV4 " + e.getLocalizedMessage());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("country_code", "US");
		} finally {
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}

	// kalai
	@RequestMapping(value = "v4.0/listpetprofile", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPetProfileForFlutter(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering listPetProfileForFlutter : " + autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<JPetprofileFlutter> profilelist = petSpeciesServices.getJPetprofilesForFlutter(usr.getId(), "1,2");
	
			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("profilelist", profilelist);

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : listPetProfileForFlutter : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/listpetprofileWatch/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listpetprofileWatchV5(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String user_id = header.getFirst("user_id");
		log.info("Entering listpetprofileWatch : " + user_id);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("id", user_id);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "user id not found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			List<JPetprofileWatch> profilelist = petSpeciesServices.getJPetprofilesForWatch(usr.getId());

			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("profilelist", profilelist);

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : listpetprofileWatch : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	// kalai
	@RequestMapping(value = "v4.0/listpetprofileWatch/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listpetprofileWatch(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String user_id = header.getFirst("user_id");
		log.info("Entering listpetprofileWatch : " + user_id);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("id", user_id);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "user id not found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			List<JPetprofileWatch> profilelist = petSpeciesServices.getJPetprofilesForWatch(usr.getId());

			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("profilelist", profilelist);

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : listpetprofileWatch : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
		
		
		@GetMapping("v4.0/removegatewaycontentlist")
		@ResponseBody
		public JResponse getRemoveGatewayContentList(
				@RequestParam String os,
				@RequestParam String app_ver,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime
				) {
			log.info("Entered into getRemoveGatewayContentList");
			JResponse response = new JResponse();
			try {
				String auth = header.getFirst("auth");
				
				UserV4 user = null;
				try {
					
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}
					log.info("user_id : "+ user.getId());
				} catch (InvalidAuthoException e) {
					log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}
				
				List<RemoveGatewayType> removeGatewayTypeList = gatewayServiceV4.getRemoveGatewayType();
				if( removeGatewayTypeList == null || removeGatewayTypeList.isEmpty() ) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				response.put("confirmation_body", remove_gateway_confirmation_body_without_subscription);
				List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(user.getChargebeeid());
				if( subscription != null ) {
					response.put("confirmation_body", remove_gateway_confirmation_body_with_subscription);	
				}
				
				List<JGatewayInfo> gatewayInfoList = gatewayServiceV4.getJGatewayInfo(user.getId());
				RemoveGatewayRequest removeGatewayRequest = null;
				removeGatewayRequest = gatewayServiceV4.getRemoveGatewayRequest(user.getId());
				
				response.put("show_remove_gateway", false);
				if( removeGatewayRequest != null ) {
					String date = removeGatewayRequest.getUpdated_on();
					Calendar cal = Calendar.getInstance();
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date curDate = cal.getTime();
					
					cal.setTime( sdf.parse( removeGatewayRequest.getUpdated_on() ) );
					cal.add(Calendar.HOUR, remove_gateway_valid_hours_to_show);
					Date updatedDate = cal.getTime();
					
					if (curDate.before(updatedDate) ) {
						response.put("show_remove_gateway", false);
					}
					
				}
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("remove_gateway_type", removeGatewayTypeList);
				response.put("confirmation_title", remove_gateway_confirmation_title);
				response.put("gateway_list", gatewayInfoList);
				
				if( gatewayInfoList == null || gatewayInfoList.isEmpty() || gatewayInfoList.size() <= show_remove_gateway_greater_than_device_cnt ) {
					response.put("show_remove_gateway", false);	
				}
				
			} catch (Exception e) {
				log.error("Error in getRemoveGatewayContentList :: Error : "+ e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
			return response;
		}
		
		@PostMapping("v5.0/updateremovegateway")
		@ResponseBody
		public JResponse updateRemoveGatewayV5(
				@RequestParam String os,
				@RequestParam String app_ver,
				@RequestBody JUpdateRemoveGateway updateRemoveGateway,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
			log.info("Entered into updateRemoveGateway :: auth : "+header.getFirst("auth"));
			JResponse response = new JResponse();
			try {
				String auth = header.getFirst("auth");
				
				UserV4 user = null;
				try {
					
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}
					log.info("user_id : "+ user.getId());
				} catch (InvalidAuthoException e) {
					log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}
				
				if( updateRemoveGateway.getRemove_gateway_type_id() == 0 ) {
					log.info("cancel_feedback_id is 0");
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				RemoveGatewayType removeGatewayType = gatewayServiceV4.getRemoveGatewayTypeById( updateRemoveGateway.getRemove_gateway_type_id() );
				if( removeGatewayType == null ) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				JGatewayDetails gateway = gatewayServiceV4.getJGatewayDetails("id", String.valueOf( updateRemoveGateway.getGateway_id()) );
	 			if( gateway == null ) {
	 				log.error("gateway not found for gateway_id : "+ updateRemoveGateway.getGateway_id());
	 				response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
	 			}
	 			long monitortype_id = updateRemoveGateway.getMonitortype_id();
				if(monitortype_id !=5 && monitortype_id != 6 && monitortype_id != 9 && monitortype_id != 8 && monitortype_id != 12) {
					Properties prop = new Properties();
					File file = ResourceUtils.getFile("classpath:iris3.properties");
					prop.load(new FileInputStream(file));
	
					String to_address = prop.getProperty("to_address");
					String cc_address = prop.getProperty("cc_address");
					String bcc_address = prop.getProperty("bcc_address");					
					
					String mailSub = "Reg Remove Device : "+ user.getUsername();
					String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway removal request</p>";
					mailContent += "<p>Email             : "+ user.getEmail() +"</p>";
					mailContent += "<p>Chargebee Id      : "+ user.getChargebeeid() +"</p>";
					mailContent += "<p>Gateway Id        : "+ updateRemoveGateway.getGateway_id() +"</p>";
					mailContent += "<p>Meid			   	 : "+ gateway.getMeid() +"</p>";
					mailContent += "<p>Gateway Name      : "+ gateway.getName() +"</p>";
					mailContent += "<p>Reason            : "+ removeGatewayType.getRemove_reason() +"</p>";
					mailContent += "<p>Customer Review   : "+ updateRemoveGateway.getCustomer_review() +"</p>";
					
					mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
					async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
					
				}else {
					
					dynamiccmdService.delDynamicCmd(user.getId(), String.valueOf( updateRemoveGateway.getGateway_id() ));
					userServiceV4.delUserGatewayV2(user.getId(),updateRemoveGateway.getGateway_id());//remove gateway from usergateway
					gatewayService.delGateway(updateRemoveGateway.getGateway_id());
					
					if(monitortype_id == 9) {
						ArrayList<Long> alertCfgIds = alertcfgService.getAlertCfgIds(updateRemoveGateway.getGateway_id());

						//Remove alerts
						boolean alert_status = alertCfgServiceV4.deleteAlertByAssetId( updateRemoveGateway.getGateway_id() );
						log.info("delete  status of alert :: status : "+ alert_status);
						
						//Remove alertcfg mapping
						alert_status = alertcfgService.delAlertcfg(user.getId(), updateRemoveGateway.getGateway_id()+"");
						log.info("detele status of alertcfg_to_asset :: status : "+ alert_status);
						
						//Remove alertcfg
						alert_status = alertCfgServiceV4.deleteAlertCfgById( alertCfgIds );
						log.info("detele status of alertcfg :: status : "+ alert_status);
						
						gatewayServiceV4.updateMeidForGateway(updateRemoveGateway.getGateway_id());
					}
					gatewayService.delAsset(gateway.getMeid());
					gatewayServiceV4.delGatewayFeature(gateway.getId());
					
					if(monitortype_id ==5 || monitortype_id == 6 || monitortype_id == 8 || monitortype_id == 12) {
						Properties prop = new Properties();
						File file = ResourceUtils.getFile("classpath:iris3.properties");
						prop.load(new FileInputStream(file));
		
						String to_address = prop.getProperty("to_address");
						String cc_address = prop.getProperty("cc_address");
						String bcc_address = prop.getProperty("bcc_address");					
						
						String mailSub = "Reg Remove Device : "+ user.getUsername();
						String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway removal request</p>";
						mailContent += "<p>Email             : "+ user.getEmail() +"</p>";
						mailContent += "<p>Chargebee Id      : "+ user.getChargebeeid() +"</p>";
						mailContent += "<p>Gateway Id        : "+ updateRemoveGateway.getGateway_id() +"</p>";
						mailContent += "<p>Meid			   	 : "+ gateway.getMeid() +"</p>";
						mailContent += "<p>Gateway Name      : "+ gateway.getName() +"</p>";
						mailContent += "<p>Reason            : "+ removeGatewayType.getRemove_reason() +"</p>";
						mailContent += "<p>Customer Review   : "+ updateRemoveGateway.getCustomer_review() +"</p>";
						
						mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
						async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
						
						try {

							AllProductSubscription allProdSub = cbService.getProductSubscriptionByGateway(gateway.getId(),user.getChargebeeid());
							
							if (allProdSub != null && allProdSub.getSubscriptionId() != null && !freeplan.contains(allProdSub.getPlanId())) {
								JCancelSubDetail jCancelSubDetail = new JCancelSubDetail();
								jCancelSubDetail.setGateway_id(gateway.getId());
								jCancelSubDetail.setMonitortype_id(monitortype_id);
								jCancelSubDetail.setReason_type(String.valueOf(updateRemoveGateway.getRemove_gateway_type_id()));
								jCancelSubDetail.setReason_desc(updateRemoveGateway.getCustomer_review());
								jCancelSubDetail.setCb_subid(allProdSub.getSubscriptionId());

								cbService.cancelsubplanV5(user, jCancelSubDetail, false);
							}

						} catch (Exception e) {
							log.info("Error in cancel subscription :: gateway : " + gateway.getId() + " Error : "
									+ e.getLocalizedMessage());
						}
						
					}
					
					niomServiceV4.deleteMappedOrder(gateway.getMeid());
				}
				
				RemoveGatewayRequest removeGatewayRequest = null;
				removeGatewayRequest = gatewayServiceV4.getRemoveGatewayRequest(user.getId());
				if( removeGatewayRequest == null ) {
					removeGatewayRequest = new RemoveGatewayRequest();
					removeGatewayRequest.setUser_id( user.getId() );
				}
				
				removeGatewayRequest.setGateway_id( updateRemoveGateway.getGateway_id() );
				removeGatewayRequest.setRemove_gateway_type_id( updateRemoveGateway.getRemove_gateway_type_id() );
				removeGatewayRequest.setReason( updateRemoveGateway.getCustomer_review() );
				
				removeGatewayRequest.setUpdated_on( _helper.getCurrentTimeinUTC() );
				removeGatewayRequest = gatewayServiceV4.saveOrUpdateRemoveGatewayRequest( removeGatewayRequest );
				if( removeGatewayRequest == null ) {
					log.error("Error while saving removeGatewayRequest");
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				async.saveOrUpdateRemoveGatewayRequestHistory( new RemoveGatewayRequestHistory( removeGatewayRequest ) );
					
				response.put("Status", 1);
				response.put("Msg", "Success");
				
				String remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription;
			
				response.put("title", remove_gateway_title_msg);
				
				if(monitortype_id !=5 && monitortype_id != 6 && monitortype_id != 9 && monitortype_id != 8 && monitortype_id != 12) {
					response.put("common_msg", remove_gateway_common_msg);
				}else {
					if(monitortype_id == 5) {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV Cam AI Mini");
					}else if(monitortype_id == 6) {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "Waggle Cam Pro");
					}else if(monitortype_id == 8) {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV 4G Camera");
					}else if(monitortype_id == 9) {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "Sensor");
					}else if(monitortype_id == 12) {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "RV 4G Mini");
					}else {
						remove_gateway_confirmation_body_without_subscription_loc = remove_gateway_confirmation_body_without_subscription_loc.replace("$$", "Device");
					}
					
					response.put("common_msg", remove_gateway_confirmation_body_without_subscription_loc);
				}
				
				
				if( removeGatewayType.getRemove_reason().contains("Upgraded") ) {
					response.put("common_msg", remove_gateway_upgrade_msg);	
				}
				
			} catch (Exception e) {
				log.error("Error in updateRemoveGateway :: Error : "+ e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
			
			return response;
		}
		
		@PostMapping("v4.0/updateremovegateway")
		@ResponseBody
		public JResponse updateRemoveGateway(
				@RequestParam String os,
				@RequestParam String app_ver,
				@RequestBody JUpdateRemoveGateway updateRemoveGateway,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime
				) {
			log.info("Entered into updateRemoveGateway");
			JResponse response = new JResponse();
			try {
				String auth = header.getFirst("auth");
				
				UserV4 user = null;
				try {
					
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}
					log.info("user_id : "+ user.getId());
				} catch (InvalidAuthoException e) {
					log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}
				
				if( updateRemoveGateway.getRemove_gateway_type_id() == 0 ) {
					log.info("cancel_feedback_id is 0");
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				Properties prop = new Properties();
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));

				String to_address = prop.getProperty("to_address");
				String cc_address = prop.getProperty("cc_address");
				String bcc_address = prop.getProperty("bcc_address");
				
				RemoveGatewayType removeGatewayType = gatewayServiceV4.getRemoveGatewayTypeById( updateRemoveGateway.getRemove_gateway_type_id() );
				if( removeGatewayType == null ) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				JGatewayDetails gateway = gatewayServiceV4.getJGatewayDetails("id", String.valueOf( updateRemoveGateway.getGateway_id()) );
	 			if( gateway == null ) {
	 				log.error("gateway not found for gateway_id : "+ updateRemoveGateway.getGateway_id());
	 				response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
	 			}
				
				String mailSub = "Reg Remove Device : "+ user.getUsername();
				String mailContent = "<p>Hi Team,</p>" + "<p>Find the user gateway removal request</p>";
				mailContent += "<p>Email             : "+ user.getEmail() +"</p>";
				mailContent += "<p>Chargebee Id      : "+ user.getChargebeeid() +"</p>";
				mailContent += "<p>Gateway Id        : "+ updateRemoveGateway.getGateway_id() +"</p>";
				mailContent += "<p>Meid			   	 : "+ gateway.getMeid() +"</p>";
				mailContent += "<p>Gateway Name      : "+ gateway.getName() +"</p>";
				mailContent += "<p>Reason            : "+ removeGatewayType.getRemove_reason() +"</p>";
				mailContent += "<p>Customer Review   : "+ updateRemoveGateway.getCustomer_review() +"</p>";
				
				mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
				async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
				
				RemoveGatewayRequest removeGatewayRequest = null;
				removeGatewayRequest = gatewayServiceV4.getRemoveGatewayRequest(user.getId());
				if( removeGatewayRequest == null ) {
					removeGatewayRequest = new RemoveGatewayRequest();
					removeGatewayRequest.setUser_id( user.getId() );
				}
				
				removeGatewayRequest.setGateway_id( updateRemoveGateway.getGateway_id() );
				removeGatewayRequest.setRemove_gateway_type_id( updateRemoveGateway.getRemove_gateway_type_id() );
				removeGatewayRequest.setReason( updateRemoveGateway.getCustomer_review() );
				
				removeGatewayRequest.setUpdated_on( _helper.getCurrentTimeinUTC() );
				removeGatewayRequest = gatewayServiceV4.saveOrUpdateRemoveGatewayRequest( removeGatewayRequest );
				if( removeGatewayRequest == null ) {
					log.error("Error while saving removeGatewayRequest");
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}
				
				async.saveOrUpdateRemoveGatewayRequestHistory( new RemoveGatewayRequestHistory( removeGatewayRequest ) );
					
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("title", remove_gateway_title_msg);
				response.put("common_msg", remove_gateway_common_msg);
				
				if( removeGatewayType.getRemove_reason().contains("Upgraded") ) {
					response.put("common_msg", remove_gateway_upgrade_msg);	
				}
				
			} catch (Exception e) {
				log.error("Error in updateRemoveGateway :: Error : "+ e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
			
			return response;
		}
		
		/** <AUTHOR> */
		@PostMapping(value = "v5.0/saveorupdatepetprofile", headers = "Accept=application/json")
		@ResponseBody
		public JResponse saveOrUpdatePetProfileV5(@RequestBody List<JPetprofile> jpetprofiles,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering saveorupdatePetProfile : " + autho +" params : "+jpetprofiles.get(0).toString() );
			try {
				if (jpetprofiles != null) {
					Map<String, String> map = new HashMap<String, String>();
					try {
						map = userServiceV4.getUserId_cmpIdByAuthV2(autho);

						JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
						if (errResponse != null) {
							return errResponse;
						}

					} catch (InvalidAuthoException ex) {
						response.put("Status", 0);
						response.put("Msg", "invalid authkey");
						response.put("Error", ex.getLocalizedMessage());
						log.error("Exception while getting user for authkey : " + autho);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}

					long userId = Long.valueOf(map.get("user_id"));
					long cmpType_id = Long.valueOf(map.get("cmpType_id"));
					
					if (cmpType_id == 3) {
						int status = 0;
						for (JPetprofile jpetprofile : jpetprofiles) {
							String gateway_name = jpetprofile.getName();
							Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
							Matcher hasSpecial = special.matcher(gateway_name);
							if (hasSpecial.find()) {
								log.info("Gateway name contains special chars");
								response.put("Status", 0);
								response.put("Msg", RegisterUserError.ER009);
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}
							
							List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, 
									userId, null);
							for (JGateway jGateway : gateways) {
								if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
									if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName()) && jpetprofile.getMonitortype() == jGateway.getMonitorTypeId()) {
										response.put("Status", 0);
										response.put("Msg", RegisterUserError.petNameUserMessage);
										response.put("Return Time", System.currentTimeMillis());
										return response;
									}
								}
							}

							if (jpetprofile != null) {
								if (!jpetprofile.getGateway_id().isEmpty() && !jpetprofile.getAge().isEmpty()
										&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
										&& !jpetprofile.getSex().isEmpty()) {

									Date birth_date = gatewayService.getBirthDate("YEAR", jpetprofile.getAge());
									SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
									String date = sf.format(birth_date);

									PetSpecies species = petSpeciesServices
											.getPetSpeciesByName(jpetprofile.getSpecieName());

									if (species == null) {
										species = petSpeciesServices.getPetSpecies( jpetprofile.getSpeciesid() );
									}

									status = gatewayServiceV4.saveorupdatePetprofileV5(jpetprofile, userId, 
											date, species.getId());

								} else {
									log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

									response.put("Status", 0);
									response.put("Msg",
											"Enter all the mandatory fields like gatewayid,name,age,sex and breed");
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}
							}
						}

						log.info("Enter Into Gateway");
						if (status == 1) {							
							for (JPetprofile jpetprofile : jpetprofiles) {
								long gatewayId = Long.valueOf(jpetprofile.getGateway_id());
								long profileId = jpetprofile.getId();
								if (profileId == 0) { 
									PetProfile petProfile = gatewayService.getPetProfile(gatewayId);
									if( petProfile != null ) {
										profileId = petProfile.getId();
									}
									
//									profileId = gatewayServiceV4.getGatewayProfile(gatewayId);
									
									status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
//									boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
//											jpetprofile.getGateway_id());
									boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),
											profileId);
									log.info("GatewayName updated with respect to petname");
								}
								else {
									status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
//									boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
//											jpetprofile.getGateway_id());
									
									boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),
											profileId);
									
									log.info("GatewayName updated with respect to petname");
								}

								log.info("GatewayName and profileId updated with respect to petname");

							}
							
							double MER = 0;
							
								double weight = Double.parseDouble( jpetprofiles.get(0).getWeight() );
								double weightPounds = weight * 0.453592;
								double RER = 70 * Math.pow(weightPounds, 0.75);
								double signalment = petSpeciesServicesv4.getSignalment( jpetprofiles.get(0).getSpeciesid() , jpetprofiles.get(0).isIntact());		
								double BCS = petSpeciesServicesv4.getBCS( jpetprofiles.get(0).getStructure() != null && jpetprofiles.get(0).getStructure() != "" ? jpetprofiles.get(0).getStructure() : "ideal");	
								String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.get(0).getActivitylevel());
								double activityLevel = petSpeciesServicesv4.getActivityLevel( ActivityLel);
								MER = RER * signalment * activityLevel * BCS;
								
								PetProfile petProfile = gatewayService.getPetProfile(Long.valueOf(jpetprofiles.get(0).getGateway_id()));
								if( petProfile != null ) {
									asyncService.updateReqCaloriesById( petProfile.getId(), MER);
									
									double tot_calories = MER;
									double foodCal = gatewayService.getPetFoodCaloriesByProfileId(petProfile.getId());
									
									if(foodCal > 0) {
										double caloriesPerGram= foodCal/1000;
										double weightGramsperDay = tot_calories / caloriesPerGram; 
										int weightGramsperDay_val = (int)weightGramsperDay;
									
										asyncService.updateReqWeightByProfId( petProfile.getId(), weightGramsperDay_val);
									}
								}else {
									asyncService.updateReqCalories( Long.valueOf(jpetprofiles.get(0).getGateway_id()), MER);
								}
								
							
							
							double kcal = (int)MER; 
							String content_1 = "Your Pet's Daily Calorie Requirements";
							String content_2 = "The daily calorie requirement is calculated by the \"Vet Calculator\", using weight and health factors to determine your pet's energy needs.";
							String content_3 = "Suggested Daily Energy Intake: \n Approx. "+(int)MER+" kcal";
							response.put("kcal", kcal);
							response.put("content_1", content_1);
							response.put("content_2", content_2);
							response.put("content_3", content_3);
							
							response.put("Status", 1);
							response.put("Msg", "success");
							response.put("Msg", "Profile saved Successfully");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Error, Not saved");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "Invalid company type");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Empty input profile list");
				}

			} catch (DataIntegrityViolationException e) {
				log.error("saveorupdatePetProfile::::" + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "DataIntegrityViolationException");
				response.put("Error", e.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
				response.put("Msg", "UnExcepted Error in pet profile");
				response.put("Error", e.getLocalizedMessage());

			}
			log.info("Exit saveorupdatepetprofile");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		/** <AUTHOR> */
		@GetMapping(value = "v5.0/listpetprofile", headers = "Accept=application/json")
		@ResponseBody
		public JResponse listPetProfile(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering listpetprofile : " + autho);

			try {
				UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
				if (usr == null) {
					response.put("Status", 0);
					response.put("Msg", "invalid authentication key");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				List<JPetprofileFlutter> profilelist = petSpeciesServices.getJPetprofilesForFlutter(usr.getId(), "1,2");

				response.put("Msg", "Success");
				response.put("Status", 1);
				response.put("profilelist", profilelist);

			} catch (Exception ex) {
				response.put("Status", 0);
				response.put("Msg", "Unknown error, Please try after sometime");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : listpetprofile : " + ex.getLocalizedMessage());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		/** <AUTHOR> */
		@PostMapping(value = "v5.0/assignpetprofile", headers = "Accept=application/json")
		@ResponseBody
		public JResponse assignPetProfile(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver,
				@RequestParam(value = "gateway_id", defaultValue = "", required = false) long gateway_id,
				@RequestParam(value = "profile_id", defaultValue = "", required = false) long profile_id,
				@RequestParam(value = "pet_name", defaultValue = "", required = false) String pet_name,
				@RequestParam long monitor_type_id) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering assignpetprofile : " + autho);

			try {
				UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
				if (usr == null) {
					response.put("Status", 0);
					response.put("Msg", "invalid authentication key");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				PetProfile petProfile = gatewayService.getPetprofile( profile_id , 0L, null, null, null, null);
				if( petProfile != null && petProfile.getGateway().giveAsset().getModel().getMonitor_type().getId() == monitor_type_id ) {
					log.info("Same monitor type. failed to assign pet profile");
					response.put("Status", 0);
					response.put("Msg", "Failed to assign pet profile");
					return response;
				}
				
				boolean issuccess = gatewayService.updateGatewayName(petProfile.getName(),gateway_id+"");
				
				gatewayServiceV4.updateGatewayProfile(gateway_id, profile_id);
				Gateway gateway = gatewayService.getGatewayByid(gateway_id);
				String deviceType = gateway.getModel().getMonitor_type().getDisplay_name();
				if ( gateway.getModel().getMonitor_type().getId() == 4 && gateway.getModel().isTemp_alert()) {
					deviceType = "Waggle Cam Ultra";
				}

				response.put("Msg", pet_name + " is successfully connected with " + deviceType);
				response.put("Status", 1);

				boolean is_sub_activated = false;
				boolean is_sub_avail = false;
				AssetModel assetModel = gatewayService.getAssetModelByGatewayId( gateway_id );
				if( assetModel != null && assetModel.getMonitor_type().getId() == 4 && assetModel.isTemp_alert()) {
					//is_temp_alert enable means it is wagglecam ultra device
					is_sub_avail = true;
				}
				JGatewayFeature gatewayFeature = crService.getGatewayFeatureById( gateway_id );
				if( gatewayFeature.getGateway_id() == gateway_id ) {
					is_sub_activated = true;
				}
				response.put("is_sub_activated", is_sub_activated);
				response.put("is_sub_avail", is_sub_avail);
				
			} catch (Exception ex) {
				response.put("Status", 0);
				response.put("Msg", "Unknown error, Please try after sometime");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : assignpetprofile : " + ex.getLocalizedMessage());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		@PostMapping(value = "v5.0/registerdevice", headers = "Accept=application/json")
		@ResponseBody
		public JResponse registerDevice(@RequestBody JRegisterDevice registerDevice,@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver) {
			return registerDeviceService.registerDevice(registerDevice);
		}
		
		@RequestMapping(value = "v5.0/updateusertimezone", method = RequestMethod.GET, headers = "Accept=application/json")
		public @ResponseBody JResponse updateUserTimezone(@RequestParam("timezone") String timezone,
				Authentication authentication, @RequestHeader HttpHeaders header,@RequestParam(value = "userid", defaultValue = "NA", required = false) String userid,
				@RequestParam(value = "os", defaultValue = "", required = false) String os,
				@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
			String autho = header.getFirst("auth");
			log.info("Entered into updateUserTimezone : autho : " + autho);
			log.info("user id : " + userid);
			JResponse response = new JResponse();
			try {
				timezone = timezone.replaceAll("\\s+", "");
				if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
					timezone = "+" + timezone;
				UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
				userServiceV4.updateUserTimezone(user.getId(), timezone);
				log.info("User timezone updated");
				response.put("Status", 1);
				response.put("MSg", "Success");
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth key exception : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} catch (Exception e) {
				log.error("Error occured at updateUserTimezone : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "error occured");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		@PostMapping(value = "v5.0/saveorupdatepetprofileV2", headers = "Accept=application/json")
		@ResponseBody
		public JResponse saveOrUpdatePetProfileV6(@RequestBody List<JPetprofile> jpetprofiles,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering saveorupdatePetProfile : " + autho +" params : "+jpetprofiles.get(0).toString() );
			try {
				if (jpetprofiles != null) {
					Map<String, String> map = new HashMap<String, String>();
					try {
						map = userServiceV4.getUserId_cmpIdByAuthV2(autho);

						JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
						if (errResponse != null) {
							return errResponse;
						}

					} catch (InvalidAuthoException ex) {
						response.put("Status", 0);
						response.put("Msg", "invalid authkey");
						response.put("Error", ex.getLocalizedMessage());
						log.error("Exception while getting user for authkey : " + autho);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}

					long userId = Long.valueOf(map.get("user_id"));
					long cmpType_id = Long.valueOf(map.get("cmpType_id"));
					
					if (cmpType_id == 3) {
						int status = 0;
						for (JPetprofile jpetprofile : jpetprofiles) {
							jpetprofile.setHeight(jpetprofile.getHeight().isEmpty() ? "0" : jpetprofile.getHeight());
							jpetprofile.setWeight(jpetprofile.getWeight().isEmpty() ? "0" : jpetprofile.getWeight());

							String gateway_name = jpetprofile.getName();
							Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
							Matcher hasSpecial = special.matcher(gateway_name);
							if (hasSpecial.find()) {
								log.info("Gateway name contains special chars");
								response.put("Status", 0);
								response.put("Msg", RegisterUserError.ER009);
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

							List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, 
									userId, null);
							for (JGateway jGateway : gateways) {
								if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
									if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName()) && jpetprofile.getMonitortype() == jGateway.getMonitorTypeId()) {
										response.put("Status", 0);
										response.put("Msg", RegisterUserError.petNameUserMessage);
										response.put("Return Time", System.currentTimeMillis());
										return response;
									}
								}
							}

							if (jpetprofile != null) {
								if (!jpetprofile.getGateway_id().isEmpty() && (jpetprofile.getAgeYr() > 0 || jpetprofile.getAgeMonth() > 0)
										&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
										&& !jpetprofile.getSex().isEmpty()) {

									int month = (jpetprofile.getAgeYr() * 12)
											+ jpetprofile.getAgeMonth();
									
									Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
									SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
									String date = sf.format(birth_date);
									
									/*
									 * Date birth_date = gatewayService.getBirthDate("YEAR", jpetprofile.getAge());
									 * SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd"); String date =
									 * sf.format(birth_date);
									 */

									PetSpecies species = petSpeciesServices
											.getPetSpeciesByName(jpetprofile.getSpecieName());

									if (species == null) {
										species = petSpeciesServices.getPetSpecies( jpetprofile.getSpeciesid() );
									}
									
									if (jpetprofile.getId() == 0) { 
										PetProfile petProfile = gatewayService.getPetProfile(Long.valueOf(jpetprofile.getGateway_id()));
										if( petProfile != null ) {
											jpetprofile.setId(petProfile.getId());
										}
									}
									
									status = gatewayServiceV4.saveorupdatePetprofileV6(jpetprofile, userId, 
											date, species.getId());

								} else {
									log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

									response.put("Status", 0);
									response.put("Msg",
											"Enter all the mandatory fields like gatewayid,name,age,sex and breed");
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}
							}
						}

						log.info("Enter Into Gateway");
						if (status == 1) {							
							for (JPetprofile jpetprofile : jpetprofiles) {
								long gatewayId = Long.valueOf(jpetprofile.getGateway_id());
								long profileId = jpetprofile.getId();
								if (profileId == 0) { 
									PetProfile petProfile = gatewayService.getPetProfile(gatewayId);
									if( petProfile != null ) {
										profileId = petProfile.getId();
									}
									
//									profileId = gatewayServiceV4.getGatewayProfile(gatewayId);
									
									status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
//									boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
//											jpetprofile.getGateway_id());
									boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),
											profileId);
									log.info("GatewayName updated with respect to petname");
								}
								else {
									status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
//									boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
//											jpetprofile.getGateway_id());
									
									boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),
											profileId);
									
									log.info("GatewayName updated with respect to petname");
								}

								log.info("GatewayName and profileId updated with respect to petname");

							}
							
							double MER = 0;
							
								double weight = Double.parseDouble( jpetprofiles.get(0).getWeight() );
								double weightPounds = weight * 0.453592;
								double RER = 70 * Math.pow(weightPounds, 0.75);
								double signalment = petSpeciesServicesv4.getSignalment( jpetprofiles.get(0).getSpeciesid() , jpetprofiles.get(0).isIntact());		
								double BCS = petSpeciesServicesv4.getBCS( jpetprofiles.get(0).getStructure() != null && jpetprofiles.get(0).getStructure() != "" ? jpetprofiles.get(0).getStructure() : "ideal");							
								String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.get(0).getActivitylevel());
								double activityLevel = petSpeciesServicesv4.getActivityLevel( ActivityLel);
								MER = RER * signalment * activityLevel * BCS;
								
								PetProfile petProfile = gatewayService.getPetProfile(Long.valueOf(jpetprofiles.get(0).getGateway_id()));
								if( petProfile != null ) {
									asyncService.updateReqCaloriesById( petProfile.getId(), MER);
									
									double tot_calories = MER;
									double foodCal = gatewayService.getPetFoodCaloriesByProfileId(petProfile.getId());
									
									if(foodCal > 0) {
										double caloriesPerGram= foodCal/1000;
										double weightGramsperDay = tot_calories / caloriesPerGram; 
										int weightGramsperDay_val = (int)weightGramsperDay;
									
										asyncService.updateReqWeightByProfId( petProfile.getId(), weightGramsperDay_val);
									}
								}else {
									asyncService.updateReqCalories( Long.valueOf(jpetprofiles.get(0).getGateway_id()), MER);
								}
								
							
							
							double kcal = (int)MER; 
							String content_1 = "Your Pet's Daily Calorie Requirements";
							String content_2 = "The daily calorie requirement is calculated by the \"Vet Calculator\", using weight and health factors to determine your pet's energy needs.";
							String content_3 = " \n Approx. "+(int)MER+" kcal";
							response.put("kcal", kcal);
							response.put("content_1", content_1);
							response.put("content_2", content_2);
							response.put("content_3", content_3);
							
							boolean is_sub_activated = false;
							boolean is_sub_avail = false;
							AssetModel assetModel = gatewayService.getAssetModelByGatewayId( Long.valueOf(jpetprofiles.get(0).getGateway_id()) );
							if( assetModel != null && assetModel.getMonitor_type().getId() == 4 && assetModel.isTemp_alert()) {
								//is_temp_alert enable means it is wagglecam ultra device
								is_sub_avail = true;
							}
							JGatewayFeature gatewayFeature = crService.getGatewayFeatureById( Long.valueOf(jpetprofiles.get(0).getGateway_id()) );
							if( gatewayFeature.getGateway_id() == Long.valueOf(jpetprofiles.get(0).getGateway_id()) ) {
								is_sub_activated = true;
							}
							response.put("is_sub_activated", is_sub_activated);
							response.put("is_sub_avail", is_sub_avail);
							response.put("Status", 1);
							response.put("Msg", "success");
							response.put("Msg", "Profile saved Successfully");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Error, Not saved");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "Invalid company type");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Empty input profile list");
				}

			} catch (DataIntegrityViolationException e) {
				log.error("saveorupdatePetProfile::::" + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "DataIntegrityViolationException");
				response.put("Error", e.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
				response.put("Msg", "UnExcepted Error in pet profile");
				response.put("Error", e.getLocalizedMessage());

			}
			log.info("Exit saveorupdatepetprofile");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		@RequestMapping(value = "v5.0/updateaddon", method = RequestMethod.GET, headers = "Accept=application/json")
		public @ResponseBody JResponse updateAddon(@RequestParam("gateway_id") long gateway_id,
				Authentication authentication, @RequestHeader HttpHeaders header,@RequestParam(value = "isAddon", defaultValue = "true") boolean isAddon,
				@RequestParam(value = "os") String os,
				@RequestParam(value = "app_ver") String app_ver) {
			String autho = header.getFirst("auth");
			log.info("Entered into updateaddon : autho : " + autho);
			JResponse response = new JResponse();
			try {
				
				UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
				userServiceV4.updateAddonTomorrowStatus(gateway_id, isAddon);
				log.info("Add on tomorrow updated");
				response.put("Status", 1);
				response.put("MSg", "Success");
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth key exception : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} catch (Exception e) {
				log.error("Error occured at updateaddon : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "error occured");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		@RequestMapping(value = "v5.0/updatedevicenotification", method = RequestMethod.GET, headers = "Accept=application/json")
		public @ResponseBody JResponse updateDeviceNotification(@RequestParam("gateway_id") long gateway_id,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam(value = "notification", defaultValue = "true") boolean notification,
				@RequestParam(value = "os") String os,
				@RequestParam(value = "pb_habit_notification", defaultValue = "false", required = false) boolean habit_notification,
				@RequestParam(value = "app_ver") String app_ver) {
			String autho = header.getFirst("auth");
			log.info("Entered into updatedevicenotification : autho : " + autho);
			JResponse response = new JResponse();
			try {
				
				UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
				userServiceV4.updateDeviceNotoficationStatus(gateway_id, notification,user.getId(),habit_notification);
				log.info("devicenotification updated");
				response.put("Status", 1);
				response.put("MSg", "Success");
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth key exception : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} catch (Exception e) {
				log.error("Error occured at updatedevicenotification : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "error occured");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		
		@GetMapping(value = "v5.0/productcategory")
		@ResponseBody
		public JResponse getProductCategory(@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
				Authentication authentication, @RequestHeader HttpHeaders header) {
			log.info("Entered into getProductCategory :: auth : "+header.getFirst("auth"));
			JResponse response = new JResponse();
			try {
				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV3("authkey", header.getFirst("auth"));
					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException e) {
					log.error("Invalid Authkey :" + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Session, Please try again");
					return response;
				}

				ArrayList<JCategory> categorie_list = commonService.getProductCategory();

				categorie_list.forEach(category ->
					{
						if(category.getCategory_name().equals("Pet Monitor")){
							ArrayList<JProduct> petMonitorList=category.getProduct_list();
							if (petMonitorList != null && petMonitorList.size() > 1) {
								ArrayList<JProduct> trimmedList = new ArrayList<>();
								trimmedList.add(petMonitorList.get(0)); 
								category.setProduct_list(trimmedList);
							}
						}
					}
				);
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("category_list", categorie_list);
				
			} catch (Exception e) {
				log.error("Error in getProductCategory :: Error : "+e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
			}

			return response;
		}
		
		@PostMapping(value = "v5.0/updatesensorconfig", headers = "Accept=application/json")
		@ResponseBody
		public JResponse updateSensorlocation(Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
				@RequestBody NodeSensorSetting nodeSensor) {
			JResponse response = new JResponse();
			log.info("Entered into updatesensorlocation :: gateway_id : " + nodeSensor.getGateway_id()
					+ " :: cam_config : " + nodeSensor.toString());
			try {
				String auth = header.getFirst("auth");
				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException e) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Session, Please try again");
					log.error("Invalid authkey : " + auth);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				boolean updated_status = false;

				Gateway gateway = gatewayService.getGatewayByid(nodeSensor.getGateway_id());

				if (gateway == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Session, Please try again");
					return response;
				}

				switch (nodeSensor.getUpdated_for()) {
					case "gatewayname": {
						// updating sensor location and device name
						String slocationId = nodeSensor.getUpdated_value1();
						String gname = nodeSensor.getUpdated_value2();
						boolean isexist = gatewayServiceV4.getGnameExist(gname, nodeSensor.getGateway_id(),gateway.getCompany().getId());
						
						if (!isexist)
							updated_status = userServiceV4.updateSensorlocation(nodeSensor.getGateway_id(),
									Long.valueOf(slocationId), gname);
						else {
							response.put("Status", 0);
							response.put("Msg", "Gateway name already exists");
							return response;
						}
	
						if (updated_status) {
							response.put("Status", 1);
							response.put("Msg", "Success");
							return response;
						} else {
							response.put("Status", 0);
							response.put("Msg", "Failed to update sensor config");
							return response;
						}
					}
				}

			} catch (Exception e) {
				log.error("Error in updatesensorlocation :: Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				return response;
			}
			return response;
		}
		
		/** <AUTHOR> */
		@PostMapping(value = "v5.0/saveuserpetprofile", headers = "Accept=application/json")
		@ResponseBody
		public JResponse saveUserPetProfile(@RequestBody JPetprofileFlutter jpetprofile,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				Authentication authentication, @RequestHeader HttpHeaders header,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering saveorupdatePetProfile : " + autho);
			try {
				if (jpetprofile != null) {
					Map<String, String> map = new HashMap<String, String>();
					try {
						map = userServiceV4.getUserId_cmpIdByAuthV2(autho);

						JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
						if (errResponse != null) {
							return errResponse;
						}

					} catch (InvalidAuthoException ex) {
						response.put("Status", 0);
						response.put("Msg", "invalid authkey");
						response.put("Error", ex.getLocalizedMessage());
						log.error("Exception while getting user for authkey : " + autho);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}

					long userId = Long.valueOf(map.get("user_id"));
					long cmpType_id = Long.valueOf(map.get("cmpType_id"));
					
					if (cmpType_id == 3) {
						int generatedPetProfileId = 0;
							String gateway_name = jpetprofile.getName();
							Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
							Matcher hasSpecial = special.matcher(gateway_name);
							if (hasSpecial.find()) {
								log.info("Gateway name contains special chars");
								response.put("Status", 0);
								response.put("Msg", RegisterUserError.ER009);
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

							if (jpetprofile != null) {
								if ((Double.parseDouble(jpetprofile.getAgeYr()) > 0 || Double.parseDouble(jpetprofile.getAgeMonth()) > 0)
										&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
										&& !jpetprofile.getSex().isEmpty()) {

									int month = (int) ((int) (Double.parseDouble(jpetprofile.getAgeYr()) * 12)
											+ Double.parseDouble(jpetprofile.getAgeMonth()));
									
									Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
									SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
									String date = sf.format(birth_date);

									PetSpecies species = petSpeciesServices
											.getPetSpeciesByName(jpetprofile.getSpecieName());

									if (species == null) {
										species = petSpeciesServices.getPetSpecies( jpetprofile.getSpeciesid() );
									}

									jpetprofile.setUser_id(userId);
									jpetprofile.setSpecieName(species.getSpeciesName());
									generatedPetProfileId = gatewayServiceV4.saveorupdatePetprofileV7(jpetprofile, userId, 
											date, species.getId());
								} else {
									log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

									response.put("Status", 0);
									response.put("Msg",
											"Enter all the mandatory fields like name,age,sex and breed");
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}
							}

						log.info("Enter Into Gateway");
						if (generatedPetProfileId > 0) {

							double MER = 0;

							double weight = jpetprofile.getWeight();
							double weightPounds = weight * 0.453592;
							double RER = 70 * Math.pow(weightPounds, 0.75);
							double signalment = petSpeciesServicesv4.getSignalment( jpetprofile.getSpeciesid() , jpetprofile.isIntact());		
							double BCS = petSpeciesServicesv4.getBCS( jpetprofile.getStructure() != null && jpetprofile.getStructure() != "" ? jpetprofile.getStructure() : "ideal");							
							String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofile.getActivitylevel());
							double activityLevel = petSpeciesServicesv4.getActivityLevel( ActivityLel);
							MER = RER * signalment * activityLevel * BCS;

							double kcal = (int)MER; 
							String content_1 = "Your Pet's Daily Calorie Requirements";
							String content_2 = "The daily calorie requirement is calculated by the \"Vet Calculator\", using weight and health factors to determine your pet's energy needs.";
							String content_3 = " \n Approx. "+(int)MER+" kcal";
							response.put("kcal", kcal);
							response.put("content_1", content_1);
							response.put("content_2", content_2);
							response.put("content_3", content_3);

							response.put("Status", 1);
							response.put("Msg", "Profile saved Successfully");
							jpetprofile.setId(generatedPetProfileId);
							if (jpetprofile.getGatewayId() > 0) {
								gatewayServiceV4.saveOrUpdateGatewayProfile(jpetprofile.getGatewayId(), generatedPetProfileId);
							}
						} else {
							response.put("Status", 0);
							response.put("Msg", "Error, Not saved");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "Invalid company type");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Empty input profile list");
				}

			} catch (DataIntegrityViolationException e) {
				log.error("saveorupdatePetProfile::::" + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "DataIntegrityViolationException");
				response.put("Error", e.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
				response.put("Msg", "UnExcepted Error in pet profile");
				response.put("Error", e.getLocalizedMessage());

			}
			log.info("Exit saveorupdatepetprofile");
			response.put("pet_profile", jpetprofile);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	/**
	 * <AUTHOR>
	 * Retrieves the user's pet profile information.
	 *
	 * @param stTime An optional parameter representing the start time (default is empty string)
	 * @param authentication The Authentication object for user authentication
	 * @param header The HttpHeaders object containing request headers
	 * @param os The operating system of the client device
	 * @param app_ver The version of the application making the request
	 * @return JResponse A custom response object containing the pet profile data,
	 *         status of the request, and any relevant messages or errors
	 */
	@GetMapping(value = "v5.0/userpetprofile", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserPetProfile(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
										Authentication authentication, @RequestHeader HttpHeaders header,
										@RequestParam("os") String os,
										@RequestParam("app_ver") String app_ver) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering getUserPetProfile : {}", auth);
		try {
			Map<String, String> map = new HashMap<>();
			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : {}", auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			long userId = Long.parseLong(map.get("user_id"));

			List<JPetprofileFlutter> petprofileList = gatewayServiceV4.getPetprofileList(userId);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("pet_profile_list", petprofileList);
		}
		catch (Exception e) {
			response.put("Status", 0);
			log.error("getUserPetProfile:::: " + e.getLocalizedMessage());
			response.put("Msg", "UnExcepted Error in get user pet profile");
			response.put("Error", e.getLocalizedMessage());
			response.put("user_pet_profile_list", new ArrayList<>());
		}

		return response;
	}

	//Save 4G Cam Alert Notifications
	@RequestMapping(value = "v4.0/save4gAlertNotifications", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse save4gAlertNotifications(@RequestParam(value = "os", defaultValue = "", required = false) String os,
											  @RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
											  Authentication authentication, @RequestHeader HttpHeaders header,
											  @RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
											  @RequestBody List<MeariNotification> notifications) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		log.info("Entering save4gAlertNotifications : " + autho);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = user.getId();

            if (meariNotificationService.saveNotifications(userId, notifications)) {
                response.put("Status", 1);
				response.put("Msg", "Success");
            } else {
                response.put("Status", 0);
                response.put("Msg", "Failed to save notifications");
            }

            List<MeariNotification> meariNotificationHistory = meariNotificationService.getNotifications(userId);
            response.put("alerts", meariNotificationHistory);
            return response;
		}
		catch (Exception e) {
			log.error("Error in save4gAlertNotifications :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
            response.put("alerts", new ArrayList<>());
			return response;
		}
	}

	//Get 4G alert notifications
	@RequestMapping(value = "v4.0/get4gAlertNotifications", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse get4gAlertNotifications(@RequestParam(value = "os", defaultValue = "", required = false) String os,
											  @RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
											  Authentication authentication, @RequestHeader HttpHeaders header,
											  @RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		log.info("Entering get4gAlertNotifications : " + autho);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			int userId = (int) user.getId();

			List<MeariNotification> meariNotificationHistory = meariNotificationService.getNotifications(userId);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alerts", meariNotificationHistory);

			return response;
		}
		catch (Exception e) {
			log.error("Error in get4gAlertNotifications :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
			return response;
		}
	}

	/**
	 * Saves the user's device spot.
	 *
	 * @param os             The operating system of the device (optional).
	 * @param app_ver        The application version (optional).
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @param stTime         The start time (optional).
	 * @param gatewayId      The gateway ID (required).
	 * @param devicePlaceType The name of the place type (required).
	 * @param devicePlace     The name of the device place (required).
	 * @return               A response containing the status and message of the operation.
	 */
	@PostMapping(value = "v5.0/saveuserdevicespot", headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveUserDeviceSpot(@RequestParam(value = "os", defaultValue = "", required = false) String os,
										@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
										Authentication authentication, @RequestHeader HttpHeaders header,
										@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
										@RequestParam(value = "gatewayid") String gatewayId,
										@RequestParam(value = "deviceplacetype") String devicePlaceType,
										@RequestParam(value = "deviceplace") String devicePlace) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering get4gAlertNotifications : {}", auth);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = user.getId();
			boolean result = gatewayServiceV4.saveUserDeviceSpot(userId, gatewayId, devicePlaceType, devicePlace);

			if(!result) {
				response.put("Status", 1);
				response.put("Msg", "Failed to save");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in saveUserDeviceSpot :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred when saving the device spot");
		}

		return response;
	}

	/**
	 * Retrieves device spots for the authenticated user.
	 *
	 * @param os             The operating system of the user's device (optional).
	 * @param app_ver        The application version (optional).
	 * @param authentication The authentication object to validate user credentials.
	 * @param header         HTTP headers containing the "auth" key for authentication.
	 * @param stTime         The start time (optional).
	 * @return               returns the response.
	 */
	@GetMapping(value = "v5.0/userdevicespot", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserDeviceSpot(@RequestParam(value = "os", defaultValue = "", required = false) String os,
										@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
										Authentication authentication, @RequestHeader HttpHeaders header,
										@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering get user device spot : {}", auth);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<Map<String, Object>> result = gatewayServiceV4.getUserDeviceSpot();

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("device_spots", result);
		} catch (Exception e) {
			log.error("Error in getUserDeviceSpot :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred when getting the device spot");
		}

		return response;
	}

	/**
	 * Retrieves FOTA (Firmware Over The Air) update information for a specific gateway.
	 *
	 * @param gatewayId      The ID of the gateway for which FOTA update information is requested.
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @return               A response containing the FOTA update information.
	 */
	@GetMapping(value = "v5.0/fotaupdate", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> getFotaUpdate(@RequestParam(value = "gatewayid") String gatewayId, Authentication authentication,
												   @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getFotaUpdate: {}", auth);

		try {
			UserV4 user;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				log.warn("Invalid authentication key: {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key: " + e.getLocalizedMessage());
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
			}

			JResponse validationResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (validationResponse != null) {
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(validationResponse);
			}

			FotaUpdate result = gatewayServiceV4.getFotaUpdate(gatewayId);
			response.put("show_bluetooth_update", false);
			response.put("Status", 1);

			if (result != null) {
				if (!result.getFileName().equalsIgnoreCase("NA")) {
					result.setFileName(fotaUpdateFileUrl.trim() + result.getFileName().trim());
				}
				response.put("fota_update", result);
				response.put("Msg", "Success");

				if (result.isBleUpdateStatus() && !"NA".equalsIgnoreCase(result.getFileName())) {
					response.put("show_bluetooth_update", true);
				}
			} else {
				response.put("fota_update", null);
				response.put("Msg", "No fota update found");
			}

			return ResponseEntity.ok(response);

		} catch (Exception e) {
			log.error("Error in getFotaUpdate: {}", e.getLocalizedMessage(), e);
			response.put("Status", 0);
			response.put("fota_update", null);
			response.put("Msg", "Error occurred when getting fota update");
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}

	/**
	 * Updates Latest  FOTA (Firmware Over The Air) version number .
	 *
	 * @param gatewayId      The ID of the gateway for which FOTA update information is requested.
	 * @param updatedFotaVersionNumber    The latest version for which the updatation is required.
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @return               A response containing the FOTA update information.
	 */
	@PostMapping(value = "v5.0/fotaupdatelatestversion", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> updateLatestFotaVersion(@RequestParam(value = "gatewayid") String gatewayId,@RequestParam(value="updatedfotaversion") String updatedFotaVersionNumber, Authentication authentication,
												   @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering  updateLatestFotaVersion : {}", auth);

		try {
			UserV4 user;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				log.warn("Invalid authentication key: {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key: " + e.getLocalizedMessage());
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
			}

			JResponse validationResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (validationResponse != null) {
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(validationResponse);
			}

			if(updatedFotaVersionNumber.isEmpty()){
				response.put("Status", 0);
				response.put("Msg", "Updated Fota version cannot be empty .");
				return ResponseEntity.ok(response);
			}

			boolean isLatestFotaVersionUpdated = gatewayServiceV4.updateLatestFotaVersion(gatewayId,updatedFotaVersionNumber);
			if(isLatestFotaVersionUpdated){
				response.put("Status", 1);
				response.put("Msg", "Updated latest Fota version number succcessfully .");
			}
			else{
				response.put("Status", 0);
				response.put("Msg", "Unable to update Fota version number .");
			}
			return ResponseEntity.ok(response);

		} catch (Exception e) {
			log.error("Error in updateLatestFotaVersion: {}", e.getLocalizedMessage(), e);
			response.put("Status", 0);
			response.put("Msg", "Error occurred while updating latest fota version number");
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}

	/**
	 * Saves the user's shipping address after plan purchase.
	 *
	 * @param os             The operating system of the device (optional).
	 * @param app_ver        The application version (optional).
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @return               A response containing the status and message of the operation.
	 */
	@PostMapping(value = "v5.0/saveminicamshippingaddr", headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveMinicamShippingaddr(@RequestBody Minicamshipping jminicamshipping, @RequestParam(value = "os", defaultValue = "", required = false) String os,
											 @RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
											 Authentication authentication, @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering saveMinicamShippingaddr : ", auth);

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			boolean isCancellationFlow=jminicamshipping.getIsCancellation();
			jminicamshipping.setUser_id(user.getId());
			boolean result = gatewayServiceV4.saveMinicamshippingaddres(jminicamshipping);

			if(!result) {
				response.put("Status", 1);
				response.put("Msg", "Already saved");
				return response;
			}

			try {
				Properties prop = new Properties();
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));

				String to_address = prop.getProperty("to_address");
				String cc_address = prop.getProperty("cc_address");
				String bcc_address = prop.getProperty("bcc_address");

				String mailSub="";
				String mailContent="";
				if(!isCancellationFlow) {
					mailSub = "Reg Free Mini Cam Shipment Address : " + user.getUsername();
					mailContent = "<p>Hi Team,</p>" + "<p>Find the user for free mini cam shipment</p>";
				}
				else if(isCancellationFlow && jminicamshipping.getMonitortypeId() == 1){
					mailSub = "Retention Offer – N12.5 Upgrade Accepted  " ;
					mailContent = "<p>Hi Team,</p>" + "<p>Find the user for Upgrade N12.5 shipment</p>";
					if(jminicamshipping.getSubId() != null &&  !jminicamshipping.getSubId().trim().equalsIgnoreCase("") && !jminicamshipping.getSubId().equalsIgnoreCase("NA")) {
						try {
							Environment.configure(chargebeeSiteName, chargebeeSiteKey);

							Result result1 = Subscription.retrieve(jminicamshipping.getSubId()).request();

							Subscription subscription = result1.subscription();

							long currentNextBilling = subscription.nextBillingAt().getTime() / 1000;

							// 2. Convert to Date and add 2 months
							Calendar cal = Calendar.getInstance();
							cal.setTime(new Date(currentNextBilling * 1000));
							cal.add(Calendar.MONTH, 2);
							long newNextBilling = cal.getTimeInMillis() / 1000;
							Timestamp ts = new Timestamp(newNextBilling * 1000);

							Result result2 = subscription.changeTermEnd(subscription.id())
									.termEndsAt(ts)
									.request();

							log.info("Subscription next billing date updated successfully for ID: " + subscription.id());
							log.info("New Next Billing Date: " + subscription.currentTermEnd());
						}catch (Exception e){
							log.error("Error in date extention:" + e.getMessage());
						}
					}
				}else if(isCancellationFlow && jminicamshipping.getMonitortypeId() == 12){
					mailSub = "Retention Offer – 4G Mini cam Upgrade Accepted  " ;
					mailContent = "<p>Hi Team,</p>" + "<p>Find the user for Upgrade 4G Mini cam shipment</p>";
				}
				mailContent += "<p>Email          : " + user.getEmail() + "</p>";
				mailContent += "<p>Chargebee Id   : " + user.getChargebeeid() + "</p>";
				mailContent += "<p>Address 1      : " + jminicamshipping.getAddress1() + "</p>";
				mailContent += "<p>Address 2      : " + jminicamshipping.getAddress2() + "</p>";
				mailContent += "<p>Zip Code       : " + jminicamshipping.getZipcode() + "</p>";
				mailContent += "<p>city           : " + jminicamshipping.getCity() + "</p>";
				mailContent += "<p>state          : " + jminicamshipping.getState() + "</p>";

				mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
				async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
			} catch (Exception e) {
				log.error("Error in email for free mini cam:" + e.getMessage());
			}

			response.put("Status", 1);
			if(isCancellationFlow){
				response.put("Msg", "Thanks! Your free upgrade is on the way.");
			}
			else{
			response.put("Msg", "Success");
			}
		} catch (Exception e) {
			log.error("Error in saveMinicamShippingaddr :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occurred when saving the mini cam shipping address");
		}

		return response;
	}
	
	/**
	 * Tigger N12fotaupdate from api .
	 *
	 * @param gateway_id   gateway ID.
	 * @param req_for    req_for 1 : get fota details 2: tigger fota upgrade
	 * @param authentication The authentication object for user validation.
	 * @param header         The HTTP headers containing the "auth" key.
	 * @return               A response containing the FOTA status information.
	 */
	@PostMapping(value = "v5.0/N12fotaupdate", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> N12fotaupdate(@RequestParam(value = "gateway_id") Long gateway_id,
			@RequestParam(value="req_for") int req_for,@RequestParam(value="fotaLatestVersion",defaultValue = "NA",required = false) String fotaLatestVersion,
			Authentication authentication, @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering  N12fotaupdate: ", auth);

		try {
			UserV4 user;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				log.warn("Invalid authkey: ", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key: " + e.getLocalizedMessage());
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
			}

			JResponse validationResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (validationResponse != null) {
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(validationResponse);
			}
			/* req_for 1 : get fota details
			 * 		   2: tigger fota upgrade
			 * 
			 */
			boolean isValidCommand= (!fotacommand.equals("") && fotacommand!=null)?true:false;

			if(!isN12_5_fotacommandUpdateNeeded){
				response.put("Status", 1);
				response.put("fota_update", false);
				response.put("Msg", "Success");
				response.put("fotaLatestVersion", "");
				return ResponseEntity.ok(response);
			}
			if(req_for ==1 && gateway_id>0 && isValidCommand){

				if(fotacommand.equalsIgnoreCase("NA")){
					response.put("Status", 1);
					response.put("fota_update", false);
					response.put("Msg", "Success");
					response.put("fotaLatestVersion", "");

				}
				else if(fotacommand.contains("$")){
					response.put("Status", 1);
					String[] res = fotacommand.split("\\$");
					String fotaUpdateVersion = res[1];
					boolean isDynamicCmdInserted = messagingService.isDynamicCmdInserted(gateway_id, fotacommand);
					if (isDynamicCmdInserted) {

						boolean isN12fotaupdateAvailable = gatewayServiceV4.checkN12fotaupdateAvailable(fotaUpdateVersion, gateway_id);
						response.put("fota_update", isN12fotaupdateAvailable);
						if (isN12fotaupdateAvailable) {
							response.put("fotaLatestVersion", fotaUpdateVersion);
						}
						response.put("Msg", "Success");


					} else {

						response.put("Msg", "Update not available or Already Updated .");
						response.put("fota_update", false);
						response.put("fotaLatestVersion", fotaUpdateVersion);
					}
				}
				else{
					response.put("Status", 0);
					response.put("fota_update", false);
					response.put("fotaLatestVersion", "");
					response.put("Msg", "Unable to get commmand .");

				}

				return ResponseEntity.ok(response);
			}
			else if(req_for ==2 && gateway_id>0 && isValidCommand){

				response.put("fota_update", false);
				messagingService.saveMessageV2(gateway_id.toString(), fotacommand, 0);
				response.put("fotaLatestVersion", fotaLatestVersion);
				response.put("Msg", "Success");
				response.put("Status", 1);

				return ResponseEntity.ok(response);
			}
			else {
				response.put("Status", 0);
				response.put("fota_update", false);
				response.put("fotaLatestVersion", fotaLatestVersion);
				response.put("Msg", "Unable to update Fota version number .");
			}

			return ResponseEntity.ok(response);

		} catch (Exception e) {
			log.error("Error in N12fotaupdate: ", e.getLocalizedMessage(), e);
			response.put("Status", 0);
			response.put("Msg", "Error in N12fotaupdate");
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}

	@GetMapping(value = "v6.0/productcategory", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<JResponse> getProductList(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication, @RequestHeader HttpHeaders header) {
		log.info("Entered into getProductList :: auth : " + header.getFirst("auth"));
		JResponse response = new JResponse();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", header.getFirst("auth"));
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return new ResponseEntity<>(errResponse, HttpStatus.UNAUTHORIZED);
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
			}

			ArrayList<JProductWithSubCategory> product_list = commonService.getProductList();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("product_list", product_list);
			return ResponseEntity.ok(response);
		} catch (Exception e) {
			log.error("Error in getProductList :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getProductList");
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
		}
	}
}
