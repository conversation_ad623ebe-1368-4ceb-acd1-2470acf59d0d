package com.nimble.irisservices.appcontroller;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.CountryCode;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.AskFeature;
import com.nimble.irisservices.entity.CountryCodeV4;
import com.nimble.irisservices.entity.FreshChat;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.RVAnswer;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.EmailContent;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICommonService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IOptimizedV4Service;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReferAndEarnServiceV4;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import freemarker.template.Template;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.TimeZone;

@RestController
@RequestMapping("/app")
public class UserControllerV4App {

	private static final Logger log = LogManager.getLogger(UserControllerV4App.class);

	@Autowired
	Email email_helper;
	
	@Autowired
	IReportService reportService;

	@Autowired
	@Lazy
	IReportServiceV4 rptServicev4;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IOptimizedV4Service optimizedService;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IReferAndEarnServiceV4 referAndEarnServiceV4;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;

	@Value("${edit_country}")
	private boolean edit_country;
	
	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("${send_registeruseremail_simactivation_to_microservice}")
	private boolean sendActivateUserDataToSQS_Microservice;

	@Value("${amazonrateuscount}")
	private int amazonrateuscount;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	@Value("${walmartredirecturl}")
	private String walmartredirecturl;

	PrettyTime prettyTime = new PrettyTime();

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IUserService userService;

	@Autowired
	UserControllerApp userController;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${show_inapp_markettingpopup}")
	private boolean showInappMarketingPopup;

	@Value("${event_id}")
	private String eventId;

	@Value("${referralurl}")
	private String referralurl;

	@Value("${weblinkurl}")
	private String weblinkurl;

	@Value("${weblinkflag}")
	private String weblinkflag;

	@Autowired
	Helper _helper = new Helper();

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Value("${plivono}")
	private String plivoNumber;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	@Value("${delete_user_msg}")
	private String delete_user_msg = "NA";
	
	@Value("#{${delete_reason}}")
	private Map<String, String> delete_reason;
	
	@Value("${delete_img_url}")
	private String delete_img_url = "NA";
	
	@Value("${delete_img_url_dark}")
	private String delete_img_url_dark = "NA";
	
	@Value("${delete_user_confirmation}")
	private String delete_user_confirmation = "NA";
	
	@Value("${delete_email_to}")
	private String delete_email_to = "NA";
	
	@Value("${delete_email_cc}")
	private String delete_email_cc = "NA";
	
	@Autowired
	@Lazy
	IMessagingService  messagingService;
	
	@Autowired
	@Lazy
	ICommonService commonService;
	
	@Autowired
	freemarker.template.Configuration templates;
	
	@Value("${askfeatureto_address}")
	private String askfeatureto_address = "NA";
	
	@Value("${askfeaturecc_address}")
	private String askfeaturecc_address = "NA";

	private String delete_user_emailsub = "Waggle Account Deletion is in process";

	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Value("${priorityemail}")
	private String priorityemail;
	
	@Value("${priorityphone}")
	private String priorityphone;

	@Value("${referral_popup_image_url}")
	private String referralPopupImageUrl;

	@Value("${referral_popup_content1}")
	private String referralPopupContent1;

	@Value("${referral_popup_content2}")
	private String referralPopupContent2;

	@Value("${referral_popup_content3}")
	private String referralPopupContent3;

	@Value("${referral_popup_content4}")
	private String referralPopupContent4;

	@Value("${referral_popup_cta1}")
	private String referalPopupCta1;

	@Value("${referral_popup_cta2}")
	private String referalPopupCta2;

	@Value("${inapp_gif_url}")
	private String inappGifUrl;

	@Value("${inapp_navigation_url}")
	private String inappNavigationUrl;

	@Value("${usereferralcandy}")
	private boolean useReferralCandy;

	@Value("${referralcandyAccessKey}")
	private String referralCandyAccessKey;

	@Value("${referralcandysecretkey}")
	private String referralCandySecretKey;

	@Value("${config.oauth2.clientid.app}")
	private String clientidApp;

	@Value("${config.oauth2.clientsecret.app}")
	private String clientSecretApp;

	@Value("${plan_purchase_site_url}")
	private String plan_purchase_site_url;

	@Autowired
	Helper helper;

	@Autowired
	private IOAuth2Service oAuth2Service;

	@RequestMapping(value = "v5.0/getpromotionalnotification", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPromotionalNotifV5(@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header, @RequestParam("userid") long user_id) {
		String auth = header.getFirst("auth");
		log.info("Entering getPromotionalNotif : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			UserMetaData userNotif = userServiceV4.getUserMetaData(user_id);
			boolean showNotif = true;
			if (userNotif != null)
				showNotif = userNotif.isShow_marketing_notif();
			else {
				userNotif = new UserMetaData();
				userNotif.setShow_marketing_notif(showNotif);
				userNotif.setVpm_id("NA");
				userNotif.setUser_id(user_id);
				async.enableDisableMarketingNotification(userNotif);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Enable_Notification", showNotif);
		} catch (Exception e) {
			log.error("Exception in getCountryCode : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/countrycode", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCountryCodeV5(@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "req_from", defaultValue = "", required = false) String req_from,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		String auth = header.getFirst("auth");
		log.info("Entering getCountryCode : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			List<CountryCodeV4> countryCodeList = new ArrayList<CountryCodeV4>();
			countryCodeList = userServiceV4.getCountryCodeList(req_from);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CountryCode", countryCodeList);
		} catch (Exception e) {
			log.error("Exception in getCountryCode : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/pwdupdatev2", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdateV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestBody UpdatePassword udatePassword,
			Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into passwordUpdateV5 ");

		JResponse response = new JResponse();
		try {

			String autho = header.getFirst("auth");

			String password = _helper.base64Decoder(udatePassword.getPassword());
			if (password == null) {
				response.put("Status", 0);
				response.put("Msg", "Password not updated. Please try again later.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if (!validatePassword.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String userName = udatePassword.getUsername();

			UserV4 user = null;

			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			return commonService.passwordUpdateV4(user, udatePassword);

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + header.getFirst("auth"));
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
	}

	@RequestMapping(value = "v5.0/validateotp", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOneTimePasswordV5(@RequestHeader HttpHeaders header, @RequestParam long otp,
			@RequestParam(value = "username") String userName, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into validateOneTimePassword :: username :" + userName);
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("validateotp :" + auth);

		try {

			if (!validation_authkey.equals(auth)) {
				UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			}

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Exception while getting user for auth : " + auth);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
		
		try {
			response = commonService.validateOneTimePassword(auth, otp, userName, request_from);
		} catch (Exception e) {
			log.error(" Error in validateOneTimePassword " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/forgetpassword", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse oneTimePasswordV5(@RequestHeader HttpHeaders header,
			@RequestParam(value = "username") String userName,
			@RequestParam(value = "via", defaultValue = "mobileno") String via, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "mobile_number", defaultValue = "0", required = false) String mobileno) {
		log.info(" Entered into forgetpassword v4 :: username :" + userName);
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			log.info("forgetpassword auth :" + auth);

			try {

				if (!validation_authkey.equals(auth)) {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				}

			} catch (InvalidAuthoException ex) {

				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}

			response = commonService.oneTimePassword(userName, via, request_from,mobileno);
		} catch (Exception e) {
			log.error(" Error in forgetpassword v5 " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/setmarketingnotification", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enableDisableMarketingNotificationV5(
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam("enable") boolean enable, @RequestParam("userid") long user_id, Authentication authentication,
			@RequestHeader HttpHeaders header) {
		String auth = header.getFirst("auth");
		log.info("Entering enableDisableMarketingNotification : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			UserMetaData userNotif = userServiceV4.getUserMetaData(user_id);

			if (userNotif == null)
			{
				userNotif = new UserMetaData();
				userNotif.setVpm_id("NA");
			}
			
			userNotif.setUser_id(user_id);
			userNotif.setShow_marketing_notif(enable);

			boolean status = userServiceV4.enableDisableMarketingNotification(userNotif);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Updated Successfully!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update notification!");
			}

		} catch (Exception e) {
			log.error("Exception in enableDisableMarketingNotification : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occurred!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "v5.0/deleteuser", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUserV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestBody UserDelete userDlt, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into deleteUser...");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				long userId = user.getId();
				if(userDlt.getUser_id()>0)
					userId = userDlt.getUser_id();
				else
					userDlt.setUser_id(userId);
				
				boolean Status = userServiceV4.deleteUserById(userId);
				userDlt.setUpdated_date(_helper.getCurrentTimeinUTC());
				
				if (Status) {
					long id = userServiceV4.getUserDlt(userDlt.getUser_id());
					userDlt.setId(id);
					boolean deleteStatus = userServiceV4.saveOrUpdateUserDelete(userDlt);
					log.info("Update user_dlt : "+deleteStatus);
					
					/* Send to support team */
					async.SendEmail_SES(delete_email_to, delete_email_cc, "", delete_user_emailsub , EmailContent.deleteUserRequest(user));
					
					response.put("Status", 1);
					response.put("Msg", delete_user_confirmation);
					response.put("contact_msg", "Our support team will contact you within 1 business day to delete your account");
					
					String email = user.getEmail();
					if(email.equalsIgnoreCase("NA"))
						email = user.getUsername();
					
					Template template = (Template) templates.getTemplate("delete-processing.ftl");
					Map<String, String> deleteRequestParams = new HashMap<>();
					deleteRequestParams.put("firstname", user.getFirstname());
					
					ResponseEntity<String> deleteRequestContent = ResponseEntity
							.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, deleteRequestParams));
					String emailContent = deleteRequestContent.getBody();
					email_helper.SendEmail_SES(user.getEmail(), "",
							"", delete_user_emailsub, emailContent);
					
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to delete account");
				}
			} catch (Exception e) {
				log.error("Error occurred while delete user : "+e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Failure");
				response.put("Error", e.getMessage());
			}

		} catch (Exception e) {
			log.error("Error in deleteUser " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/updateamazonreview/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateAmazonReviewV5(@RequestBody AmazonUserReview auReview,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) long monitorType) {
		String autho = header.getFirst("auth");
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (usr != null) {
				boolean result = userServiceV4.updateAmazonReview(auReview,monitorType);

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Review updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "v5.0/getUserByUsernameV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsernameV5(@RequestParam("name") String name,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getUserByUsernameV5 : " + name);
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		String autho = header.getFirst("auth");
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", name);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("User", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV5 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/freshchat", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveFreshChatIdV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestBody FreshChat freshChat, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into saveFreshChat");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}

			FreshChat fchat = userServiceV4.getFreshChatByUserId(freshChat.getUser_id());
			boolean isFreshChatIdSaved = false;
			if (fchat == null) {
				isFreshChatIdSaved = userServiceV4.saveFreshChatId(freshChat);
			} else if (fchat.getFreshchat_id().equalsIgnoreCase(freshChat.getFreshchat_id())) {
				isFreshChatIdSaved = true;
			} else {
				long id = fchat.getId();
				freshChat.setId(id);
				isFreshChatIdSaved = userServiceV4.saveFreshChatId(freshChat);
			}

			if (isFreshChatIdSaved) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
			}

		} catch (Exception e) {
			log.info("user_id : " + freshChat.getUser_id());
			log.error("Error in saveFreshChat " + e.getLocalizedMessage());

			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/freshchat", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFreshChatIdV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("userid") long userid, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into getFreshChatId");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			FreshChat freshChat = userServiceV4.getFreshChatByUserId(user.getId());
			if (freshChat != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("freshchat_id", freshChat.getFreshchat_id());
			} else {
				response.put("Status", 0);
				response.put("Msg", "User don't have freshchat id");
			}

		} catch (Exception e) {
			log.info("user_id : " + userid);
			log.error("Error in getFreshChatId " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/userupdate/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateV5(@RequestBody User user, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering userUpdateV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				long user_id = userServiceV4.getUserByUNameOrEmailV4(user.getEmail());

				if (user_id > 0 && !(usr.getId() == user_id)) {
					response.put("Status", 0);
					response.put("Msg", "Email already exist. Please enter alternate Email");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} catch (Exception ex) {

				log.error("Exception : userUpdateV4" + ex.getLocalizedMessage());
			}

			JValidateString validString = userServiceV4.checkAlphabetOnly(user.getFirstname(), user.getLastname());
			if (!validString.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			String mobileNo = user.getMobileno().replaceAll("\\s", "");

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			String email = user.getEmail().replaceAll("\\s", "");
			if (email == null || email.equalsIgnoreCase("NA"))
				user.setEmail(user.getEmail());

			boolean Status = userServiceV4.updateUserv4byuserid(user, usr.getId());

			if (usr.getChargebeeid().toString() != null && usr.getChargebeeid().isEmpty())
				user.setChargebeeid(usr.getChargebeeid());

			// update cb customer details
			if (user.getChargebeeid() != null && !user.getChargebeeid().isEmpty()
					&& !user.getChargebeeid().equalsIgnoreCase("NA"))
				_helper.updateCBCustomerDetails(user);

			// update RvDetails in user_rvdetails
//			if (user.isUpdateRvDetails()) {
//
//				if (user.getUserRvDetails() != null) {
//					Status = userServiceV4.updateUserRVDetails(user.getUserRvDetails(), usr.getId());
//
//					if (Status) {
//						int device_cnt = user.getDevice_cnt();
//						String plan_id = user.getPlan_id();
//						UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//						boolean rvStatus = (rvObj != null) ? true : false;
//
//						boolean stat1 = rvcentricServ.saveUserBadgeTxn(usr.getId(), plan_id, device_cnt, rvStatus,
//								usr.getChargebeeid());
//						log.info("in userupdate:" + user.getId() + " Badge created:" + stat1);
//					} else
//						log.info("user:" + user.getId() + " RVer Badge not created.");
//				} else
//					Status = false;
//			}

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Updation");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateV4 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/usertoken/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse usertokenV5(@RequestBody UserToken usertoken,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "ostype", defaultValue = "", required = false) String ostype,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering usertokenV4 : " + autho);
		log.info("user time zone : " + timeZone);
		Map<String, String> mapResults = new HashMap<String, String>();
		try {

			if (ostype == null || ostype.trim().isEmpty()) {
				ostype = os;
			}

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (user != null) {
				usertoken.setOstype(ostype);
				boolean status = userServiceV4.saveOrUpdateUserTokenV4(user.getId() + "", usertoken);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Userid and token not unique");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : usertokenV4 :" + e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : usertokenV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Usertoken ");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "v5.0/userV2/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserV2_V5(@RequestParam("cmpid") String cmpid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		String autho = header.getFirst("auth");
		log.info("Entering getUserV2_V4 : " + autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String userid = String.valueOf(user.getId());
		try {
			UserV4 newUser = null;
			long cmp_id = 0;
			if (user != null) {
				cmp_id = user.getCmpId();
				// usersList = userService.getUser(userid, cmp_id);
				List<UserV4> usersList = userServiceV4.getUsersByUserId_CmpId(userid, cmp_id);

				if (usersList != null && usersList.size() > 0) {
					for (UserV4 thisUser : usersList) {
						if (userid.trim().isEmpty())
							userid = String.valueOf(thisUser.getId());
						String mobile = "";
						newUser = thisUser;
						if (thisUser != null) {
							String mobileNumber = thisUser.getMobileno().trim();
							if (!mobileNumber.isEmpty() && mobileNumber != null
									&& !mobileNumber.equalsIgnoreCase("null")) {
								String[] phoneNumber = null;

								if (!mobileNumber.contains("-")) {
									mobileNumber = "-" + mobileNumber;
								}
								phoneNumber = mobileNumber.split("-");
								String cont = phoneNumber[0];
								String mob = phoneNumber[1];
								mobile = mob.replaceAll("\\W", "");

								if (thisUser.getCountry().equalsIgnoreCase("NA") || thisUser.getCountry() == null) {
									if (cont.equalsIgnoreCase("+91") || cont.equalsIgnoreCase("91")) {
										thisUser.setCountry("IN");
									} else if (cont.equalsIgnoreCase("+44") || cont.equalsIgnoreCase("44")) {
										thisUser.setCountry("GB");
									} else {
										thisUser.setCountry("US");
									}
								}
							} else {
								mobile = "";
								thisUser.setCountry("US");
							}

//							if (mobile.length() > 10) {
//								mobile = mobile.substring(mobile.length() - 10);
//							}

							thisUser.setMobileno(mobile);
							newUser.setDelete_msg(delete_user_msg);
							newUser.setDelete_img_url(delete_img_url);
							newUser.setDelete_img_url_dark(delete_img_url_dark);
							newUser.setDelete_reason(new LinkedList<>(delete_reason.values()));
						
							String userCountryCode = "NA";
							if(thisUser.getCountry().equalsIgnoreCase("US") || thisUser.getCountry().equalsIgnoreCase("CA"))
								userCountryCode = "+1";
							else
								userCountryCode =  userServiceV4.getUserCountryCode(thisUser.getCountry());
							
							response.put("country_code", userCountryCode);
						}
					}
					ArrayList<RVAnswer> ansList =new ArrayList<RVAnswer>();// rvcentricServ.listRVAnswer();
					ArrayList<JRVAnswer> rv_cat_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> rv_type_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> pet_avail_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> no_travels_list = new ArrayList<JRVAnswer>();

					if (ansList != null) {

						for (int i = 0; i < ansList.size(); i++) {
							long ques_id = ansList.get(i).getQues_id();
							long ans_id = ansList.get(i).getId();
							String ans_value = ansList.get(i).getAns_value();
							JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);

							if (ques_id == 1) {
								rv_cat_list.add(ansObj);
							} else if (ques_id == 2) {
								rv_type_list.add(ansObj);
							} else if (ques_id == 3) {
								pet_avail_list.add(ansObj);
							} else {
								no_travels_list.add(ansObj);
							}
						}
					}
					boolean is_travelprofile = false;

//					UserRvDetails rvObj = userServiceV4.getUserRvDetails(Long.parseLong(userid));
//					JUserRVDetail travelprofile = new JUserRVDetail();
//
//					if (rvObj != null) {
//						travelprofile = new JUserRVDetail(rvObj.getId(), rvObj.getOwn_rv(), rvObj.getRvtype(),
//								rvObj.getWithPet(), rvObj.getHow_often(), rvObj.getOthers_type());
//						is_travelprofile = true;
//					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("User", newUser);
					response.put("travelprofile", null);
					response.put("is_travelprofile", is_travelprofile);
					response.put("rv_cat_list", rv_cat_list);
					response.put("rv_type_list", rv_type_list);
					response.put("pet_avail_list", pet_avail_list);
					response.put("no_travels_list", no_travels_list);
					response.put("edit_country", edit_country);

				} else {
					response.put("Status", 0);
					response.put("Msg", "User not found!");
					response.put("User", newUser);
				}
			}

		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	// userUpdateV4 - By Anand
	@RequestMapping(value = "v4.0/userupdate/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateV4(@RequestBody User user, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering userUpdateV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				long user_id = userServiceV4.getUserByUNameOrEmailV4(user.getEmail());

				if (user_id > 0 && !(usr.getId() == user_id)) {
					response.put("Status", 0);
					response.put("Msg", "Email already exist. Please enter alternate Email");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} catch (Exception ex) {

				log.error("Exception : userUpdateV4" + ex.getLocalizedMessage());
			}

			JValidateString validString = userServiceV4.checkAlphabetOnly(user.getFirstname(), user.getLastname());
			if (!validString.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			String mobileNo = user.getMobileno().replaceAll("\\s", "");

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			String email = user.getEmail().replaceAll("\\s", "");
			if (email == null || email.equalsIgnoreCase("NA"))
				user.setEmail(user.getEmail());

			boolean Status = userServiceV4.updateUserv4byuserid(user, usr.getId());

			if (usr.getChargebeeid().toString() != null && usr.getChargebeeid().isEmpty())
				user.setChargebeeid(usr.getChargebeeid());

			// update cb customer details
			if (user.getChargebeeid() != null && !user.getChargebeeid().isEmpty()
					&& !user.getChargebeeid().equalsIgnoreCase("NA"))
				_helper.updateCBCustomerDetails(user);

			// update RvDetails in user_rvdetails
//			if (user.isUpdateRvDetails()) {
//
//				if (user.getUserRvDetails() != null) {
//					Status = userServiceV4.updateUserRVDetails(user.getUserRvDetails(), usr.getId());
//
//					if (Status) {
//						int device_cnt = user.getDevice_cnt();
//						String plan_id = user.getPlan_id();
//						UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//						boolean rvStatus = (rvObj != null) ? true : false;
//
//						boolean stat1 = rvcentricServ.saveUserBadgeTxn(usr.getId(), plan_id, device_cnt, rvStatus,
//								usr.getChargebeeid());
//						log.info("in userupdate:" + user.getId() + " Badge created:" + stat1);
//					} else
//						log.info("user:" + user.getId() + " RVer Badge not created.");
//				} else
//					Status = false;
//			}

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Updation");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateV4 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// v4.0/userV2/ - SIV
	@RequestMapping(value = "v4.0/userV2/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserV2_V4(@RequestParam("cmpid") String cmpid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		String autho = header.getFirst("auth");
		log.info("Entering getUserV2_V4 : " + autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		List<UserV4> usersList = new ArrayList<UserV4>();
		List<UserV4> responseList = new ArrayList<UserV4>();
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String userid = String.valueOf(user.getId());
		try {
			long cmp_id = 0;
			if (user != null) {
				cmp_id = user.getCmpId();
				// usersList = userService.getUser(userid, cmp_id);
				usersList = userServiceV4.getUsersByUserId_CmpId(userid, cmp_id);

				if (usersList != null && usersList.size() > 0) {
					for (UserV4 thisUser : usersList) {
						if (userid.trim().isEmpty())
							userid = String.valueOf(thisUser.getId());
						String mobile = "";
						UserV4 newUser = thisUser;
						if (thisUser != null) {
							String mobileNumber = thisUser.getMobileno().trim();
							if (!mobileNumber.isEmpty() && mobileNumber != null
									&& !mobileNumber.equalsIgnoreCase("null")) {
								String[] phoneNumber = null;

								if (!mobileNumber.contains("-")) {
									mobileNumber = "-" + mobileNumber;
								}
								phoneNumber = mobileNumber.split("-");
								String cont = phoneNumber[0];
								String mob = phoneNumber[1];
								mobile = mob.replaceAll("\\W", "");

								if (thisUser.getCountry().equalsIgnoreCase("NA") || thisUser.getCountry() == null) {
									if (cont.equalsIgnoreCase("+91") || cont.equalsIgnoreCase("91")) {
										thisUser.setCountry("IN");
									} else if (cont.equalsIgnoreCase("+44") || cont.equalsIgnoreCase("44")) {
										thisUser.setCountry("GB");
									} else {
										thisUser.setCountry("US");
									}
								}
							} else {
								mobile = "1234567890";
								thisUser.setCountry("US");
							}

							if (mobile.length() > 10) {
								mobile = mobile.substring(mobile.length() - 10);
							}

							thisUser.setMobileno(mobile);
							newUser.setDelete_msg(delete_user_msg);
							newUser.setDelete_img_url(delete_img_url);
							newUser.setDelete_img_url_dark(delete_img_url_dark);
							newUser.setDelete_reason(new LinkedList<>(delete_reason.values()));
						
							String userCountryCode = "NA";
							if(thisUser.getCountry().equalsIgnoreCase("US") || thisUser.getCountry().equalsIgnoreCase("CA"))
								userCountryCode = "+1";
							else
								userCountryCode =  userServiceV4.getUserCountryCode(thisUser.getCountry());
							
							response.put("country_code", userCountryCode);
							responseList.add(newUser);
						}
					}
					ArrayList<RVAnswer> ansList =new ArrayList<RVAnswer>();// rvcentricServ.listRVAnswer();
					ArrayList<JRVAnswer> rv_cat_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> rv_type_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> pet_avail_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> no_travels_list = new ArrayList<JRVAnswer>();

					if (ansList != null) {

						for (int i = 0; i < ansList.size(); i++) {
							long ques_id = ansList.get(i).getQues_id();
							long ans_id = ansList.get(i).getId();
							String ans_value = ansList.get(i).getAns_value();
							JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);

							if (ques_id == 1) {
								rv_cat_list.add(ansObj);
							} else if (ques_id == 2) {
								rv_type_list.add(ansObj);
							} else if (ques_id == 3) {
								pet_avail_list.add(ansObj);
							} else {
								no_travels_list.add(ansObj);
							}
						}
					}
					boolean is_travelprofile = false;

//					UserRvDetails rvObj = userServiceV4.getUserRvDetails(Long.parseLong(userid));
//					JUserRVDetail travelprofile = new JUserRVDetail();
//
//					if (rvObj != null) {
//						travelprofile = new JUserRVDetail(rvObj.getId(), rvObj.getOwn_rv(), rvObj.getRvtype(),
//								rvObj.getWithPet(), rvObj.getHow_often(), rvObj.getOthers_type());
//						is_travelprofile = true;
//					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("users", responseList);
					response.put("travelprofile", null);
					response.put("is_travelprofile", is_travelprofile);
					response.put("rv_cat_list", rv_cat_list);
					response.put("rv_type_list", rv_type_list);
					response.put("pet_avail_list", pet_avail_list);
					response.put("no_travels_list", no_travels_list);
					response.put("edit_country", edit_country);

				} else {
					response.put("Status", 0);
					response.put("Msg", "User not found!");
					response.put("users", responseList);
				}
			}

		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// ========get user by name ================
	// getBreedsV4 - by anand
	@RequestMapping(value = "v4.0/getUserByUsernameV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsernameV4(@RequestParam("name") String name,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getUserByUsernameV4 : " + name);
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		String autho = header.getFirst("auth");
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", name);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);
			response.put("user", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// v4.0/usertoken/ - SIV
	@RequestMapping(value = "v4.0/usertoken/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse usertokenV4(@ModelAttribute UserToken usertoken,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "ostype", defaultValue = "", required = false) String ostype,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering usertokenV4 : " + autho);
		log.info("user time zone : " + timeZone);
		Map<String, String> mapResults = new HashMap<String, String>();
		try {

			if (ostype == null || ostype.trim().isEmpty()) {
				ostype = os;
			}

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (user != null) {
				usertoken.setOstype(ostype);
				boolean status = userServiceV4.saveOrUpdateUserTokenV4(user.getId() + "", usertoken);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Userid and token not unique");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : usertokenV4 :" + e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : usertokenV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Usertoken ");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// ==========save / update userdevice info========
	// v4.0/userdeviceinfo/ - SIV
	@RequestMapping(value = "v4.0/userdeviceinfo/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userdeviceinfoV4(@RequestBody JUserDeviceInfo jUserDeviceInfo,
			HttpServletRequest request,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering userdeviceinfoV4 : " + autho);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ee) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ee.getLocalizedMessage());
				log.error("Exception occured : " + ee.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.info("IP Address  :" + request.getRemoteAddr());

			boolean status = userServiceV4.saveOrUpdateUserDeviceInfoV4(user.getId(), jUserDeviceInfo,
					request.getRemoteAddr(), _helper.getCurrentTimeinUTC());
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User deviceinfoV4");
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "userid and device id also be unique");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : userdeviceinfoV4  " + e.getLocalizedMessage());
		}

		catch (Exception e) {
			log.error("Exception : userdeviceinfoV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User deviceinfoV4");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	public boolean deleteordermap(String meid, int initialDeviceStateid) {
		boolean status = niomDbservice.deleteMappedOrder(meid);

		boolean inventory_result = niomDbservice.updateInventoryNewMeid(initialDeviceStateid + "", meid, null);

		if (status && inventory_result) {
			return true;
		} else {
			return false;
		}
	}

	JSONObject getInventory(String qrcCode) {
		JResponse response = new JResponse();
		try {
			List<Inventory> inventory = niomDbservice.getInventory(qrcCode);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("inventory", inventory);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			return new JSONObject(response).getJSONObject("response");
		}
		return new JSONObject(response).getJSONObject("response");

	}

	public boolean nameCheck(Orders order, ActivateUser activateUser) {
		if (!order.getBilling_first_name().equalsIgnoreCase(activateUser.getFirstname().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(activateUser.getLastname().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	private boolean updateExternalOrders(Orders order) {
		return niomDbservice.saveORupdateOrder(order);
	}

	public JSONObject getNiomGetOrderCount(String orderchannel, String orderid) {
		// TODO Auto-generated method stub

		JResponse response = new JResponse();

		List<Orders> orders = new ArrayList<Orders>();
		List<Ordermap> ordermap = new ArrayList<Ordermap>();

		try {
			orders = niomDbservice.getOrderById(orderchannel, orderid);

			String totalOrderedQuantity = "";

			String totalMappedCount = "";

			if (orders == null) {
				response.put("Status", 0);
				log.info("No Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ "Order ID  :" + orderid);
				response.put("Error", "No order found for respective order id");
				return new JSONObject(response).getJSONObject("response");
			}

			if (orders.size() > 0) {

				log.info("Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ " Order ID  :" + orderid);

				String quantityList[] = orders.get(0).getQuantity().split(":");

				totalOrderedQuantity = quantityList[0];

				ordermap = niomDbservice.checkOrderMappedCount(Long.toString(orders.get(0).getOrder_id()));

				if (ordermap != null) {
					totalMappedCount = Integer.toString(ordermap.size());
				} else {
					totalMappedCount = "0";
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Order", orders.get(0));
				response.put("Ordermap", ordermap);
				response.put("Totalordered", totalOrderedQuantity);
				response.put("Totalmapped", totalMappedCount);

				return new JSONObject(response).getJSONObject("response");

			} else {
				response.put("Status", 0);
				log.info("No Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ "Order ID  :" + orderid);
				response.put("Error", "No order found for respective order id");
				return new JSONObject(response).getJSONObject("response");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("GetMappedOrderCount Exception : " + e.getLocalizedMessage());
			return new JSONObject(response).getJSONObject("response");
		}
	}

	public boolean orderMapping(String meids, String mapped_date, long devicestateid, String l, String devicemodel,
			String subscriptioncreated, String isuserregistered) {
		log.info("Entered into orderMapping : meids : " + meids);
		boolean res = true;
		// Check if given meids are valid and are not already mapped
		String validStatus = niomDbservice.checkmeidsvalid(meids, devicemodel);

		if (!validStatus.equalsIgnoreCase("valid")) {
			log.info("orderMapping:: One or more meids are not valid for mapping." + validStatus);
			return false;
		}
		// Update the device status and order_id for given meid in inventory
		boolean inventory_result = niomDbservice.updateInventoryNewMeid("6", meids, l);
		String meid_list[] = meids.split(",");
		ArrayList<Long> locationids = new ArrayList<Long>();

		if (inventory_result) {
			if (locationids != null && !locationids.isEmpty()) {
				for (int i = 0; i < meid_list.length; i++) {
					Device_history device_history = new Device_history(devicestateid, meid_list[i].replaceAll("\'", ""),
							locationids.get(i), mapped_date);

					boolean device_res = niomDbservice.saveDeviceHistory(device_history);
					if (device_res == false) {
						return res;
					}
				}

				boolean map_date_res = niomDbservice.updateOrderMappedDate(mapped_date, l);

				if (map_date_res) {
					log.info("vieworder : order_id = " + l);
					List<Jorder> jorders = niomDbservice.getJorder(l);

					if (jorders.size() > 0) {

						String name = null;
						String username = null;

						if (jorders.get(0).getBilling_last_name() != "NA")
							name = jorders.get(0).getBilling_first_name() + " " + jorders.get(0).getBilling_last_name();
						else
							name = jorders.get(0).getBilling_first_name();

						if ((jorders.get(0).getBilling_email().length()) > 30) {
							int ind = (jorders.get(0).getBilling_email()).indexOf("@");
							username = (jorders.get(0).getBilling_email()).substring(0, ind);
						} else {
							username = jorders.get(0).getBilling_email();
						}

						for (int i = 0; i < meid_list.length; i++) {
							int orderid = Integer.valueOf(l);
							log.info("Input order id :" + l);

							Ordermap ordermap = new Ordermap(orderid, jorders.get(0).getDatetime(), name,
									jorders.get(0).getBilling_email(), jorders.get(0).getBilling_phone(),
									meid_list[i].replaceAll("\'", ""), username, "NA", jorders.get(0).getDevicemodel(),
									"1", jorders.get(0).getCustomer_note(), "Activated", "1111-11-11 11:11:11",
									_helper.getCurrentTimeinUTC(), subscriptioncreated, isuserregistered);

							boolean result = niomDbservice.saveORupdateMappedOrder(ordermap);

							if (result == false) {
								return res;
							}
						}
						res = true;
					}
				}
			} else {
				log.info("Location ids are empty");
			}
		}
		return res;
	}

	public boolean updateOrdersData(String orderchannel, Orders order, Inventory inventory) {

		boolean status = false;
		Gson gson = new Gson();

		JSONObject jorderIdCheckResponse = new JSONObject();

		jorderIdCheckResponse = getNiomGetOrderCount(orderchannel, order.getId() + "");

		if (jorderIdCheckResponse != null) {

			int orderIdCheckStatus;
			try {
				orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				log.error(e.getLocalizedMessage());
				return false;
			}

			if (orderIdCheckStatus > 0 ? true : false) {

				try {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				} catch (JsonSyntaxException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				} catch (JSONException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				}
				order.setProvision_status(1);
				order.setEmail_status("111");
				order.setTracking_summary("NA");
				order.setStatus("completed");
				order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
				order.setWelcome_status(order.getWelcome_status());
				if (inventory.getDevicestate().getName().toLowerCase().contains("amazon")) {
					order.setFulfillmentchannel("amazon");
				} else {
					order.setFulfillmentchannel("rv");
				}

				boolean updateExternalOrderData = updateExternalOrders(order);
				log.info("updateExternalOrderData Status : " + updateExternalOrderData);

				status = updateExternalOrderData;
			} else {
				return false;
			}
		}

		return status;
	}

	public JResponse activateEseyeSim(String niomActivateEseyeSimURL, String iccids, String tariff, String groupname) {
		JResponse response = new JResponse();

		try {
			niomActivateEseyeSimURL = niomActivateEseyeSimURL + "?iccids=" + URLEncoder.encode(iccids.trim(), "UTF-8")
					+ "&tariff=" + tariff + "&groupname=" + groupname;
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPost post = new HttpPost(niomActivateEseyeSimURL);

			post.setHeader("Content-type", "application/json");
			try {
				HttpResponse niomresponse = httpClient.execute(post);
				String niomRes = EntityUtils.toString(niomresponse.getEntity());
				JSONObject _response = new JSONObject();
				Gson _gson = new Gson();
				try {
					JSONObject _res = new JSONObject(niomRes);
					_response = _res.getJSONObject("response");
					int _status = _response.getInt("Status");
					String msg = _response.getString("Msg");
					response.put("Status", _status);
					response.put("Msg", msg);
					response.put("ErrorCode", "0");
				} catch (JSONException e) {
					response.put("Status", 0);
					response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
					response.put("ErrorCode", "ER026");
					log.error(e.getLocalizedMessage());
				}

			} catch (ClientProtocolException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER027");
			} catch (IOException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER028");
				log.error(e.getLocalizedMessage());
			}

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
			response.put("ErrorCode", "ER029");
			log.error("activateEseyeSim : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public JCreateGateway createOrUpdateGateway(ActivateUser activateUser, Inventory inventory, int model_id,
			long group_id, String sensor_available, String passwordType, User user) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();

		JGateway jgateway = new JGateway();
		jgateway.setName(activateUser.getGatewayName());
		jgateway.setMeid(inventory.getMeid());
		jgateway.setMdn(inventory.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(model_id);
		jgateway.setGroupid(group_id);
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(sensor_available);
		jgateway.setPasswordtype(Long.parseLong(passwordType));
		jgateway.setDescription("");
		jgateway.setQrcode(activateUser.getQrcCode().trim());

		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Gateway gateway = new Gateway();
		try {
			gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
			isGatewayCreated = true;
		} catch (Exception e1) {
			log.error("createOrUpdateGateway : " + e1.getLocalizedMessage());
			return null;
		}

		if (jgateway.isUserGatDis()) {
			user.getGateways().add(gateway);
			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}

		if (jgateway.getId() == 0) {
			try {
				reportService.saveGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveGatewayReportStatus = true;
				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} catch (Exception e) {
				log.error("1saveORupdateGateway-Default Reports Cannot be Generated::::" + e.getLocalizedMessage());
			}
		}

		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);

		return jcreateGateway;
	}
	
	@RequestMapping(value = "v5.0/updatepageviewcount/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updatepageviewcountV5(
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (usr != null) {
				boolean result = userServiceV4.updateViewCount(usr.getId());

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Count updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/updatepageviewcount/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updatepageviewcount(
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (usr != null) {
				boolean result = userServiceV4.updateViewCount(usr.getId());

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Count updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getamazonreviewstatus/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getAmazonReviewStatus(@RequestParam("gatewayid") long gatewayid,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username!");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (usr != null) {
				if (showamazonrateus) {
					AmazonUserReview amazonReviewObj = userServiceV4.getAmazonUserReview(usr.getId(),1);
					AmazonReviewStatus reviewStatus = new AmazonReviewStatus();
					int viewCount = 0;
					int rptCount = 0;
					String lastRptDate = "";

					if (amazonReviewObj == null || (!amazonReviewObj.isAmazon_rateus())) {
						viewCount = userServiceV4.getViewCount(usr.getId());
						rptCount = rptServicev4.getGatewayReportCount(gatewayid);
						lastRptDate = rptServicev4.getLastGatewayReporttime(gatewayid);

						String currenttime = IrisservicesUtil.getCurrentTimeUTC();
						SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
						formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

						Date pre = formatter.parse(lastRptDate);
						Date curr = formatter.parse(currenttime);
						log.info(pre + " : " + curr);

						long diffInMins = ((curr.getTime() - pre.getTime()) / 60000);
						log.info(" diffInMins : " + diffInMins);

						int amazon_redirect_cnt = 3;
						if ((viewCount % amazonrateuscount == 0) && (rptCount > 1000) && (diffInMins <= 60)) {
							boolean show_dialog = true;
							String redirecturl = amazonredirecturl;

							reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, amazon_redirect_cnt, "");

							response.put("reviewstatus", reviewStatus);
							response.put("Status", 1);
							response.put("Msg", "Success");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Amazon rating not applicable");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "Already amazon rating completed");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Amazon Rating not enabled");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/updateamazonreview/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateAmazonReview(@RequestBody AmazonUserReview auReview,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) long monitorType) {
		String autho = header.getFirst("auth");
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (usr != null) {
				boolean result = userServiceV4.updateAmazonReview(auReview, monitorType);

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Review updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/forgetpassword", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse oneTimePassword(@RequestHeader HttpHeaders header,
			@RequestParam(value = "username") String userName,
			@RequestParam(value = "via", defaultValue = "mobileno") String via, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "mobile_number", defaultValue = "0", required = false) String mobileno) {
		log.info(" Entered into forgetpassword v4 :: username :" + userName);
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			log.info("generateaddonpurchaselink :" + auth);

			try {

				if (!validation_authkey.equals(auth)) {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				}

			} catch (InvalidAuthoException ex) {

				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}

			response = commonService.oneTimePassword(userName, via, request_from,mobileno);
		} catch (Exception e) {
			log.error(" Error in forgetpassword v4 " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}


	@RequestMapping(value = "v4.0/validateotp", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOneTimePassword(@RequestHeader HttpHeaders header, @RequestParam long otp,
			@RequestParam(value = "username") String userName, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into validateOneTimePassword :: username :" + userName);
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("validateotp :" + auth);

		try {

			if (!validation_authkey.equals(auth)) {
				UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			}

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Exception while getting user for auth : " + auth);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
		
		try {
			response = commonService.validateOneTimePassword(auth, otp, userName, request_from);
		} catch (Exception e) {
			log.error(" Error in validateOneTimePassword " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getuserbyusername", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsername(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("name") String name, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering getuserbyusername : " + name + "auth key : " + auth);

		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		UserV4 user = null;
		try {
			if (!auth.equalsIgnoreCase(validation_authkey)) {
				response.put("Status", 0);
				response.put("Msg", "Authentication Error");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				user = userServiceV4.verifyAuthV3("username", name);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			user.setPassword("NA");
			response.put("users", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/pwdupdatev2", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdateV4(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestBody UpdatePassword udatePassword,
			Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into Password Update v2 ");

		JResponse response = new JResponse();
		try {

			String autho = header.getFirst("auth");

			String password = _helper.base64Decoder(udatePassword.getPassword());
			if (password == null) {
				response.put("Status", 0);
				response.put("Msg", "Password not updated. Please try again later.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if (!validatePassword.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String userName = udatePassword.getUsername();

			UserV4 user = null;

			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			return commonService.passwordUpdateV4(user, udatePassword);

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + header.getFirst("auth"));
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
	}

	@RequestMapping(value = "v5.0/validateCurrentPassword", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateCurrentPasswordV5(@RequestHeader HttpHeaders header,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("currentPassword") String currentPassword, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into Password Update v2 ");

		JResponse response = new JResponse();

		String autho = header.getFirst("auth");
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			return commonService.validateCurrentPassword(user,currentPassword);
		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again later");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
	}

	@RequestMapping(value = "v4.0/freshchat", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveFreshChatId(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestBody FreshChat freshChat, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into saveFreshChat");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}

			FreshChat fchat = userServiceV4.getFreshChatByUserId(freshChat.getUser_id());
			boolean isFreshChatIdSaved = false;
			if (fchat == null) {
				isFreshChatIdSaved = userServiceV4.saveFreshChatId(freshChat);
			} else if (fchat.getFreshchat_id().equalsIgnoreCase(freshChat.getFreshchat_id())) {
				isFreshChatIdSaved = true;
			} else {
				long id = fchat.getId();
				freshChat.setId(id);
				isFreshChatIdSaved = userServiceV4.saveFreshChatId(freshChat);
			}

			if (isFreshChatIdSaved) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
			}

		} catch (Exception e) {
			log.info("user_id : " + freshChat.getUser_id());
			log.error("Error in saveFreshChat " + e.getLocalizedMessage());

			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/freshchat", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFreshChatId(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("userid") long userid, @RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into getFreshChatId");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			FreshChat freshChat = userServiceV4.getFreshChatByUserId(user.getId());
			if (freshChat != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("freshchat_id", freshChat.getFreshchat_id());
			} else {
				response.put("Status", 0);
				response.put("Msg", "User don't have freshchat id");
			}

		} catch (Exception e) {
			log.info("user_id : " + userid);
			log.error("Error in getFreshChatId " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v4.0/deleteuser", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUser(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestBody UserDelete userDlt, @RequestParam(value = "os", defaultValue = "", required = false) String os, @RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into deleteUser...");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later.");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				long userId = user.getId();
				if(userDlt.getUser_id()>0)
					userId = userDlt.getUser_id();
				else
					userDlt.setUser_id(userId);
				
				boolean Status = userServiceV4.deleteUserById(userId);
				userDlt.setUpdated_date(_helper.getCurrentTimeinUTC());
				
				if (Status) {
					long id = userServiceV4.getUserDlt(userDlt.getUser_id());
					userDlt.setId(id);
					boolean deleteStatus = userServiceV4.saveOrUpdateUserDelete(userDlt);
					log.info("Update user_dlt : "+deleteStatus);
					
					/* Send to support team */
					async.SendEmail_SES(delete_email_to, delete_email_cc, "", delete_user_emailsub , EmailContent.deleteUserRequest(user));
					
					response.put("Status", 1);
					response.put("Msg", delete_user_confirmation);
					response.put("contact_msg", "Our support team will contact you within 1 business day to delete your account");
					
					String email = user.getEmail();
					if(email.equalsIgnoreCase("NA"))
						email = user.getUsername();
					
					Template template = (Template) templates.getTemplate("delete-processing.ftl");
					Map<String, String> deleteRequestParams = new HashMap<>();
					deleteRequestParams.put("firstname", user.getFirstname());
					
					ResponseEntity<String> deleteRequestContent = ResponseEntity
							.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, deleteRequestParams));
					String emailContent = deleteRequestContent.getBody();
					email_helper.SendEmail_SES(user.getEmail(), "",
							"", delete_user_emailsub, emailContent);
					
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to delete account");
				}
			} catch (Exception e) {
				log.error("Error occurred while delete user : "+e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Failure");
				response.put("Error", e.getMessage());
			}

		} catch (Exception e) {
			log.error("Error in deleteUser " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	//Get Country code - savitha
	@RequestMapping(value = "v4.0/countrycode", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCountryCode(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "req_from", defaultValue = "", required = false) String req_from,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		String auth = header.getFirst("auth");
		log.info("Entering getCountryCode : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			List<CountryCodeV4> countryCodeList = new ArrayList<CountryCodeV4>();
			countryCodeList = userServiceV4.getCountryCodeList(req_from);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CountryCode", countryCodeList);
		} catch (Exception e) {
			log.error("Exception in getCountryCode : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	//Get user promotional notification enabled for user - savitha
	@RequestMapping(value = "v4.0/getpromotionalnotification", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPromotionalNotif(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header, @RequestParam("userid") long user_id) {
		String auth = header.getFirst("auth");
		log.info("Entering getPromotionalNotif : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			UserMetaData userNotif = userServiceV4.getUserMetaData(user_id);
			boolean showNotif = true;
			if (userNotif != null)
				showNotif = userNotif.isShow_marketing_notif();
			else {
				userNotif = new UserMetaData();
				userNotif.setShow_marketing_notif(showNotif);
				userNotif.setVpm_id("NA");
				userNotif.setUser_id(user_id);
				async.enableDisableMarketingNotification(userNotif);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Enable_Notification", showNotif);
		} catch (Exception e) {
			log.error("Exception in getCountryCode : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v4.0/validateCurrentPassword", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateCurrentPassword(@RequestHeader HttpHeaders header,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam("currentPassword") String currentPassword, Authentication authentication,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into Password Update v2 ");

		JResponse response = new JResponse();

		String autho = header.getFirst("auth");
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			return commonService.validateCurrentPassword(user,currentPassword);
		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again later");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
	}

	
	//Enable Disable Marketing InApp Notification - savitha
	@RequestMapping(value = "v4.0/setmarketingnotification", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enableDisableMarketingNotification(
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam("enable") boolean enable, @RequestParam("userid") long user_id, Authentication authentication,
			@RequestHeader HttpHeaders header) {
		String auth = header.getFirst("auth");
		log.info("Entering enableDisableMarketingNotification : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + auth + " : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			UserMetaData userNotif = userServiceV4.getUserMetaData(user_id);

			if (userNotif == null)
			{
				userNotif = new UserMetaData();
				userNotif.setVpm_id("NA");
			}
			
			userNotif.setUser_id(user_id);
			userNotif.setShow_marketing_notif(enable);

			boolean status = userServiceV4.enableDisableMarketingNotification(userNotif);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Updated Successfully!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to update notification!");
			}

		} catch (Exception e) {
			log.error("Exception in enableDisableMarketingNotification : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occurred!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@GetMapping("v1.0/deleteuserpl")
	public String deleteUserWeb(@RequestParam String username) {
		log.info("Entered into deleteUser :: username : " + username );
		try {
			
			User user = null;
			try {
				user = userService.getUserByName(username);
			} catch (Exception ex) {
				log.error("Exception while getting user by username : " + username);
				return "Invalid username";
			}
			
			boolean status = userServiceV4.deleteUserById(user.getId());
			log.info("Update status of delete key in user : "+ status);
			
			UserDelete userDlt = new UserDelete();
			long id = userServiceV4.getUserDlt(user.getId());
			userDlt.setId(id);
			userDlt.setUser_id( user.getId() );
			userDlt.setDlt_reason("user delete request from deleteuserpl API");
			userDlt.setDlt_description("");
			userDlt.setUpdated_date( _helper.getCurrentTimeinUTC() );
			
			boolean deleteStatus = userServiceV4.saveOrUpdateUserDelete(userDlt);
			log.info("Update user_dlt : " + deleteStatus);

			if( status )
				return "Success";
		} catch (Exception e) {
			log.error("Error in deleteUser :: Error : "+ e.getLocalizedMessage());
		}
		return "Failed";
	}

	@PostMapping("v5.0/saveorupdatedevicelist")
	@ResponseBody
	public JResponse saveorupdatedevicelist(@RequestBody JUserMiniCam JUserMiniCam,
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		log.info("Entered into saveorupdatedevicelist auth :: "+header.getFirst("auth"));
		JResponse response = new JResponse();
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			
			try {
				if (JUserMiniCam != null && JUserMiniCam.getDevicelist().size() > 0) {
					StringJoiner deviceIds = new StringJoiner(",");
					for (DeviceList thisUser : JUserMiniCam.getDevicelist()) {
						deviceIds.add("'" + thisUser.getDevice_id() + "'");
						boolean createdStatus = userServiceV4.saveOrUpdateUserMini(user.getId(),thisUser.getDevice_id(),thisUser.getDevice_name(),thisUser.getIs_petcam());
					}
					if(deviceIds.toString() != null) {
						userServiceV4.updateDeleteDeviceStatus(user.getId(),deviceIds.toString());
					}
					
				}
				
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Please try again later");
				return response;
			}
			
			
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in saveorupdatedevicelist :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}
		return response;
	}

	// only used until version 7.9.3
	@GetMapping("v5.0/getfirebaseinappevent")
	@ResponseBody
	public JResponse getfirebaseinappevent(
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		log.info("Entered into getfirebaseinappevent");
		JResponse response = new JResponse();
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			ArrayList<Map<String, Object>> eventList = new ArrayList<Map<String, Object>>();

			try {
				//eventMap.put("event", "nimble_app_open_event");
				ArrayList<String> events = new ArrayList<String>();
				events = userServiceV4.checkActivateEligible(user.getId());
				if(events.isEmpty()) {
					events = userServiceV4.checkAmazonRatingEligible(user.getId());
					
					
					if(events.isEmpty()) {
						/*checking cx has a eligible for refer and earn*/
						events = userServiceV4.checkReferralEligible(user.getId());
						if(events.isEmpty()) {
							/*checking cx has a particular firebase event in user_firebase_event table*/
							events = userServiceV4.getUserFirebaseEvents(user.getId());
							if(events.isEmpty())
								/*checking cx has pet monitor or waggleCam Pro or both*/
								events = userServiceV4.checkProductBasedEligible(user.getId());
						}
					}
						
				}
				for ( String evs : events)
				{
					Map<String, Object> eventMap = new HashMap<String, Object>();
					eventMap.put("event", evs);
					eventList.add(eventMap);
				}
				
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Please try again later");
				return response;
			}
			
			
			response.put("Status", 1);
			response.put("eventList", eventList);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in getfirebaseinappevent :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}
		return response;
	}

	// only used until version 7.9.3
	@PostMapping("v5.0/updatefirebaseeventcount")
	@ResponseBody
	public JResponse updateFirebaseEventCount(
			@RequestParam(value = "clickcnt", defaultValue = "0", required = true) int clickcnt,
			@RequestParam(value = "viewcnt", defaultValue = "0", required = true) int viewcnt,
			@RequestParam String event_id,@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		log.info("Entered into updatefirebaseeventcount:eventid: "+event_id);
		JResponse response = new JResponse();
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			

			try {
				boolean status = userServiceV4.updateFirebaseCnt(user.getId() ,viewcnt, clickcnt, event_id);
				
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Please try again later");
				return response;
			}
			
			
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in getfirebaseinappevent :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}
		return response;
	}
	
	@PostMapping("v5.0/askfeature")
	public JResponse saveOrUpDateAskFeature(
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam String feature_request) {
		log.info("Entered into saveOrUpDateAskFeature :: auth : "+header.getFirst("auth"));
		JResponse response = new JResponse();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", header.getFirst("auth"));
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + header.getFirst("auth") + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please tryagain");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			if(feature_request.length() > 300) {
				response.put("Status", 0);
				response.put("Msg", "The character count of request has reached the maximum limit.");
				return response;
			}
			
			if(feature_request == null || feature_request.trim().equalsIgnoreCase("")) { //|| !Pattern.matches("[a-zA-Z0-9@. ]+",feature_request)
				log.error("ask_feature not saved for user_id : enter valid text ");
				response.put("Status", 0);
				response.put("Msg", "Invalid Request");
				return response;
			}
			
			AskFeature askFeature = userServiceV4.saveOrUpdateAskFeature( new AskFeature(0, user.getId(), feature_request, _helper.getCurrentTimeinUTC()) );
			if( askFeature == null ) {
				log.error("ask_feature not saved for user_id : "+user.getId()+" :: feature_request : "+ feature_request);
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please tryagain");
				return response;
			}
			
			String to_address = askfeatureto_address;
			String cc_address = askfeaturecc_address;
			String bcc_address = "";
			
			String currenttime = IrisservicesUtil.getCurrentTimeUTC();
			
			String mailSub = "New Feature Request from App User : "+ user.getUsername();
			String mailContent = "<p>Hi Waggle,</p>" + "<p>Find the user feedback request</p>";
			mailContent += "<p>User Email      : "+ user.getEmail() +"</p>";
			mailContent += "<p>Feedback        : "+ feature_request +"</p>";
			mailContent += "<p>Timestamp       : "+ currenttime +"</p>";
			mailContent += "<p>User Activated On : "+ (String)(user.getCreatedOn()) +"</p>";
			mailContent = mailContent + "<br><br>Thanks,<br> Waggle ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
			
			response.put("Status", 1);
			response.put("Msg", "Thanks for your feedback! We're committed to improving your experience.");
			
		} catch (Exception e) {
			log.error("Error in saveOrUpDateAskFeature :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please tryagain");
		}
		return response;
	}

	@GetMapping("v5.0/getinappevent")
	public JResponse getInappEvent(@RequestParam String os, @RequestParam String app_ver,
											Authentication authentication, @RequestHeader HttpHeaders header) {

		log.info("Entered into getInappEvent");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			ArrayList<Map<String, Object>> eventList = new ArrayList<>();

			try {

				List<String> events;

				events = userServiceV4.getUserFirebaseEvents(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				events = userServiceV4.isN7PopupEligible(user.getId());
				if(!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				JResponse referralResponse = getReferAndEarnEventResponse(user);
				if (referralResponse.get("Msg").equals("Success")) {
					return referralResponse;
				}

				events = userServiceV4.checkActivateEligible(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				events = userServiceV4.checkAmazonRatingEligible(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				events = userServiceV4.checkPetMonitorPopupEligible(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				events = userServiceV4.checkReferralEligible(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				events = userServiceV4.checkProductBasedEligible(user.getId());
				if (!events.isEmpty()) {
					return buildFirebaseEventResponse(events);
				}

				if (showInappMarketingPopup) {
					response.put("type", "inAppMarketingEvent");
					response.put("inapp_gifurl", inappGifUrl);
					response.put("inapp_redirect_url", inappNavigationUrl);
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				}

			} catch (Exception e) {
				log.error("Error in getInappEvent :: Error : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Please try again later");
			}

			response.put("Status", 1);
			response.put("eventList", eventList);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in getInappEvent :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}

		return response;
	}

	@PostMapping("v5.0/updateevent")
	@ResponseBody
	public JResponse updateEvent(
			@RequestParam(value = "clickcnt", defaultValue = "0", required = false) int clickcnt,
			@RequestParam(value = "viewcnt", defaultValue = "0", required = false) int viewcnt,
			@RequestParam(value="eventid", required = false) String event_id, @RequestParam String os,
			@RequestParam(value="eventtype") String eventType,
			@RequestParam String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestBody(required = false) InAppEvent inAppEvent) {

		log.info("Entered into updateEvent:eventType: {}", eventType);
		JResponse response = new JResponse();

		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : {}   Error : {}", auth, e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if(eventType.equalsIgnoreCase("fireBaseEvent") ||
					eventType.equalsIgnoreCase("inAppMarketingEvent")) {
				userServiceV4.updateFirebaseCnt(user.getId() ,viewcnt, clickcnt, event_id);
			}
			else {
				if(inAppEvent == null) { throw new NullPointerException("In App event is null");}
				userServiceV4.updateEvent(inAppEvent);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in update event :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}

		return response;
	}

	private JResponse getReferAndEarnEventResponse(UserV4 user) {

		log.info("Entered into getReferAndEarnEventResponse");
		JResponse response = new JResponse();

		try {
			if(!userIsEligible(user.getId())) {
				response.put("Status", 1);
				response.put("Msg", "No need to show refer and earn");
				return response;
			}

			String referralUrl = referralurl + user.getEmail();

			if (useReferralCandy) {
				for (int i = 0; i < 3; i++) {
					try {
						String referralCandyResponse = helper.signUpInReferralCandy(user.getFirstname(),
								user.getLastname(), user.getEmail(), referralCandySecretKey,
								referralCandyAccessKey);
						JSONObject jsonObject = new JSONObject(referralCandyResponse);
						String status = (String) jsonObject.get("message");
						String referralCornerUrl = (String) jsonObject.get("referralcorner_url");
						referralUrl = (String) jsonObject.get("referral_link");
						log.info("status : {} \n referralcorner_url : {} \n referral_link : {}", status, referralCornerUrl, referralUrl);
						break;
					} catch (Exception e) {
						log.error("Error while getting sign Up In Referral Candy : {}", e.getLocalizedMessage());
					}
				}
			}

			List<InAppReferReason> referAndEarnEvents = referAndEarnServiceV4.getNotInterestedReasons();
			ReferralCredits ref = referAndEarnServiceV4.getLatestReferralCredits();

			response.put("not_interested_reasons", referAndEarnEvents);
			response.put("referral_popup_image_url", referralPopupImageUrl);
			response.put("referral_popup_content1", referralPopupContent1);
			response.put("referral_popup_content2", referralPopupContent2);
			response.put("referral_popup_content3", referralPopupContent3);
			response.put("referral_popup_content4", referralPopupContent4);
			response.put("referral_popup_cta1", referalPopupCta1);
			response.put("referral_popup_cta2", referalPopupCta2);
			response.put("referallink", referralUrl);
			response.put("weblinkurl", weblinkurl);
			response.put("weblinkflag", weblinkflag);
			response.put("messagecontent", ref.getTitle());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("type", "inAppReferralEvent");
		}
		catch (Exception e) {
			log.error("Error in referAndEarnServiceV4 :: getNotInterestedReasons :: Error : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
		}

		return response;
	}

	private boolean userIsEligible(long userId) {

		log.info("Entered into userIsEligible");

		return referAndEarnServiceV4.userIsEligible(userId);
	}

	@PostMapping("v5.0/updatemobileno")
	public JResponse updateMobileno(
			@RequestParam String os,@RequestParam String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value="mobileno") String mobileno) {

		log.info("Entered into updatemobileno :eventType :: ", mobileno);
		JResponse response = new JResponse();

		try {

			String auth = header.getFirst("auth");

			User user = null;
			try {
				user = userServiceV4.verifyUser("authKey", auth);
			} catch (Exception e) {
				log.info("updatemobileno Invalid auth :: ", auth, e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			if(mobileno != null) {
				mobileno = mobileno.replaceAll("\\s+", "");
				if (mobileno.charAt(0) != '-' && mobileno.charAt(0) != '+')
					mobileno = "+" + mobileno;
			}else{
				response.put("Status", 0);
				response.put("Msg", "Mobile no empty");
				return response;
			}
			if(user.getMobileno() == null || user.getMobileno().trim().equalsIgnoreCase("")) {
				user.setMobileno(mobileno);
				userService.updateUser(user);
			}else{
				response.put("Status", 0);
				response.put("Msg", "Mobile no already updated");
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Error in update mobileno :: ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}

		return response;
	}

	private JResponse buildFirebaseEventResponse(List<String> events) {

		log.info("Entered into buildFirebaseEventResponse");
		JResponse response = new JResponse();
		List<Map<String, Object>> eventList = new ArrayList<>();

		for (String ev : events) {
			Map<String, Object> eventMap = new HashMap<>();
			eventMap.put("event", ev);
			eventList.add(eventMap);
		}

		response.put("event_id", eventId);
		response.put("type", "fireBaseEvent");
		response.put("eventList", eventList);
		response.put("Status", 1);
		response.put("Msg", "Success");

		return response;
	}

	/**
	 * Generates a JWT token for the authenticated user.
	 *
	 * @param header The HTTP headers containing the request information.
	 * @param os     The operating system of the user's device.
	 * @param app_ver The version of the user's application.
	 * @param jwtTokenData The JWT token data containing user information.
	 * @return A JResponse object containing the generated JWT token and other information.
	 */
	@PostMapping("v5.0/jwttoken")
	public JResponse getJWTToken(@RequestHeader HttpHeaders header, Authentication authentication,
								 @RequestParam(value = "os", defaultValue = "", required = false) String os,
								 @RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
								 @RequestBody JwtTokenData jwtTokenData) {

		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			String auth = header.getFirst("auth").trim();
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
			} catch (Exception e) {
				log.error("getupgradesubplansv5:userblock : {}", e.getMessage());
				response.put("Status", 0);
				response.put("Msg", e.getMessage());
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			byte[] token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientidApp,
					clientSecretApp);

			jwtTokenData.setZippedAccessToken(token);
			jwtTokenData.setAuthKey(user.getAuthKey());

			String jwtToken = oAuth2Service.generateJWTToken(jwtTokenData);
			String planPurchaseSiteUrl = plan_purchase_site_url + jwtToken;

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("url", planPurchaseSiteUrl);
			response.put("Return Time", System.currentTimeMillis());

			return response;

		} catch (Exception e) {
			log.error("Error in getJWTToken : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in generating JWT token");
			return response;
		}
	}

	@RequestMapping(value = "v6.0/userupdate", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateV6(@RequestBody User user, Authentication authentication,
												@RequestHeader HttpHeaders header,
												@RequestParam("os") String os,
												@RequestParam("app_ver") String app_ver,
												@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering userUpdateV6 : " + autho);

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JValidateString validString = userServiceV4.checkAlphabetOnly(user.getFirstname(), user.getLastname());
			if (!validString.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());

			boolean status = userServiceV4.updateFullUserName(user, usr.getId());

			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Updation");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateV6 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
		}

	/**
	 * Saves the users plan preference
	 *
	 * @param header The HTTP headers containing the request information.
	 * @param os     The operating system of the user's device.
	 * @param app_ver The version of the user's application.
	 * @param gatewayId The gatewayId for which the plan is purchased.
	 * @param userPref The plan preference of the user.
	 * @return A JResponse object containing the status of the saving of plan preference.
	 */
	@PostMapping("v5.0/updateplanpreference")
	public @ResponseBody JResponse userPlanPreferenceUpdate(@RequestHeader HttpHeaders header,
															Authentication authentication, @RequestParam("os")  String os,
															@RequestParam("app_ver") String app_ver,
															@RequestParam("gateway_id") long gatewayId,
															@RequestParam("userpref") String userPref) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering update plan preference : {}", autho);

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
			} catch (Exception e) {
				log.error("Error occurred in validating the user : {}", e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			UserPlanPreference userPlanPreference = new UserPlanPreference();

			userPlanPreference.setUserId(usr.getId());
			userPlanPreference.setGatewayId(gatewayId);
			userPlanPreference.setPlanPref(userPref);
			userPlanPreference.setUpdatedOn(_helper.getCurrentTimeinUTC());

			boolean status = userServiceV4.updateUserPlanPreference(userPlanPreference);

			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in update plan preference");
			}

		} catch (Exception e) {
			log.error("Exception : update plan preference : {}" , e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in update plan preference");
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

}
