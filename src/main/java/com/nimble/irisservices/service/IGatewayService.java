package com.nimble.irisservices.service;

import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.JAlertRangeType;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JGatewaySensorType;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JMeariNotification;
import com.nimble.irisservices.dto.JPacketReport;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FurbitLastGatewayReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayCredits;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.exception.InvalidSubgroupIdException;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IGatewayService {

	List<JGateway> getGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid, long id,
			String meid);

	public List<JGatewayConfig> getJGatewayConfig(String gatewayid, long userid, String tempunit);

	JGatewayUserDetails getGateway(String meid);

	JGatewayOverview getgatewayoverview(String groupid, String subgroupid, long id, String levelid);

	Gateway saveORupdateGateway(JGateway gateway, long cmpid)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException;

	// Save Or update Gateway to register QRC user
	Gateway saveORupdateQRCGateway(JGateway gateway, long cmpid)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException;

	boolean updateGatewayName(String gatewayName, String gatewayId);

	JSreiGatewayOverview getSreiGatewayOverview(String groupid, String subgroupid, long id, String levelid);

	JSreiGatewayOverview getSreiRGatewayOverview(String groupid, String subgroupid, long id, String levelid);

	JSreiGatewayOverview getSreiNRGatewayOverview(String groupid, String subgroupid, long id, String levelid);

	JSreiGatewayOverview getSreiInbuiltDevicesOverview(String groupid, String subgroupid, long id, String levelid);

	JGateway gatewayExitsinDB(JGateway jgateway, long cmpid);

	boolean delGateway(User user, String assetid);

	List<JSGateway> getGateways(String cmptype_id);

	GatewayCredits updateGatewayCredit(long gateway_id, long cmp_id);

	Gateway getGateway(long id);

	Gateway getGatewayDetails(String meid);

	boolean updateGatewayCreditPoints(ThrottlingSettings throtSettings);

	int saveorupdatePetprofile(List<JPetprofile> jpetprofiles, long userid);

	List<JPetprofile> getJPetprofiles(long userid, long id, long gatewayid, String name, String age, String sex,
			String breed);

	boolean updatepetprofile(String gatewayId);

	public ProbeCategory getProbeCategory(long model_id);

	public boolean gatewayOnOff(String gatewayId, boolean isEnable, String message);

	boolean enableOrDisableGateway(String gatewayId, String userId, boolean isEnable);

	public int updateGoalSetting(String gatewayId, String goal);

	public int updateCaloriesGoalSetting(String gatewayId, String goal);

	public JResponse getJPetprofilesByUser(long userid, long gatewayid, int mnitortype);

	public List<Gateway> getGatewayByMonitorType(String monitortype, long userid);

	public List<Gateway> getGatewayByUser(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid);

	public PetProfile getPetProfile(long gateway_id);

	public JGatewayUserDetails getGatewayAndUserDetails(String meid);

	public List<FurbitLastGatewayReport> getFLastGatewayReportByUser(long userid);

	public ArrayList<JGatewayConfig> getGatewayConfig(long userid);

	// New by SIV
	public boolean updatePetProfile(PetProfile petProfile);

	public AssetModel getAssetModelByName(String inventorymodelname);

	public boolean checkQrcExist(long id, String qrcode);

	public int updateGateway(Gateway gateway);

	public Gateway getGatewayByid(long id);

	public JGateway getJGateway(String columnname, String value);

	public List<JGateway> getJGatewayByUser(long userid, String monitor_type);

	public AssetModel getAssetModel(long id);

	public Date getBirthDate(String calendarValue, String age);

	public List<JGatewayForPet> getNotMappedGateway(long userid);

	public long getOrderchannelid(String orderchannel);

	// public int saveOrderchannelDetail(long user_id,long gateway_id,long
	// orderchannel);

	public JGatewayUserDetails getGatewayByMAC(String macid, long user_id);

	public boolean updateProfileImgPath(long ppid, String imgpath);

	public int getDeviceCount(long userid);

	boolean enableOrDisablePetProfile(long userId, List<EnableOrDisablePetProfile> enableOrDisablePetPrfList);

	public boolean updateGatewayName(String gatewayid);

	public List<Gateway> getGateways(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid);

	public boolean changeGatewayOrderidStatus(long id, boolean pFromStatus);

	public Gateway saveORupdateRecallGateway(JGateway jgateway, long id)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException;

	public String changeOldGatewayDetails(Gateway exGatewayInDB);

	public boolean changeOldAssertDetails(long id, String changedMeid);

	public boolean saveRecallGateway(Gateway gateway);

	boolean changeDefalutRptInLastGateway(long gatewayId, String currentTimeUTC, int i);

	public Gateway saveORupdateReturnGateway(JGateway jgateway, long id)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException;

	public Gateway saveReturnGateway(Gateway gateway);

	public String changeOldAssertDetailsToZ(Asset giveAsset);

	public List<String> getOldAssertList(Asset asset);

	public boolean changeOldAssertDetailsByAddr(String meid, String changedMeid);

	public Asset getAssetById(long id);

	public boolean updateAsset(Asset asset);

	public AssetModel getAssetModelByMeid(String imei);

	List<JGateway> checkDeviceExist(String meid);

	JQrcDetails getQrcDetails(String qrc);

	public boolean checkQrcRegistered(String qrcode);

	public boolean assignGatewayToLogin(String gatewayId, long return_login_user_id);

	long getGatewayId(String meid);

	public boolean  isUserRegistered(String qrc);
		
    public JQrcDetails  getOldModelNameandModelId(String deviceModelNo);

	public  long getOtpByEmail(String email);

	public boolean createOrResetGatewayStatus(long gateway_id, String meid, String serial_number,long mtype,int recall);

	public List<String> getDistinctProductCategory();

	public JGatewayToAlexa getJGatewayToAlexa(String user_id);

	public GatewayStatus getGatewayStatusByMeid(String meid);
	
	public PetProfile getPetprofile(long id, long gatewayid, String name, String age, String sex, String breed);

	public boolean updateGatewayNameByPetId(String name, long profileId);
	
	public double getPetFoodCaloriesByProfileId(long profileId);
	
	public List<JAlertRangeType> getJAlertRangeType(String string);
	
	public boolean delGatewayStatus(long gateway_id);
	
	public boolean delGatewayProfile(long gateway_id);

	public boolean delGateway(long gateway_id);
	
	public List<JMeariNotification> getMeariNotification();
	
	public boolean delAsset(String meid);

	JResponse getInvalidPacket(String otype, long offset, long limit, String from_date, String to_date,String type,
			JResponse response);

	JResponse getInvalidPacketExport(String from_date, String to_date, String type, JResponse response);

	Map getInvalidFieldMeidCount(String from_date, String to_date);


	boolean updateskipotastatus(String meid, boolean status);

	boolean getSkipOtaStatus(String meid);

	AssetModel getAssetModelByGatewayId(long gateway_id);
	
	boolean updateuseremaildetails(String from_email, String to_email);
	
	public JGatewaySensorType getSensorDetailByGatewayId(long gateway_id);
	
	public void updateRecallAsset(Asset asset) throws InvalidAsseIdException;
	
	public boolean updateGatewayFotoversion(String verVal, String gatewayId);

	double[] getDeviceLatLon(long gateway_id);
	
	public boolean createSolarcamSubscription(long gateway_id, long userid, long mType);
	public JGateway getJGatewayByGateway(long gatewaid);
	
	public Gateway getActiveGatewayByid(long id);
	
	public long getUserIdByGatewayId(long gateway_id);
	
	public PlanToPeriod getPlanAndPeriodId(String planName);

	List<JPacketReport> getPacketReport(String meid, String startTime, int seconds);

        public boolean delGatewayAllProdSub(long gateway_id);

	List<JGateway> getGatewayV1(String assetgroupid, String groupid, String subgroupid, String gatewayid,
								long userid, String meid,String chargebeeId);

	boolean enableOrDisableBtUpdate(String gatewayId, int bleEnable);

	boolean enableOrDisableFotaVersionMapping(String fotaVersionId, int enable, String currVesion);

	public boolean minicamaddresavilable(long userId);

	public String getUsernameByQrc(String qrcode);

	public boolean updateGatewaySkip(boolean isSkip,long gatewayId);

	boolean saveWarrantyClaimType(String claimType, long gatewayId);

	boolean checkFotaVersionAvailable(long gatewayId, String meid);

	boolean checkFreeplanavilinmearidevice(long gatewayId, long userId);

	boolean enableOrDisableMeariUpdate(String gatewayId, int btnEnable);

	void updatePurchaseMonthYear(long userId, long gatewayId, String purchasedMonthYear);

	String getNewQrc(String oldQrc);

}
