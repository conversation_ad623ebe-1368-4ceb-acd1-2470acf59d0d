package com.nimble.irisservices.service.impl;

import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.InvalidPacket;
import com.nimble.irisservices.dto.JAlertRangeType;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JGatewaySensorType;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JMeariNotification;
import com.nimble.irisservices.dto.JPacketReport;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FurbitLastGatewayReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayCredits;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.exception.InvalidSubgroupIdException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IMonitorTypeService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IWifiInfoService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class GatewayServiceImpl implements IGatewayService {

	private static final Logger log = LogManager.getLogger(GatewayServiceImpl.class);

	@Autowired
	IGatewayDao gatewayDao;

	@Autowired
	IMessagingService messagingService;

	@Autowired
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IAlertService alertService;

	@Autowired
	@Lazy
	IAlertCfgService alertcfgService;

	@Autowired
	@Lazy
	IDynamicCmdService dynamiccmdService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;

	@Autowired
	IMonitorTypeService monitorTypeService;

	@Autowired
	@Lazy
	private IWifiInfoService wifiService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	Helper _helper;

	@Transactional
	@Override
	public List<JGateway> getGateway(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		return gatewayDao.getJGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);
	}

	@Transactional
	@Override
	public List<JGatewayConfig> getJGatewayConfig(String gatewayid, long userid, String tempunit) {
		return gatewayDao.getJGatewayConfig(gatewayid, userid, tempunit);
	}

	@Transactional
	public List<JSGateway> getGateways(String cmptype_id) {
		return gatewayDao.getJSGateways(cmptype_id);
	}

	@Transactional
	public JGatewayOverview getgatewayoverview(String groupid, String subgroupid, long userid, String levelid) {

		JGatewayOverview gatewayOverview = gatewayDao.getGatewayCount(groupid, subgroupid, userid, levelid);
		return gatewayOverview;
	}

	@Transactional
	public boolean getSkipOtaStatus(String meid) {

		return gatewayDao.getSkipOtaStatus(meid);
	}

	@Transactional
	public boolean updateskipotastatus(String meid, boolean status) {

		return gatewayDao.updateskipotastatus(meid, status);
	}

	
	@Transactional
	public Gateway saveORupdateGateway(JGateway gateway, long cmpid)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException {

		return gatewayDao.saveORupdateGateway(gateway, cmpid);
	}

	@Transactional
	public JSreiGatewayOverview getSreiGatewayOverview(String groupid, String subgroupid, long userid, String levelid) {

		JSreiGatewayOverview sgateGatewayOverview = gatewayDao.getSreiGatewayCount(groupid, subgroupid, userid, -1,
				levelid, -1);
		return sgateGatewayOverview;
	}

	@Transactional
	public JSreiGatewayOverview getSreiRGatewayOverview(String groupid, String subgroupid, long userid,
			String levelid) {

		JSreiGatewayOverview sgateRGatewayOverview = gatewayDao.getSreiGatewayCount(groupid, subgroupid, userid, 1,
				levelid, 0);
		return sgateRGatewayOverview;
	}

	@Transactional
	public JSreiGatewayOverview getSreiNRGatewayOverview(String groupid, String subgroupid, long userid,
			String levelid) {

		JSreiGatewayOverview sgateNRGatewayOverview = gatewayDao.getSreiGatewayCount(groupid, subgroupid, userid, 0,
				levelid, 0);
		return sgateNRGatewayOverview;
	}

	@Transactional
	public JSreiGatewayOverview getSreiInbuiltDevicesOverview(String groupid, String subgroupid, long userid,
			String levelid) {

		JSreiGatewayOverview sgateNRGatewayOverview = gatewayDao.getSreiGatewayCount(groupid, subgroupid, userid, -1,
				levelid, 1);
		return sgateNRGatewayOverview;
	}

	@Transactional
	public JGateway gatewayExitsinDB(JGateway jgateway, long cmpid) {
		return gatewayDao.gatewayExitsinDB(jgateway, cmpid);
	}

	@Transactional
	public boolean delGateway(User user, String assetid) {
		boolean result = false;
		try {
			userService.delUserGateway(user.giveCompany().getId(), assetid);

			userService.updateMeidInOrderMap(user.getId() + "",
					gatewayService.getGateway(Long.parseLong(assetid)).getMeid());

			alertService.delAlert(user.getId(), assetid);
			alertcfgService.delAlertcfg(user.getId(), assetid);

			// update gateway name
			gatewayService.updateGatewayName(assetid);

			// included : delete - last powersavemode, powersavemode history details.
			dynamiccmdService.delDynamicCmd(user.getId(), assetid);
			gatewayService.updatepetprofile(assetid);

			// new : delete wifi info for gateway.
			wifiService.deleteWifiInfoForGateway(user.getId() + "", assetid);

			Gateway gateway = gatewayService.getGatewayByid(Long.parseLong(assetid));

			// delete entry in device_replaced table
			boolean isRemovedInDeviceReplaced = gatewayServiceV4.removeNotComplitedDeviceReplaced(user.getId(),
					gateway.getMeid());
			log.info(" Gateway deleted in device_replaced table : " + isRemovedInDeviceReplaced);

			boolean gatewayst = gatewayService.delGatewayStatus(Long.parseLong(assetid));
			log.info(" delGatewayStatus :" + assetid + " : " + gatewayst);

			boolean gprofile = gatewayService.delGatewayProfile(Long.parseLong(assetid));
			log.info(" delGatewayProfile :" + assetid + " : " + gprofile);

			//gateway - 0 in all product subscription table
			boolean gpAllprod = gatewayService.delGatewayAllProdSub(Long.parseLong(assetid));
			log.info(" delGatewayAllProdSub :" + assetid + " : " + gprofile);

			boolean gpFeature =gatewayServiceV4.delGatewayFeature(Long.parseLong(assetid));
			log.info(" delGatewayFeature :" + assetid + " : " + gpFeature);

			result = true;
		} catch (Exception e) {
			log.error("Error while deleting gateway details : " + e.getLocalizedMessage());
			return false;
		}
		return result;
	}

	@Transactional
	public GatewayCredits updateGatewayCredit(long gateway_id, long cmp_id) {
		return gatewayDao.updateGatewayCredit(gateway_id, cmp_id);
	}

	@Transactional
	public boolean updateGatewayCreditPoints(ThrottlingSettings throtSettings) {
		return gatewayDao.updateGatewayCreditPoints(throtSettings);
	}

	@Transactional
	public int saveorupdatePetprofile(List<JPetprofile> jpetprofiles, long userid) {
		return gatewayDao.saveORupdatePetprofile(jpetprofiles, userid);

	}

	@Transactional
	public List<JPetprofile> getJPetprofiles(long userid, long id, long gatewayid, String name, String age, String sex,
			String breed) {
		return gatewayDao.getJPetprofiles(userid, id, gatewayid, name, age, sex, breed);
	}

	@Transactional
	public JGatewayUserDetails getGateway(String meid) {
		return gatewayDao.getGateway(meid);
	}

	@Override
	@Transactional
	public boolean updateGatewayName(String gatewayName, String gatewayId) {
		return gatewayDao.updateGatewayName(gatewayName, gatewayId);
	}

	@Transactional
	public Gateway getGateway(long id) {
		return gatewayDao.getGateway(id);
	}

	@Override
	@Transactional
	public Gateway getGatewayDetails(String meid) {
		return gatewayDao.getGatewayInMeid(meid);
	}

	@Override
	@Transactional
	public Gateway saveORupdateQRCGateway(JGateway gateway, long cmpid)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException {
		return gatewayDao.saveORupdateQRCGateway(gateway, cmpid);
	}

	@Override
	@Transactional
	public boolean updatepetprofile(String gatewayId) {
		return gatewayDao.updatepetprofile(gatewayId);
	}

	@Override
	@Transactional
	public ProbeCategory getProbeCategory(long model_id) {
		return gatewayDao.getProbeCategory(model_id);
	}

	@Override
	@Transactional
	public boolean gatewayOnOff(String gatewayId, boolean isEnable, String message) {
		return gatewayDao.gatewayOnOff(gatewayId, isEnable, message);
	}

	@Override
	@Transactional
	public boolean enableOrDisableGateway(String gatewayId, String userId, boolean isEnable) {
		return gatewayDao.enableOrDisableGateway(gatewayId, userId, isEnable);
	}

	@Override
	@Transactional
	public int updateGoalSetting(String gatewayId, String goal) {
		return gatewayDao.updateGoalSetting(gatewayId, goal);
	}

	@Override
	@Transactional
	public int updateCaloriesGoalSetting(String gatewayId, String goal) {
		return gatewayDao.updateCaloriesGoalSetting(gatewayId, goal);
	}

	@Override
	@Transactional
	public JResponse getJPetprofilesByUser(long userid, long gatewayid, int monitortype) {
		return gatewayDao.getJPetprofilesByUser(userid, gatewayid, monitortype);
	}

	@Override
	@Transactional
	public List<Gateway> getGatewayByMonitorType(String monitortype, long userid) {
		return gatewayDao.getGatewayByMonitorType(monitortype, userid);
	}

	@Override
	@Transactional
	public List<Gateway> getGatewayByUser(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		return gatewayDao.getGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);
	}

	@Transactional
	public PetProfile getPetProfile(long gateway_id) {
		return gatewayDao.getPetProfile(gateway_id);
	}

	@Override
	@Transactional
	public JGatewayUserDetails getGatewayAndUserDetails(String meid) {
		// TODO Auto-generated method stub
		return gatewayDao.getGatewayAndUserDetails(meid);
	}

	@Transactional
	public List<FurbitLastGatewayReport> getFLastGatewayReportByUser(long userid) {
		return gatewayDao.getFLastGatewayReportByUser(userid);
	}

	@Transactional
	public ArrayList<JGatewayConfig> getGatewayConfig(long userid) {
		return gatewayDao.getGatewayConfig(userid);
	}

	@Transactional
	public boolean updatePetProfile(PetProfile petProfile) {
		return gatewayDao.updatePetProfile(petProfile);
	}

	@Transactional
	public AssetModel getAssetModelByName(String inventorymodelname) {
		return gatewayDao.getAssetModelByName(inventorymodelname);
	}

	@Transactional
	public boolean checkQrcExist(long id, String qrcode) {
		return gatewayDao.checkQrcExist(id, qrcode);
	}

	@Transactional
	public int updateGateway(Gateway gateway) {
		return gatewayDao.updateGateway(gateway);
	}

	@Transactional
	public Gateway getGatewayByid(long id) {
		return gatewayDao.getGatewayByid(id);
	}

	@Transactional
	public JGateway getJGateway(String columnname, String value) {
		return gatewayDao.getJGateway(columnname, value);
	}

	@Transactional
	public List<JGateway> getJGatewayByUser(long userid, String monitor_type) {
		return gatewayDao.getJGatewayByUser(userid, monitor_type);
	}

	@Transactional
	public AssetModel getAssetModel(long id) {
		return gatewayDao.getAssetModel(id);
	}

	@Transactional
	public Date getBirthDate(String calendarValue, String age) {
		return gatewayDao.getBirthDate(calendarValue, age);
	}

	@Transactional
	public List<JGatewayForPet> getNotMappedGateway(long userid) {
		return gatewayDao.getNotMappedGateway(userid);
	}

	@Transactional
	public long getOrderchannelid(String orderchannel) {
		return gatewayDao.getOrderchannelid(orderchannel);
	}

//	@Transactional
//	public int saveOrderchannelDetail(long user_id,long gateway_id,long orderchannel) {
//		return gatewayDao.saveOrderchannelDetail(user_id, gateway_id, orderchannel);
//	}

	public JGatewayUserDetails getGatewayByMAC(String macid, long user_id) {
		return gatewayDao.getGatewayByMAC(macid, user_id);
	}

	public boolean updateProfileImgPath(long ppid, String imgpath) {
		return gatewayDao.updateProfileImgPath(ppid, imgpath);
	}

	public int getDeviceCount(long userid) {
		return gatewayDao.getDeviceCount(userid);
	}

	@Override
	public boolean enableOrDisablePetProfile(long userId, List<EnableOrDisablePetProfile> enableOrDisablePetPrfList) {
		return gatewayDao.enableOrDisablePetProfile(userId, enableOrDisablePetPrfList);
	}

	@Override
	public boolean updateGatewayName(String gatewayid) {
		return gatewayDao.updateGatewayName(gatewayid);
	}

	@Override
	public List<Gateway> getGateways(String assetgroupid, String groupid, String subgroupid, String gatewayid,
			long userid, String meid) {
		return gatewayDao.getGateway(assetgroupid, groupid, subgroupid, gatewayid, userid, meid);
	}

	@Override
	public boolean changeGatewayOrderidStatus(long id, boolean pFromStatus) {
		return gatewayDao.changeGatewayOrderidStatus(id, pFromStatus);
	}

	@Override
	public Gateway saveORupdateRecallGateway(JGateway jgateway, long id)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException {
		return gatewayDao.saveORupdateRecallGateway(jgateway, id);
	}

	@Override
	public String changeOldGatewayDetails(Gateway exGatewayInDB) {
		try {
			System.out.println("");
			List<String> meidList = gatewayDao.getMeidsList(exGatewayInDB);

			if (meidList == null) {
				return exGatewayInDB.getMeid();
			}

			int itr = 0;
			if (meidList.size() > 1) {
				itr = 1;
			}

			char currentChar = meidList.get(itr).charAt(0);
			char replaceableChar = 'z';

			if (currentChar <= 'z' && currentChar >= 'a') {
				replaceableChar = (char) (currentChar - 1);
			}

			String meid = changeFirstCharToZ(replaceableChar, exGatewayInDB.getMeid());
			String qrc = changeFirstCharToZ(replaceableChar, exGatewayInDB.getQrcode());
			String macId = changeFirstCharToZ(replaceableChar, exGatewayInDB.getMacid());

			exGatewayInDB.setMeid(meid);
			exGatewayInDB.setQrcode(qrc);
			exGatewayInDB.setMacid(macId);

			exGatewayInDB.giveAsset().setAssetaddress(meid);
			gatewayDao.saveORupdateGateway(exGatewayInDB);

			return meid;
		} catch (Exception e) {
			e.getLocalizedMessage();
			return "NA";
		}

	}

	private String changeFirstCharToZ(char replaceableChar, String rawString) {
		String changedStr = replaceableChar + (rawString.substring(1, rawString.toString().length()));
		return changedStr;
	}

	@Override
	public boolean changeOldAssertDetails(long id, String meid) {
		return gatewayDao.updateAssetMeid(id, meid);
	}

	@Override
	public boolean saveRecallGateway(Gateway gateway) {
		return gatewayDao.saveRecallGateway(gateway);
	}

	@Override
	public boolean changeDefalutRptInLastGateway(long gatewayId, String currentTimeUTC, int defaultRpt) {
		return gatewayDao.changeDefalutRptInLastGateway(gatewayId, currentTimeUTC, defaultRpt);
	}

	@Override
	public Gateway saveORupdateReturnGateway(JGateway jgateway, long id)
			throws ConstraintViolationException, InvalidAssetGroupIdException, InvalidSubgroupIdException,
			InvalidModelIdException, InvalidAsseIdException, InvalidGroupIdException, DataIntegrityViolationException {
		return gatewayDao.saveORupdateReturnGateway(jgateway, id);
	}

	@Override
	public Gateway saveReturnGateway(Gateway gateway) {
		return gatewayDao.saveReturnGateway(gateway);
	}

	@Override
	public String changeOldAssertDetailsToZ(Asset asset) {

		List<String> assetAddList = gatewayService.getOldAssertList(asset);

		if (assetAddList == null) {
			return asset.getAssetaddress();
		}

		int itr = 0;
		if (assetAddList.size() > 1) {
			itr = 1;
		}

		char currentChar = assetAddList.get(itr).charAt(0);
		char replaceableChar = 'z';

		if (currentChar <= 'z' && currentChar >= 'a') {
			replaceableChar = (char) (currentChar - 1);
		}

		return changeFirstCharToZ(replaceableChar, asset.getAssetaddress());
	}

	@Override
	public List<String> getOldAssertList(Asset asset) {
		return gatewayDao.getOldAssertList(asset);
	}

	@Override
	public boolean changeOldAssertDetailsByAddr(String meid, String changedMeid) {
		return gatewayDao.changeOldAssertDetailsByAddr(meid, changedMeid);
	}

	@Override
	public Asset getAssetById(long id) {
		try {
			return gatewayDao.getAssetById(id);
		} catch (Exception e) {
			return null;
		}
	}

	@Override
	public boolean updateAsset(Asset asset) {
		try {
			gatewayDao.updateAsset(asset);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	@Override
	public AssetModel getAssetModelByMeid(String imei) {
		return gatewayDao.getAssetModelByMeid(imei);
	}

	@Override
	public List<JGateway> checkDeviceExist(String meid) {
		return gatewayDao.checkDeviceExist(meid);
	}

	@Override
	public JQrcDetails getQrcDetails(String qrc) {
		return gatewayDao.getQrcDetails(qrc);
	}

	@Override
	public boolean checkQrcRegistered(String qrcode) {
		return gatewayDao.checkQrcRegistered(qrcode);
	}

	public boolean assignGatewayToLogin(String gid, long user_id) {
		return gatewayDao.assignGatewayToLogin(gid, user_id);

	}

	@Override
	public long getGatewayId(String meid) {
		return gatewayDao.getGatewayId(meid);
	}

	@Override
	public boolean isUserRegistered(String qrc) {
		return gatewayDao.isUserRegistered(qrc);
	}

	@Override
	public JQrcDetails getOldModelNameandModelId(String deviceModelNo) {
		return gatewayDao.getOldModelNameandModelId(deviceModelNo);
	}

	@Override
	public long getOtpByEmail(String email) {
		return gatewayDao.getOtpByEmail(email);
	}

	@Override
	public boolean createOrResetGatewayStatus(long gateway_id, String meid, String serial_number,long mType,int isRecall) {
		return gatewayDao.createOrResetGatewayStatus(gateway_id, meid, serial_number,mType,isRecall);
	}

	@Override
	public List<String> getDistinctProductCategory() {
		return gatewayDao.getDistinctProductCategory();
	}

	@Override
	public JGatewayToAlexa getJGatewayToAlexa(String user_id) {
		return gatewayDao.getJGatewayToAlexa(user_id);
	}

	@Override
	public GatewayStatus getGatewayStatusByMeid(String meid) {
		return gatewayDao.getGatewayStatusByMeid(meid);
	}

	@Override
	public PetProfile getPetprofile(long id, long gatewayid, String name, String age, String sex, String breed) {
		return gatewayDao.getPetprofile(id, gatewayid, name, age, sex, breed);
	}

	@Override
	public boolean updateGatewayNameByPetId(String name, long profileId) {
		return gatewayDao.updateGatewayNameByPetId(name, profileId);
	}

	@Override
	public double getPetFoodCaloriesByProfileId(long profileId) {
		return gatewayDao.getPetFoodCaloriesByProfileId(profileId);
	}

	public List<JAlertRangeType> getJAlertRangeType(String userid) {
		return gatewayDao.getJAlertRangeType(userid);
	}

	@Override
	public boolean delGatewayStatus(long gateway_id) {
		return gatewayDao.delGatewayStatus(gateway_id);
	}

	@Override
	public boolean delGatewayProfile(long gateway_id) {
		return gatewayDao.delGatewayProfile(gateway_id);
	}

	@Override
	public boolean delGateway(long gateway_id) {
		return gatewayDao.delGateway(gateway_id);
	}

	@Override
	public List<JMeariNotification> getMeariNotification() {
		return gatewayDao.getMeariNotification();
	}

	@Override
	public boolean delAsset(String meid) {
		return gatewayDao.delAsset(meid);
	}

	@Override
	public JResponse getInvalidPacket(String otype, long offset, long limit, String from_date, String to_date,
			String type, JResponse response) {
		return gatewayDao.getInvalidPacket(otype, offset, limit, from_date, to_date, type, response);
	}

	@Override
	public JResponse getInvalidPacketExport(String from_date, String to_date, String type, JResponse response) {
		ArrayList<InvalidPacket> packet = gatewayDao.getInvalidPacketExport(from_date, to_date, type);
		String fileLink = "NA";
		if (packet != null && !packet.isEmpty()) {
			fileLink = writeInvalidPacket(packet);
			if (fileLink != null && !fileLink.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("File", fileLink);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Success");
			}
		} else {
			response.put("Status", 0);
			response.put("Msg", "Success");
		}
		return response;
	}

	private String writeInvalidPacket(ArrayList<InvalidPacket> packet) {
		log.info("Entered in writeInvalidPacket.");
		String filename = "InvalidPacket_" + Calendar.getInstance().getTimeInMillis() + ".xls";
		File file = new File(filename);
		try {
			if (file.exists())
				file.delete();
			file.createNewFile();
		} catch (IOException e1) {
			log.error("Exception in writeRevRec : " + e1.getMessage());
		}

		String headers[] = { "  S.No   ", "Packet", "Type", "MEID", "Ip Address", "Created On" };

		try {
			FileOutputStream fileOs = new FileOutputStream(filename);
			HSSFWorkbook workbook = new HSSFWorkbook();
			HSSFSheet deviceSheet = workbook.createSheet(filename);

			CellStyle headStyle = _helper.getHeadersStyle(workbook);
			headStyle.setBorderRight(HSSFCellStyle.BORDER_MEDIUM);

			_helper.createHeadersRow_2(workbook, deviceSheet, headers);

			for (int columnSize = 0; columnSize <= headers.length; columnSize++) {
				deviceSheet.autoSizeColumn(columnSize);
			}

			int row = 1;
			for (InvalidPacket obj : packet) {
				int add = 0;

				try {
					HSSFRow dataRow = deviceSheet.createRow(row++);

					dataRow.createCell(add++).setCellValue(row - 1);
					dataRow.createCell(add++).setCellValue(obj.getPacket());
					dataRow.createCell(add++).setCellValue(obj.getType());
					dataRow.createCell(add++).setCellValue(obj.getMeid());
					dataRow.createCell(add++).setCellValue(obj.getIp_address());
					dataRow.createCell(add++).setCellValue(obj.getCreated_on());

					dataRow.setHeightInPoints(20);
				} catch (Exception e) {
					log.error("Error in sheet writing : " + e.getMessage());
				}
			}

			workbook.write(fileOs);
			fileOs.close();

		} catch (FileNotFoundException e) {
			log.error("File Error in write count revenue : " + e.getMessage());
		} catch (Exception e) {
			log.error("Exception in write count revenue : " + e.getMessage());
		}
		String file_link = _helper.uploadToS3bucket(filename);
		file.delete();
		return file_link;
	}

	@Override
	public Map getInvalidFieldMeidCount(String from_date, String to_date) {
		return gatewayDao.getInvalidFieldMeidCount(from_date, to_date);
	}

	@Override
	public AssetModel getAssetModelByGatewayId(long gateway_id) {
		return gatewayDao.getAssetModelByGatewayId(gateway_id);
	}
	
	@Override
	public boolean updateuseremaildetails(String from_email, String to_email) {
		return gatewayDao.updateuseremaildetails(from_email,to_email);
	}
	
	@Override
	public JGatewaySensorType getSensorDetailByGatewayId(long gateway_id) {
		return gatewayDao.getSensorDetailByGatewayId(gateway_id);
	}

	@Override
	public void updateRecallAsset(Asset asset) throws InvalidAsseIdException {
		gatewayDao.updateRecallAsset(asset);
	}
	
	@Override
	public boolean updateGatewayFotoversion(String verVal, String gatewayId) {
		return gatewayDao.updateGatewayFotoversion(verVal,gatewayId);
	}

	@Override
	public double[] getDeviceLatLon(long gateway_id) {
		return gatewayDao.getDeviceLatLon(gateway_id);
	}
	
	@Override
	public boolean createSolarcamSubscription(long gateway_id, long userid,long mType) {
		return gatewayDao.createSolarcamSubscription(gateway_id,userid,mType);
	}

	@Override
	public JGateway getJGatewayByGateway(long gatewaid) {
		return gatewayDao.getJGatewayByGateway(gatewaid);
	}
	
	@Override
	public Gateway getActiveGatewayByid(long id) {
		return gatewayDao.getActiveGatewayByid(id);
	}
	
	@Override
	public long getUserIdByGatewayId(long gateway_id) {
		return gatewayDao.getUserIdByGatewayId(gateway_id);
	}
	
	@Override
	public PlanToPeriod getPlanAndPeriodId(String planName) {
		return gatewayDao.getPlanAndPeriodId(planName);
	}

	@Override
	public List<JPacketReport> getPacketReport(String meid, String startTime, int seconds) {

		return gatewayDao.getPacketReport(meid, startTime, seconds);
	}

        @Override
	public boolean delGatewayAllProdSub(long gateway_id) {
		return gatewayDao.delGatewayAllProdSub(gateway_id);
	}

	@Override
	public List<JGateway> getGatewayV1(String assetgroupid, String groupid, String subgroupid, String gatewayid,
								long userid, String meid,String chargebeeId) {
		return gatewayDao.getJGatewayV1(assetgroupid, groupid, subgroupid, gatewayid, userid, meid, chargebeeId);
	}

	@Override
	public boolean enableOrDisableBtUpdate(String gatewayId, int bleEnable) {

		boolean result = gatewayDao.enableOrDisableBtUpdate(gatewayId, bleEnable);

		if(result && bleEnable == 1) {
			gatewayDao.updatePasscode(gatewayId);
		}

		return result;
	}

	@Override
	public boolean enableOrDisableFotaVersionMapping(String fotaVersionId, int enable, String currVersion) {

		return gatewayDao.enableOrDisableFotaVersionMapping(fotaVersionId, enable, currVersion);
	}

	@Override
	public boolean minicamaddresavilable(long userId) {
		return gatewayDao.minicamaddresavilable(userId);
	}

	@Override
	public String getUsernameByQrc(String qrcode) {
		return gatewayDao.getUsernameByQrc(qrcode);
	}

	@Override
	public boolean updateGatewaySkip(boolean isSkip,long gatewayId) {
		return gatewayDao.updateGatewaySkip(isSkip,gatewayId);
	}

	@Override
	public boolean saveWarrantyClaimType(String claimType, long gatewayId) {

		return gatewayDao.saveWarrantyClaimType(claimType, gatewayId);
	}

	@Override
	public boolean checkFotaVersionAvailable(long gatewayId, String meid) {

		return gatewayDao.checkFotaVersionAvailable(gatewayId, meid);
	}

	@Override
	public boolean checkFreeplanavilinmearidevice(long gatewayId, long userId) {
		return gatewayDao.checkFreeplanavilinmearidevice(gatewayId, userId);
	}

	@Override
	public boolean enableOrDisableMeariUpdate(String gatewayId, int btnEnable) {
		return gatewayDao.enableOrDisableMeariUpdate(gatewayId, btnEnable);
	}
	@Override
	public void updatePurchaseMonthYear(long userId, long gatewayId, String purchasedMonthYear){

		gatewayDao.updatePurchaseMonthYear(userId,gatewayId,purchasedMonthYear);
	}

	@Override
	public String getNewQrc(String oldQrc) {

		return gatewayDao.getNewQrc(oldQrc);
	}
}
